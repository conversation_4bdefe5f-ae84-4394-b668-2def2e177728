import './style.scss';
import { dashboard, DashboardState, SourceType, Rollup, bitable, IDataCondition, GroupMode, DATA_SOURCE_SORT_TYPE, ORDER } from "@lark-base-open/js-sdk";
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, Input, Button, Form, Switch, Tabs, TabPane } from '@douyinfe/semi-ui';
import { Item } from '../Item';
import { 
  useIndicatorData, 
  useTableList, 
  useDataRange,
  useFieldList, 
  ISelectOption, 
  IIndicatorConfig, 
  IIndicatorData,
  getConfigModeData,
  getQueryConfig,
  getViewModeData,
} from './useIndicatorData';

export default function IndicatorCard(props: { bgColor: string }) {
  // 初始化国际化语言配置
  const { t } = useTranslation();
  console.log('IndicatorCard 国际化语言组件初始化');

  // 定义配置内容项
  const [config, setConfig] = useState<IIndicatorConfig>({
    tableId: '',  // 数据表ID
    tableName: '', // 数据表名
    rangeType: '',
    rangeName: '',
    groupField: '',
    groupFieldName: '',
    targetField: '',
    targetFieldName: '',
    compareField: '',
    compareFieldName:'',
    // 添加平均值字段
    averageField: '',
    averageFieldName: '',
    // 显示控制选项
    showTitle: true,
    showPercentage: true,
    showAverage: true,
    showTotalCount: true,
    // 数值单位选择
    targetUnit: '个',
    totalCountUnit: '个',
    averageUnit: '个',
    // 数值正负值选择
    showTargetAsPositive: true,
    showTotalAsPositive: true,
    showAverageAsPositive: true,
    showPercentageAsPositive: true,
    
    title: t('indicator.default.title'),
  });

   // 获取表格列表
   const tables = useTableList();
   console.log('获取到的表格列表:', tables); 

  // 获取数据范围
  const ranges = useDataRange(config.tableId);
  console.log('获取到的数据范围:', ranges);

  // 获取字段列表
  const fields = useFieldList(config.tableId);
  console.log('获取到的字段列表:', fields);

  // 获取指标数据
  const data = useIndicatorData(config);
  console.log('获取到的指标数据:', data);
  
  // 添加选择数据表后的处理函数
  const handleTableChange = (value: string) => {
    // 获取表格信息
    console.log('表格选择变更:', value);
    try {
      const { id, name } = JSON.parse(value);
      setConfig({ 
        ...config, 
        tableId: id, 
        tableName: name,
        // 清空相关字段
        rangeType: '',
        rangeName: '',
        targetField: '',
        targetFieldName: '',
        compareField: '',
        compareFieldName: '',
        averageField: '',
        averageFieldName: ''
      });
    } catch (error) {
      console.error('表格选项存储错误:', error);
    }
  };

  // 修改 handleRangeChange 函数
  const handleRangeChange = (value: string) => {
    console.log('数据范围选择变更:', value);
    try {
      const { type } = JSON.parse(value);
      const rangeName = type === 'ALL' ? '全部数据' : '视图数据';
      setConfig({ 
        ...config, 
        rangeType: type,
        rangeName
      });
      console.log('数据范围选择变更后的配置:', {...config, rangeType: type, rangeName });
    } catch (error) {
      console.error('数据范围存储错误:', error);
    }
  };
  
  // 修改 handleGroupChange 函数
  const handleGroupChange = (value: string) => {
    try {
      console.log('数据分组字段选择变更:', value);
      const { id, name } = JSON.parse(value);
      console.log('解析后的字段ID:', id, '字段名称:', name);
      
      setConfig({ 
        ...config, 
        groupField: id,
        groupFieldName: name
      });
      console.log('数据分组字段选择变更后的配置:', {...config, groupField: id, groupFieldName: name });
    } catch (error) {
      console.error('数据分组字段存储错误:', error);
    }
  };

  // 添加选择目标字段后的处理函数
  const handleTargetFieldChange = (value: string) => {
    try {
      console.log('目标字段选择变更, 原始值:', value);
      const { id, name } = JSON.parse(value);
      console.log('解析后的字段ID:', id, '字段名称:', name);
      
      setConfig({...config, targetField: id, targetFieldName: name, title: name});
      console.log('目标字段变更后的配置:', { ...config, targetField: id, targetFieldName: name, title: name });
    } catch (error) {
      console.error('Failed to parse target field value:', error);
    }
  };
  
  // 添加选择比较字段后的处理函数
  const handleCompareFieldChange = (value: string) => {
    try {
      console.log('比较字段选择变更, 原始值:', value);
      const { id, name } = JSON.parse(value);
      console.log('解析后的字段ID:', id, '字段名称:', name);
      
      setConfig({ 
        ...config, 
        compareField: id,
        compareFieldName: name
      });
      console.log('比较字段变更后的配置:', { ...config, compareField: id, compareFieldName: name });
    } catch (error) {
      console.error('Failed to parse compare field value:', error);
    }
  };

  // 添加选择平均值计算字段的处理函数
  const handleAverageFieldChange = (value: string) => {
    try {
      console.log('平均值计算字段选择变更, 原始值:', value);
      const { id, name } = JSON.parse(value);
      console.log('解析后的字段ID:', id, '字段名称:', name);
      
      setConfig({ 
        ...config, 
        averageField: id,
        averageFieldName: name
      });
      console.log('平均值计算字段变更后的配置:', { ...config, averageField: id, averageFieldName: name });
    } catch (error) {
      console.error('平均值计算字段存储错误:', error);
    }
  };

  // 添加显示选项切换处理函数
  const handleShowTitleChange = (checked: boolean) => {
    setConfig({ ...config, showTitle: checked });
  };

  const handleShowPercentageChange = (checked: boolean) => {
    setConfig({ ...config, showPercentage: checked });
  };

  const handleShowAverageChange = (checked: boolean) => {
    setConfig({ ...config, showAverage: checked });
  };
  
  const handleShowTotalCountChange = (checked: boolean) => {
    setConfig({ ...config, showTotalCount: checked });
  };

  // 处理单位选择变更
  const handleTargetUnitChange = (value: string | number | any[] | Record<string, any> | undefined) => {
    if (typeof value === 'string') {
      setConfig({ ...config, targetUnit: value });
    }
  };
  
  const handleTotalCountUnitChange = (value: string | number | any[] | Record<string, any> | undefined) => {
    if (typeof value === 'string') {
      setConfig({ ...config, totalCountUnit: value });
    }
  };
  
  const handleAverageUnitChange = (value: string | number | any[] | Record<string, any> | undefined) => {
    if (typeof value === 'string') {
      setConfig({ ...config, averageUnit: value });
    }
  };
  
  // 处理正负值显示选择
  const handleTargetAsPositiveChange = (checked: boolean) => {
    setConfig({ ...config, showTargetAsPositive: checked });
  };
  
  const handleTotalAsPositiveChange = (checked: boolean) => {
    setConfig({ ...config, showTotalAsPositive: checked });
  };
  
  const handleAverageAsPositiveChange = (checked: boolean) => {
    setConfig({ ...config, showAverageAsPositive: checked });
  };

  const handlePercentageAsPositiveChange = (checked: boolean) => {
    setConfig({ ...config, showPercentageAsPositive: checked });
  };

  const handleTitleChange = (value: string) => {
    console.log('标题变更:', value);
    setConfig({ ...config, title: value });
  };

  // 添加保存配置的处理函数
  const handleSave = async () => {
    try {
      console.log('开始保存配置...');
      // 保存配置
      const result = await dashboard.saveConfig({
        customConfig: config as unknown as Record<string, unknown>,
        dataConditions: [getQueryConfig(config)],
      });
      console.log('配置保存成功:', result, '当前状态:', dashboard.state);

      await dashboard.setRendered();
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  // Check if in config mode
  const isConfig = dashboard.state === DashboardState.Config 
                  || dashboard.state === DashboardState.Create;
  console.log('是否处于配置模式:', isConfig);

  // 视图模式下的处理函数
  useEffect(() => {
    if (dashboard.state !== DashboardState.View) {
      return;
    }

    console.log('视图模式下的处理函数开始执行');

    // 获取配置
    dashboard.getConfig().then(dashbordConfig => {
      // 获取配置内容
      console.log('视图模式下获取到的配置内容:', dashbordConfig);
      // 解析自定义配置并更新
      const { customConfig, dataConditions } = dashbordConfig;
      setConfig(customConfig as unknown as IIndicatorConfig);
      console.log('视图模式下更新后的配置内容:', config);  // 更新配置后会自动触发数据更新
    })
  }, [dashboard.state]);

  // 配置模式下的处理函数
  useEffect(() => {
    if (dashboard.state!== DashboardState.Config && dashboard.state!== DashboardState.Create) {
      return;      
    }
    console.log('配置模式下的处理函数开始执行');

    // 获取配置
    dashboard.getConfig().then(config => {
       // 获取配置内容
       console.log('获取到的配置内容:', config);
       const { customConfig, dataConditions } = config;
       // 解析自定义配置并更新
       const indicatorConfig = customConfig as unknown as IIndicatorConfig;
       setConfig({ ...indicatorConfig });
       console.log('更新后的配置内容:', config);
    })
  }, [dashboard.state]);

  return (
    <div className="indicator-wrapper" style={{ backgroundColor: props.bgColor }}>
      <div className="indicator-main">
        <div className="indicator-display">
          {/* 将指标值包装在一个自适应容器中 */}
          <div className="indicator-value">
            {/* 添加标题显示 */}
            {config.showTitle && config.title && <div className="indicator-title">{config.title}</div>}
            {/* 数值和百分比显示 */}
            <div className="indicator-data">
              <span className="indicator-number">{data.displayValue} {config.targetUnit !== '个' ? config.targetUnit : ''}</span>
              {config.showPercentage && (
                <span className="indicator-percent">
                  ({data.percentage}%)
                </span>
              )}
            </div>
            {/* 平均值显示 */}
            {config.showAverage && config.averageField && (
              <div className="indicator-average">
                <div className="average-item">
                  {config.showTotalCount && (
                    <>
                      <span className="average-label">{config.averageFieldName}: </span>
                      <span className="average-value">总计 {data.displayTotalCount} {config.totalCountUnit !== '个' ? config.totalCountUnit : ''}</span>
                      <span className="average-separator">/</span>
                    </>
                  )}
                  <span className="average-value">均值 {data.displayAverage} {config.averageUnit !== '个' ? config.averageUnit : ''}</span>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {isConfig && (
          <div className="indicator-config">
            <div className="config-form">
              <Tabs type="line" defaultActiveKey="basicConfig">
                <TabPane tab={t('indicator.tab.basicConfig') || "基础配置"} itemKey="basicConfig">
                  {/* 数据源配置块 */}
                  <div className="config-section">
                    <h4 className="section-title">{t('indicator.source.title')}</h4>
                    
                    {/* 表格选择 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.table')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.table')}
                          optionList={tables}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleTableChange(value);
                            }
                          }}
                          value={config.tableId ? JSON.stringify({ id: config.tableId, name: config.tableName }) : ''}
                          size="default"
                        />
                      </Item>
                    </div>
                    
                    {/* 范围选择 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.select.range')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.range')}
                          disabled={!config.tableId}
                          optionList={ranges}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleRangeChange(value);
                            }
                          }}
                          value={config.rangeName}
                          size="default"
                        />
                      </Item>
                    </div>
                    
                    {/* 分组字段选择 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.select.group')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.group')}
                          disabled={!config.tableId}
                          optionList={fields}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleGroupChange(value);
                            }
                          }}
                          value={config.groupFieldName}
                          size="default"
                        />
                      </Item>
                    </div>
                  </div>
                  
                  {/* 字段配置块 */}
                  <div className="config-section">
                    <h4 className="section-title">{t('indicator.fields.title')}</h4>
                    
                    {/* 目标字段 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.target.field')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.target')}
                          disabled={!config.tableId}
                          optionList={fields}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleTargetFieldChange(value);
                            }
                          }}
                          value={config.targetField ? JSON.stringify({ id: config.targetField, name: config.targetFieldName }) : ''}
                          size="default"
                        />
                      </Item>
                    </div>
                    
                    {/* 比较字段 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.compare.field')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.compare')}
                          disabled={!config.tableId}
                          optionList={fields}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleCompareFieldChange(value);
                            }
                          }}
                          value={config.compareField ? JSON.stringify({ id: config.compareField, name: config.compareFieldName }) : ''}
                          size="default"
                        />
                      </Item>
                    </div>
                    
                    {/* 平均值计算字段 */}
                    <div className="form-item-wrapper">
                      <Item label={t('indicator.average.field')}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder={t('indicator.select.average')}
                          disabled={!config.tableId}
                          optionList={fields}
                          onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                            if (typeof value === 'string') {
                              handleAverageFieldChange(value);
                            }
                          }}
                          value={config.averageField ? JSON.stringify({ id: config.averageField, name: config.averageFieldName }) : ''}
                          size="default"
                        />
                      </Item>
                    </div>
                  </div>
                </TabPane>

                <TabPane tab={t('indicator.tab.formatConfig') || "格式配置"} itemKey="formatConfig">
                  <div className="config-section">

                    {/* 目标字段格式配置 */}
                    <div className="field-row">
                      <div className="field-label">{t('indicator.target.field')}</div>
                      <div className="field-config-row">
                        <div 
                          className={`control-button ${config.showTargetAsPositive ? 'active' : ''}`}
                          onClick={() => handleTargetAsPositiveChange(!config.showTargetAsPositive)}
                        >
                          {t('indicator.displayOption.asPositive')}
                        </div>
                        <Select
                          style={{ width: '70px' }}
                          placeholder={t('indicator.unit.label')}
                          value={config.targetUnit}
                          onChange={handleTargetUnitChange}
                          optionList={[
                            { label: t('indicator.unit.个'), value: '个' },
                            { label: t('indicator.unit.千'), value: '千' },
                            { label: t('indicator.unit.万'), value: '万' },
                            { label: t('indicator.unit.百万'), value: '百万' },
                            { label: t('indicator.unit.亿'), value: '亿' }
                          ]}
                          size="default"
                        />
                      </div>
                    </div>

                    {/* 比较字段格式配置 */}
                    <div className="field-row">
                      <div className="field-label">{t('indicator.compare.field')}</div>
                      <div className="field-config-row">
                        <div 
                          className={`control-button ${config.showPercentageAsPositive ? 'active' : ''}`}
                          onClick={() => handlePercentageAsPositiveChange(!config.showPercentageAsPositive)}
                        >
                          {t('indicator.displayOption.asPositive')}
                        </div>
                      </div>
                    </div>
                  
                    {/* 平均值计算字段格式配置 */}
                    <div className="field-row">
                      <div className="field-label">{t('indicator.average.field')}</div>
                      <div className="field-config-row">
                        <div 
                          className={`control-button ${config.showAverageAsPositive ? 'active' : ''}`}
                          onClick={() => handleAverageAsPositiveChange(!config.showAverageAsPositive)}
                        >
                          {t('indicator.displayOption.asPositive')}
                        </div>
                        <Select
                          style={{ width: '70px' }}
                          placeholder={t('indicator.unit.label')}
                          value={config.averageUnit}
                          onChange={handleAverageUnitChange}
                          optionList={[
                            { label: t('indicator.unit.个'), value: '个' },
                            { label: t('indicator.unit.千'), value: '千' },
                            { label: t('indicator.unit.万'), value: '万' },
                            { label: t('indicator.unit.百万'), value: '百万' },
                            { label: t('indicator.unit.亿'), value: '亿' }
                          ]}
                          size="default"
                        />
                      </div>
                    </div>
                  
                    {/* 平均值计算字段总数格式配置 */}
                    <div className="field-row">
                      <div className="field-label">{t('indicator.displayOption.total')}</div>
                      <div className="field-config-row">
                        <div 
                          className={`control-button ${config.showTotalAsPositive ? 'active' : ''}`}
                          onClick={() => handleTotalAsPositiveChange(!config.showTotalAsPositive)}
                        >
                          {t('indicator.displayOption.asPositive')}
                        </div>
                        <Select
                          style={{ width: '70px' }}
                          placeholder={t('indicator.unit.label')}
                          value={config.totalCountUnit}
                          onChange={handleTotalCountUnitChange}
                          optionList={[
                            { label: t('indicator.unit.个'), value: '个' },
                            { label: t('indicator.unit.千'), value: '千' },
                            { label: t('indicator.unit.万'), value: '万' },
                            { label: t('indicator.unit.百万'), value: '百万' },
                            { label: t('indicator.unit.亿'), value: '亿' }
                          ]}
                          size="default"
                        />
                      </div>
                    </div>

                    <div className="format-section-divider"></div>

                     {/* 标题配置 */}
                     <div className="field-row">
                      <div className="field-label">{t('indicator.title')}</div>
                      <Input
                        placeholder={t('indicator.input.title')}
                        value={config.title}
                        onChange={handleTitleChange}
                        size="default"
                      />
                    </div>
                    
                    {/* 显示选项 */}
                    <div className="field-row display-settings-row">
                      <div className="field-label">{t('indicator.display.title')}</div>
                      <div className="display-control-buttons">
                        <div 
                          className={`control-button ${config.showTitle ? 'active' : ''}`}
                          onClick={() => handleShowTitleChange(!config.showTitle)}
                        >
                          {t('indicator.show.title')}
                        </div>
                        <div 
                          className={`control-button ${config.showPercentage ? 'active' : ''}`}
                          onClick={() => handleShowPercentageChange(!config.showPercentage)}
                        >
                          {t('indicator.show.percentage')}
                        </div>
                        <div 
                          className={`control-button ${config.showAverage ? 'active' : ''}`}
                          onClick={() => handleShowAverageChange(!config.showAverage)}
                        >
                          {t('indicator.show.average')}
                        </div>
                        <div 
                          className={`control-button ${config.showTotalCount ? 'active' : ''}`}
                          onClick={() => handleShowTotalCountChange(!config.showTotalCount)}
                        >
                          {t('indicator.show.totalCount')}
                        </div>
                      </div>
                    </div>

                  </div>
                </TabPane>
              </Tabs>
              
              <div className="config-footer">
                <Button type="primary" theme="solid" onClick={handleSave} size="default">
                  {t('indicator.save')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}