# 飞书多维表格仪表盘指标卡插件

这是一个为飞书多维表格仪表盘开发的指标卡插件，可以帮助用户在仪表盘上直观展示关键数据指标。

## 业务模块说明

该插件主要实现以下功能：

1. **指标数据展示**：根据用户配置，展示多维表格中的关键数据指标
2. **多状态适配**：适配仪表盘的创建、配置、展示和全屏四种状态
3. **国际化支持**：支持中文、英文和日文三种语言
4. **主题适配**：适配飞书仪表盘的主题样式，包括全屏深色模式

## 架构说明

该项目基于 React + TypeScript + Vite 构建，使用了飞书官方提供的 SDK 和 UI 组件库：

- **前端框架**：React 18，这是一个由 Facebook 开发的 JavaScript 库，用于构建用户界面。它使用组件化的方式来构建 UI，让代码更易于维护和复用。
- **开发语言**：TypeScript，这是 JavaScript 的超集，添加了静态类型定义。它可以帮助您在编写代码时捕获错误，提供更好的开发体验和代码提示。
- **构建工具**：Vite
- **UI 组件库**：Semi Design（使用飞书仪表盘定制主题）。这是一个由字节跳动开发的设计系统和 UI 组件库，提供了一系列可复用的组件，如按钮、表单、对话框等。在这个项目中使用了飞书仪表盘定制的主题。
- **SDK**：@lark-base-open/js-sdk，飞书多维表格插件 SDK，提供了与飞书多维表格交互的 API，让您能够读取表格数据、监听数据变化、保存配置等。
- **国际化**：i18next + react-i18next
- **日期处理**：dayjs

## 目录结构

```
indicator-card/
├── public/                 # 静态资源目录
├── src/                    # 源代码目录
│   ├── components/         # 组件目录
│   ├── locales/            # 国际化资源文件
│   ├── App.scss            # 应用样式
│   ├── App.tsx             # 应用主组件
│   ├── hooks.ts            # 自定义 React Hooks
│   └── index.tsx           # 应用入口
├── .gitignore              # Git 忽略文件
├── .npmrc                  # NPM 配置
├── .nvmrc                  # Node 版本管理
├── check_mobile.js         # 移动端检测脚本
├── index.html              # HTML 入口文件
├── mobile_disable.html     # 移动端禁用页面
├── package.json            # 项目依赖配置
├── README.md               # 项目说明文档
├── tsconfig.json           # TypeScript 配置
├── tsconfig.node.json      # Node TypeScript 配置
└── vite.config.js          # Vite 配置文件
```

### 主要文件说明

- **src/App.tsx**：应用主组件，处理插件的不同状态逻辑和指标卡展示功能
- **src/hooks.ts**：自定义 React Hooks，包含与飞书 SDK 交互的逻辑
- **src/components/**：包含各种 UI 组件，如指标卡显示、配置面板等
- **src/locales/**：包含中文、英文和日文的翻译文件
- **vite.config.js**：配置 Vite 构建工具和 Semi Design 主题

## 配置说明

### 项目依赖

项目的主要依赖包括：

```json
{
  "dependencies": {
    "@douyinfe/semi-foundation": "^2.76.1",
    "@douyinfe/semi-ui": "^2.76.1",
    "@lark-base-open/js-sdk": "0.4.1-beta.5",
    "@semi-bot/semi-theme-feishu-dashboard": "^1.0.4",
    "dayjs": "^1.11.11",
    "i18next": "^23.11.4",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-i18next": "^14.1.1",
    "vite-plugin-semi-theming": "^0.1.0"
  }
}
```

### Vite 配置

```javascript
export default defineConfig({
    base: "./",
    plugins: [
        react(),
        semiTheming({
            theme: "@semi-bot/semi-theme-feishu-dashboard",
        }),
    ],
    server: {
        host: "0.0.0.0",
    },
});
```

## 使用说明

### 开发环境设置

1. 克隆仓库到本地：

```bash
git clone <仓库地址>
cd indicator-card
```

2. 安装依赖：

```bash
npm install
```

3. 启动开发服务器：

```bash
npm run dev
```

### 构建生产版本

package.json 中的 output 字段指定了部署的目录，将会直接部署该目录。
每次提交之前，都需要手动执行构建命令来生成部署产物。

```bash
npm run build
```

当前配置，构建后的文件将生成在 `dist` 目录中。

## 在飞书多维表格中调试

1. 打开任意飞书多维表格仪表盘
2. 点击"添加组件"
3. 点击"更多"打开插件市场
4. 选择"添加自定义插件"
5. 输入插件运行地址（本地开发时通常是 `http://localhost:5173`）

## 发布到插件中心

1. 构建生产版本
2. 通过[飞书插件发布表单](https://feishu.feishu.cn/share/base/form/shrcnGFgOOsFGew3SDZHPhzkM0e)提交插件

## 插件状态说明

该插件适配了仪表盘的四种状态：

1. **创建状态**：首次添加插件时的状态，用户可以配置指标卡数据源和展示方式
2. **配置状态**：用户可以修改指标卡配置
3. **展示状态**：仅展示指标卡数据
4. **全屏状态**：适配深色模式的指标卡展示

## 国际化支持

插件支持中文、英文和日文三种语言，会根据用户的飞书界面语言自动切换。

## 注意事项

1. 该插件仅适用于飞书多维表格仪表盘
2. 开发时需要使用 Node.js v18 或更高版本（根据 .nvmrc 文件显示为 18.15.0）
3. 插件发布前需确保符合飞书插件中心的审核要求

## 贡献指南

欢迎提交 Issue 或 Pull Request 来改进这个插件。在提交代码前，请确保：

1. 代码符合项目的编码规范
2. 所有测试通过
3. 更新相关文档

## 许可证

ISC