import './style.scss';
import { dashboard, DashboardState, SourceType, Rollup } from "@lark-base-open/js-sdk";
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, Input, Button } from '@douyinfe/semi-ui';
import { Item } from '../Item';
import { 
  useIndicatorData, 
  useTableList, 
  useFieldList, 
  ISelectOption, 
  IIndicatorConfig, 
  IIndicatorData 
} from './useIndicatorData';

export default function IndicatorCard(props: { bgColor: string }) {
  const { t } = useTranslation();
  
  const [config, setConfig] = useState<IIndicatorConfig>({
    tableId: '',
    tableName: '',
    targetField: '',
    targetFieldName: '',
    compareField: '',
    compareFieldName:'',
    title: t('indicator.default.title')
  });

  // 使用自定义Hook获取数据
  const data = useIndicatorData(config);
  
  // 使用自定义Hook获取表格和字段列表
  const tables = useTableList();
  const fields = useFieldList(config.tableId);
  
  // 添加 Select 组件的处理函数
  const handleTableChange = (value: string) => {
    try {
      const { id, name } = JSON.parse(value);
      setConfig({ 
        ...config, 
        tableId: id,
        tableName: name
      });
    } catch (error) {
      console.error('Failed to parse table value:', error);
    }
  };
  
  const handleTargetFieldChange = (value: string) => {
    try {
      const { id, name } = JSON.parse(value);
      setConfig({ 
        ...config, 
        targetField: id,
        targetFieldName: name
      });
    } catch (error) {
      console.error('Failed to parse target field value:', error);
    }
  };
  
  const handleCompareFieldChange = (value: string) => {
    try {
      const { id, name } = JSON.parse(value);
      setConfig({ 
        ...config, 
        compareField: id,
        compareFieldName: name
      });
    } catch (error) {
      console.error('Failed to parse compare field value:', error);
    }
  };

  const handleTitleChange = (value: string) => {
    setConfig({ ...config, title: value });
  };

  // Check if in config mode
  const isConfig = dashboard.state === DashboardState.Config 
                  || dashboard.state === DashboardState.Create;

  // 添加保存配置的处理函数
  const handleSave = async () => {
    try {
      console.log('Saving config:', config);

      // 修改 dataConditions 格式，确保包含所有必要字段
      await dashboard.saveConfig({
        customConfig: config as unknown as Record<string, unknown>,
        dataConditions: [
          {
            tableId: config.tableId,
            dataRange: { type: SourceType.ALL },
            series: [
              { 
                fieldId: config.targetField, 
                rollup: Rollup.SUM,
              }
            ]
          },
          {
            tableId: config.tableId,
            dataRange: { type: SourceType.ALL },
            series: [
              { 
                fieldId: config.compareField, 
                rollup: Rollup.SUM,
              }
            ]
          }
        ]
      });
      // 保存后切换到视图模式
      dashboard.setRendered(); // 必须调用此方法通知宿主
      console.log('保存配置后状态：',dashboard.state);
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  };

  // 展示态
  useEffect(() => {
    if (dashboard.state === DashboardState.View) {
      dashboard.getData().then(data => {
        console.log('获取数据成功:', data);
      });

      dashboard.onDataChange(async (res) => {
        console.log('数据变更:', res);
      })
    }
  }, [])

  // 添加配置恢复逻辑
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const savedConfig = await dashboard.getConfig();
        console.log('Loaded saved config:', savedConfig);
        if (savedConfig?.customConfig) {
          console.log('Setting config from saved config:', savedConfig.customConfig);
          setConfig(savedConfig.customConfig as unknown as IIndicatorConfig);
        }
      } catch (error) {
        console.error('Failed to load config:', error);
      }
    };

    loadConfig();
  }, []);

  return (
    <div className="indicator-wrapper" style={{ backgroundColor: props.bgColor }}>
      <div className="indicator-main">
        <div className="indicator-display">
          <div className="indicator-title">{config.title}</div>
          <div className="indicator-number">{data.value.toLocaleString()}</div>
          <div className="indicator-percent">({data.percentage}%)</div>
        </div>
        
        {isConfig && (
          <div className="indicator-config">
            <div className="config-form">
              <Item label={t('indicator.table')}>
                <Select
                  placeholder={t('indicator.select.table')}
                  value={config.tableId ? JSON.stringify({ id: config.tableId, name: config.tableName }) : ''}
                  onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                    if (typeof value === 'string') {
                      handleTableChange(value);
                    }
                  }}
                  optionList={tables}
                  style={{ width: '100%' }}
                />
              </Item>
              <Item label={t('indicator.target.field')}>
                <Select
                  placeholder={t('indicator.select.target')}
                  value={config.targetField ? JSON.stringify({ id: config.targetField, name: config.targetFieldName }) : ''}
                  onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                    if (typeof value === 'string') {
                      handleTargetFieldChange(value);
                    }
                  }}
                  optionList={fields}
                  style={{ width: '100%' }}
                  disabled={!config.tableId}
                />
              </Item>
              <Item label={t('indicator.compare.field')}>
                <Select
                  placeholder={t('indicator.select.compare')}
                  value={config.compareField ? JSON.stringify({ id: config.compareField, name: config.compareFieldName }) : ''}
                  onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
                    if (typeof value === 'string') {
                      handleCompareFieldChange(value);
                    }
                  }}
                  optionList={fields}
                  style={{ width: '100%' }}
                  disabled={!config.tableId}
                />
              </Item>
              <Item label={t('indicator.title')}>
                <Input
                  placeholder={t('indicator.input.title')}
                  value={config.title}
                  onChange={handleTitleChange}
                />
              </Item>
            </div>
            <div className="config-footer">
              <Button type="primary" theme="solid" onClick={handleSave}>
                {t('indicator.save')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}