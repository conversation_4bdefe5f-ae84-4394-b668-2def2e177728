// 渠道数据接口定义
export interface ChannelData {
  分渠道: string;
  渠道负责人: string;
  渠道二类: string;
  收入: number;
  占总收入比: string;
  商品成本: number;
  仓储物流成本: number;
  毛利率_不含物流: string;
  毛利率_含物流: string;
  渠道费用合计: number;
  TP佣金: number;
  TP固定费用: number;
  站内投放: number;
  品牌推广费用: number;
  拍摄素材及设计费用: number;
  平台费项: number;
  门店物料_ATM: number;
  补提佣金: number;
  销售样品及赠品: number;
  销售激励: number;
  其他渠道费用: number;
  站内投放发票递减: number;
  分摊前利润: number;
  数字化: number;
  数字化系统费: number;
  数字化人力: number;
  资产减值损失: number;
  管理相关费用: number;
  人力成本: number;
  研发人力: number;
  占总人力成本比: string;
  业绩奖金: number;
  分摊后利润: number;
  分摊后利润_不含减值损失: number;
  残次计提: number;
  软件及技术服务费: number;
  品控: number;
  其他咨询服务费: number;
  应收逾期扣减: number;
  研发费用: number;
  商品研发费: number;
  积分成本: number;
  月份: string;
  口径: string;
  数据来源: string;
  年份: number;
  季度: string;
  预算_实际: string;
  是否剔除调整: string;
  H1_H2: string;
  大促: string;
  大促精准: string;
  渠道一级: string;
  渠道二级: string;
  线上_线下: string;
}

// 插件配置接口
export interface ChannelProfitConfig {
  tableId: string;
  tableName: string;
  channelField: string;
  managerField: string;
  selectedChannel: string;
  selectedManager: string;
  dateRangeType: 'all' | 'year' | 'quarter' | 'month';
  selectedYear?: number;
  selectedQuarter?: string;
  selectedMonth?: string;
  showComparison: boolean;
  chartTypes: {
    revenue: boolean;
    cost: boolean;
    margin: boolean;
    expense: boolean;
  };
}

// 选择选项接口
export interface SelectOption {
  label: string;
  value: string;
}

// 图表数据接口
export interface ChartDataPoint {
  month: string;
  revenue: number;
  profit: number;
  cost: number;
  grossMarginExcl: number;
  grossMarginIncl: number;
  netProfitMargin: number;
  year: string;
}

// 费用明细接口
export interface ExpenseItem {
  name: string;
  value: number;
  percentage: number;
}

// 多维表字段映射
export const FIELD_MAPPING = {
  // 基础字段
  CHANNEL: '分渠道',
  MANAGER: '渠道负责人',
  CHANNEL_CATEGORY: '渠道二类',
  REVENUE: '收入',
  REVENUE_RATIO: '占总收入比',
  
  // 成本字段
  PRODUCT_COST: '商品成本',
  LOGISTICS_COST: '仓储物流成本',
  GROSS_MARGIN_EXCL: '毛利率_不含物流',
  GROSS_MARGIN_INCL: '毛利率_含物流',
  
  // 渠道费用
  CHANNEL_EXPENSE_TOTAL: '渠道费用合计',
  TP_COMMISSION: 'TP佣金',
  TP_FIXED_FEE: 'TP固定费用',
  PLATFORM_AD: '站内投放',
  BRAND_PROMOTION: '品牌推广费用',
  DESIGN_FEE: '拍摄素材及设计费用',
  PLATFORM_FEE: '平台费项',
  
  // 利润字段
  PROFIT_BEFORE_ALLOCATION: '分摊前利润',
  PROFIT_AFTER_ALLOCATION: '分摊后利润',
  PROFIT_EXCL_IMPAIRMENT: '分摊后利润_不含减值损失',
  
  // 时间字段
  MONTH: '月份',
  YEAR: '年份',
  QUARTER: '季度',
  H1_H2: 'H1_H2',
  
  // 分类字段
  CHANNEL_L1: '渠道一级',
  CHANNEL_L2: '渠道二级',
  ONLINE_OFFLINE: '线上_线下',
} as const;
