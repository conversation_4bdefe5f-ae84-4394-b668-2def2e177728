import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { semiTheming } from "vite-plugin-semi-theming";
import * as sass from 'sass';

// https://vitejs.dev/config/
export default defineConfig({
    base: "./",
    plugins: [
        react(),
        semiTheming({
            theme: "@semi-bot/semi-theme-feishu-dashboard",
        }),
    ],
    server: {
        host: "0.0.0.0",
        port: 5051,
    },
    build: {
        // 启用 gzip 压缩
        brotliSize: true,
        // 分块策略
        rollupOptions: {
            output: {
            manualChunks: {
                'react-vendor': ['react', 'react-dom'],
                'semi-ui': ['@douyinfe/semi-ui']
            }
            }
        }
    },
    css: {
        preprocessorOptions: {
            scss: {
                implementation: sass
            }
        }
    }
});
