entrypoint = "index.js"

hidden = [".config", ".next", ".swc"]

run = "npm run dev"

[[hints]]
regex = "Error \\[ERR_REQUIRE_ESM\\]"
message = "We see that you are using require(...) inside your code. We currently do not support this syntax. Please use 'import' instead when using external modules. (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/import)"

[nix]
channel = "stable-22_11"

[env]
XDG_CONFIG_HOME = "/home/<USER>/.config"
PATH = "$REPL_HOME/.config/npm/node_global/bin:$REPL_HOME/node_modules/.bin"
npm_config_prefix = "$REPL_HOME/.config/npm/node_global"
NEXT_TELEMETRY_DISABLED = "1"

[packager]
language = "nodejs"

  [packager.features]
  packageSearch = true
  guessImports = true
  enabledForHosting = false

[unitTest]
language = "nodejs"

[languages.javascript]
pattern = "**/{*.js,*.jsx,*.ts,*.tsx}"

  [languages.javascript.languageServer]
  start = [ "typescript-language-server", "--stdio" ]

[debugger]
support = true

  [debugger.interactive]
  transport = "localhost:0"
  startCommand = [ "dap-node" ]

    [debugger.interactive.initializeMessage]
    command = "initialize"
    type = "request"

      [debugger.interactive.initializeMessage.arguments]
      clientID = "replit"
      clientName = "replit.com"
      columnsStartAt1 = true
      linesStartAt1 = true
      locale = "en-us"
      pathFormat = "path"
      supportsInvalidatedEvent = true
      supportsProgressReporting = true
      supportsRunInTerminalRequest = true
      supportsVariablePaging = true
      supportsVariableType = true

    [debugger.interactive.launchMessage]
    command = "launch"
    type = "request"
    
      [debugger.interactive.launchMessage.arguments]  
      args = []
      console = "externalTerminal"
      cwd = "."
      environment = []
      pauseForSourceMap = false
      program = "./index.js"
      request = "launch"
      sourceMaps = true
      stopOnEntry = false
      type = "pwa-node"

[deployment]
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "npm run start"]

[[ports]]
localPort = 3000
externalPort = 80