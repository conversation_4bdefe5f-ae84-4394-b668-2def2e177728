.indicator-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;  // 确保子元素能撑满高度
  overflow: hidden;

  .indicator-main {
    width: 100%;
    height: 100%;
    display: flex;
    gap: 4px;
    padding: 2px;

    // 在小屏幕上调整布局为垂直方向
    @media screen and (max-width: 480px) {
      flex-direction: column;
    }

    .indicator-display {
      flex: 7;
      min-height: 100px;
      display: flex;
      align-items: center;      // 水平居中
      justify-content: center;  // 垂直居中
      padding: 12px;
      border-radius: 6px;
      background-color: rgba(255, 255, 255, 0.7);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      overflow: auto;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      // 在小屏幕上调整显示
      @media screen and (max-width: 480px) {
        padding: 12px;
        min-height: 100px;
      }

      .indicator-value {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        min-height: 100%;
        padding: 4px 0;
        
        .indicator-title {
          font-size: clamp(14px, min(2.5vw, 2.5vh), 18px);
          color: #646a73;
          margin-bottom: 8px;
          text-align: center;
          font-weight: 500;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .indicator-data {
          display: flex;
          align-items: baseline;
          gap: 6px;
          justify-content: center;
          margin-bottom: 8px;
          flex-wrap: wrap;

          .indicator-number {
            font-weight: 700;
            color: #1f2329;
            /* 使用clamp代替min-font-size，设置最小值，首选值和最大值 */
            font-size: clamp(24px, min(8vw, 8vh), 48px);
            line-height: 1.2;
            text-align: center;
          }

          .indicator-percent {
            font-size: clamp(14px, min(3.5vw, 3.5vh), 18px);
            color: #646a73;
            padding: 2px 6px;
            border-radius: 6px;
            background-color: rgba(240, 242, 245, 0.6);
          }
        }
        
        .indicator-average {
          margin-top: 4px;
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 4px;
          
          .average-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 10px;
            border-radius: 6px;
            background-color: rgba(240, 242, 245, 0.4);
            font-size: clamp(13px, min(2.7vw, 2.7vh), 16px);
            color: #646a73;
            text-align: center;
            width: auto;
            max-width: 100%;
            overflow: hidden;
            flex-wrap: wrap;
            justify-content: center;
            
            .average-label {
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              flex-shrink: 0;
            }
            
            .average-value {
              color: #1f2329;
              font-weight: 600;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            
            .average-separator {
              margin: 0 2px;
              color: #a3a6ad;
              font-weight: bold;
            }
          }
        }
      }
    }

    .indicator-config {
      flex: 3;
      padding: 8px;
      border-left: 1px solid #e5e6eb;
      overflow-y: auto;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 6px;

      @media screen and (max-width: 768px) {
        flex: none;
        border-left: none;
        border-top: 1px solid #e5e6eb;
      }

      // 在小屏幕上调整配置区域
      @media screen and (max-width: 480px) {
        padding: 10px;
      }

      .config-form {
        width: 100%;
        
        .config-section {
          margin-bottom: 10px;
          padding: 8px;
          background-color: rgba(246, 247, 250, 0.6);
          border-radius: 6px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
          
          .section-title {
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            color: #1f2329;
            padding-bottom: 4px;
            border-bottom: 1px solid rgba(229, 230, 235, 0.8);
          }
        }
        
        .field-row {
          display: flex;
          align-items: center;
          margin-bottom: 30px;
          
          .field-label {
            width: 100px;
            flex-shrink: 0;
            font-size: 13px;
            color: #1f2329;
            font-weight: 500;
            padding-right: 10px;
          }
          
          .field-config-row {
            flex: 1;
          }
          
          &.display-settings-row {
            .field-label {
              width: 80px;
              align-self: flex-start;
              margin-top: 3px;
            }
            margin-bottom: 12px;
            align-items: flex-start;
            
            .display-control-buttons {
              gap: 6px;
              margin-bottom: 3px;
              margin-left: 20px;
            }
          }
        }

        .form-item-wrapper {
          margin-bottom: 8px;
        }

        .field-config-row {
          display: flex;
          gap: 6px;
          align-items: center;
          padding: 1px 0;
        }

        .display-control-buttons {
          display: flex;
          gap: 6px;
          flex-wrap: wrap;
          width: 100%;
        }

        .control-button {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 13px;
          cursor: pointer;
          user-select: none;
          text-align: center;
          transition: all 0.2s ease;
          background-color: #f5f5f5;
          color: #666;
          border: 1px solid #e0e0e0;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 22px;
          
          &:hover {
            background-color: #e8e8e8;
          }
          
          &.active {
            background-color: #2e7cf6;
            color: white;
            border-color: #2e7cf6;
            
            &:hover {
              background-color: #1b6ef3;
            }
          }
          
          // 在字段配置行中的样式
          .field-config-row & {
            flex: 1;
          }
          
          // 在显示控制按钮中的样式
          .display-control-buttons & {
            flex: 0 0 calc(50% - 4px);
          }
        }

        :global(.semi-form-field) {
          margin-bottom: 6px;
          padding-bottom: 0;
        }

        :global(.semi-form-field-label-text) {
          font-size: 13px;
        }
        
        :global(.semi-select), :global(.semi-input) {
          line-height: 32px;
          height: 32px;
        }
        
        :global(.semi-select-selection), :global(.semi-input-wrapper) {
          padding-top: 0;
          padding-bottom: 0;
        }

        // 添加分隔线样式
        .format-section-divider {
          height: 1px;
          background-color: #eee;
          margin: 12px 0;
        }
      }

      .config-footer {
        margin-top: 10px;
        text-align: right;
        
        button {
          padding: 6px 12px;
        }
      }
    }
  }
}

// Tab相关样式
.indicator-config .semi-tabs {
  margin-bottom: 10px;
}

.indicator-config .semi-tabs-bar {
  margin-bottom: 8px;
}

.indicator-config .semi-tabs-tab {
  font-size: 14px;
  padding: 6px 12px;
}

.indicator-config .semi-tabs-tab-active {
  font-weight: 600;
}

.indicator-config .semi-tabs-content {
  padding-top: 2px;
}