import { useState, useEffect, useCallback } from 'react';
import { bitable, base, dashboard, DashboardState, SourceType,Rollup,ORDER,DATA_SOURCE_SORT_TYPE,GroupMode,IDataCondition,IData } from '@lark-base-open/js-sdk';

// 定义指标配置接口
export interface IIndicatorConfig {
  tableId: string;
  tableName: string;
  rangeType: string;      // 添加数据范围类型
  rangeName: string;      // 添加数据范围名称
  groupField: string;     // 添加分组字段
  groupFieldName: string; // 添加分组字段名称
  targetField: string;
  targetFieldName: string;
  compareField: string;
  compareFieldName: string;
  // 添加平均值计算相关字段
  averageField: string;   // 平均值计算字段（如销量）
  averageFieldName: string; // 平均值计算字段名称
  
  // 显示控制选项
  showTitle: boolean;         // 是否显示标题
  showPercentage: boolean;    // 是否显示占比
  showAverage: boolean;       // 是否显示平均值
  showTotalCount: boolean;    // 是否显示总计数量
  
  // 数值单位选择
  targetUnit: string;         // 目标字段单位，如："个"、"千"、"万"、"亿"
  totalCountUnit: string;     // 总数单位
  averageUnit: string;        // 平均值单位
  
  // 数值正负值选择
  showTargetAsPositive: boolean;  // 是否显示目标值为正数
  showTotalAsPositive: boolean;   // 是否显示总数为正数
  showAverageAsPositive: boolean; // 是否显示平均值为正数
  showPercentageAsPositive: boolean; // 是否显示百分比为正数
  
  title: string;
}

// 定义下拉选项获取数据接口
export interface ISelectOption {
  label: string;
  value: string;
}

// 定义字段值接口
export interface IFieldValue {
  groupValue: string;    // 分组字段值
  targetValue: number;   // 目标字段值
  compareValue: number;  // 比较字段值
  averageValue: number;  // 平均值计算字段值
}

// 定义字段信息接口
export interface IFieldInfo {
  groupField: {          // 分组字段信息
    id: string;
    name: string;
  };
  targetField: {         // 目标字段信息
    id: string;
    name: string;
  };
  compareField: {        // 比较字段信息
    id: string;
    name: string;
  };
  averageField: {        // 平均值计算字段信息
    id: string;
    name: string;
  };
  values: IFieldValue[]; // 字段值数组
}

export interface IIndicatorData {
  title: string;
  value: number;           // 原始值
  displayValue: string;    // 根据单位格式化后的显示值
  percentage: number;
  average: number;         // 平均值原始数据
  displayAverage: string;  // 根据单位格式化后的平均值
  averageLabel: string;    // 平均值标签
  totalCount: number;      // 平均值计算字段的总数原始值
  displayTotalCount: string; // 根据单位格式化后的总数值
  totalCountLabel: string;  // 总数标签
}

// 获取表格列表的Hook
export function useTableList(): ISelectOption[] {
  const [tables, setTables] = useState<ISelectOption[]>([]);

  useEffect(() => {
    console.log('开始获取表格列表');
    
    const fetchTables = async () => {
      try {
        const tableList = await base.getTableList();
        console.log('原始表格列表:', tableList);
        
        const formattedTables = await Promise.all(
          tableList.map(async (table) => {
            const name = await table.getName();
            console.log('表格名称:', name, '表格ID:', table.id);
            
            return {
              label: name,
              value: JSON.stringify({ id: table.id, name })
            };
          })
        );
        
        console.log('格式化后的表格列表:', formattedTables);
        setTables(formattedTables);
      } catch (error) {
        console.error('获取表格列表失败:', error);
      }
    };

    fetchTables();
  }, []);

  return tables;
}

//  获取表格数据范围的 Hook
export function useDataRange(tableId: string): ISelectOption[] {
  const [ranges, setRanges] = useState<ISelectOption[]>([]);

  useEffect(() => {
    console.log('开始获取数据范围');

    if (!tableId) {
      console.log('表格ID为空，不获取数据范围');
      setRanges([]);
      return;
    }

    const fetchRanges = async () => {
      try {
        console.log('开始获取数据范围元数据列表');
        const dataRanges = await dashboard.getTableDataRange(tableId);
        console.log('原始数据范围列表:', dataRanges);
        
        const formattedRanges = dataRanges.map((range) => {
          const rangeName = range.type === SourceType.ALL ? '全部数据' : '视图数据';
          return {
            label: rangeName,
            value: JSON.stringify({ type: range.type, rangeName })
          };
        });
        console.log('格式化后的数据范围列表:', formattedRanges);

        setRanges(formattedRanges);
      } catch (error) {
        console.error('获取数据范围列表失败:', error);
      }
    };

    fetchRanges();
  }, [tableId]);

  return ranges;
}

// 获取字段列表的Hook
export function useFieldList(tableId: string): ISelectOption[] {
  const [fields, setFields] = useState<ISelectOption[]>([]);

  useEffect(() => {
    console.log('开始获取字段列表, 表格ID:', tableId);
    
    if (!tableId) {
      console.log('表格ID为空，不获取字段列表');
      setFields([]);
      return;
    }

    const fetchFields = async () => {
      try {
        console.log('开始获取表格实例');
        const table = await base.getTableById(tableId);
        console.log('获取到表格实例:', table);
        
        console.log('开始获取字段元数据列表');
        const fieldMetaList = await table.getFieldMetaList();
        console.log('原始字段元数据列表:', fieldMetaList);
        
        const formattedFields = fieldMetaList.map((field) => {
          console.log('字段名称:', field.name, '字段ID:', field.id);
          
          return {
            label: field.name,
            value: JSON.stringify({ id: field.id, name: field.name })
          };
        });
        
        console.log('格式化后的字段列表:', formattedFields);
        setFields(formattedFields);
      } catch (error) {
        console.error('获取字段列表失败:', error);
      }
    };

    fetchFields();
  }, [tableId]); // 监听注入的 tableId 变化，如果变化则重新获取字段列表

  return fields;
}

// 获取指标数据的Hook
export function useIndicatorData(config: IIndicatorConfig): IIndicatorData {
  const [data, setData] = useState<IIndicatorData>({ 
    value: 0, 
    displayValue: '0',
    percentage: 0, 
    title: '', 
    average: 0, 
    displayAverage: '0',
    averageLabel: '', 
    totalCount: 0, 
    displayTotalCount: '0',
    totalCountLabel: '' 
  });

  useEffect(() => {
    console.log('开始获取指标数据, 配置:', config);
    
    if (!config.tableId || !config.rangeType || !config.groupField || !config.targetField || !config.compareField) {
      console.log('配置不完整，无法获取指标数据');
      return;
    }

    const fetchData = async () => {
      try {
        console.log('当前仪表盘状态:', dashboard.state);
        
        if (dashboard.state === DashboardState.View) {
          const data = await getViewModeData(config);
          setData(data);
        } else {
          // 配置态下使用 getConfigModeData 获取实时数据
          const previewData = await getConfigModeData(config);
          setData(previewData);
        }
      } catch (error) {
        console.error('获取指标数据失败:', error);
      }
    };

    fetchData();
  }, [config]); // config 变化时重新获取数据

  return data;
}

// 获取查询配置
export function getQueryConfig(config: IIndicatorConfig): IDataCondition {
  return {
    tableId: config.tableId, 
    groups: [
      {
        fieldId: config.groupField,
        sort: {order: ORDER.ASCENDING, sortType: DATA_SOURCE_SORT_TYPE.VALUE},
        mode: GroupMode.ENUMERATED,
      },
    ],
    dataRange: { type: SourceType.ALL },
    series: [
      { fieldId: config.targetField, rollup: Rollup.SUM },
      { fieldId: config.compareField, rollup: Rollup.SUM },
      ...(config.averageField ? [{ fieldId: config.averageField, rollup: Rollup.SUM }] : [])
    ]
  };
}

// 结构化数据查询结果
export function structureData(config: IIndicatorConfig, data: IData): IFieldInfo {
  // 处理字段信息
  const fieldInfo: IFieldInfo = {
    groupField: {
      id: config.groupField,
      name: config.groupFieldName
    },
    targetField: {
      id: config.targetField,
      name: config.targetFieldName
    },
    compareField: {
      id: config.compareField,
      name: config.compareFieldName
    },
    averageField: {
      id: config.averageField,
      name: config.averageFieldName
    },
    values: []
  };
  // 从第二行开始处理数据（跳过表头）
  for (let i = 1; i < data.length; i++) {
    fieldInfo.values.push({
      groupValue: data[i][0]?.value?.toString() ?? '',
      targetValue: parseFloat((parseFloat(data[i][1]?.value?.toString() ?? '0') || 0).toFixed(2)),
      compareValue: parseFloat((parseFloat(data[i][2]?.value?.toString() ?? '0') || 0).toFixed(2)),
      averageValue: parseFloat((parseFloat(data[i][3]?.value?.toString() ?? '0') || 0).toFixed(2))
    });
  }
  return fieldInfo;
}

// 单位转换函数
export function formatNumberWithUnit(value: number, unit: string, showAsPositive: boolean = true): string {
  // 根据showAsPositive决定是否取绝对值
  const processedValue = showAsPositive ? Math.abs(value) : value;
  
  // 根据单位进行转换
  let convertedValue: number;
  
  switch(unit) {
    case '个':
      convertedValue = processedValue;
      break;
    case '千':
      convertedValue = processedValue / 1000;
      break;
    case '万':
      convertedValue = processedValue / 10000;
      break;
    case '百万':
      convertedValue = processedValue / 1000000;
      break;
    case '亿':
      convertedValue = processedValue / 100000000;
      break;
    default:
      convertedValue = processedValue;
  }
  
  // 格式化数值，保留两位小数
  return unit !== '个' ? 
    convertedValue.toLocaleString(undefined, { maximumFractionDigits: 2 }) : 
    convertedValue.toLocaleString();
}

// 计算指标数据及占比
export function calculateIndicatorData(config: IIndicatorConfig, fieldInfo: IFieldInfo): IIndicatorData {
  // 初始化指标数据
  const indicatorData: IIndicatorData = {
    title: config.title,
    value: 0,
    displayValue: '0',
    percentage: 0,
    average: 0,
    displayAverage: '0',
    averageLabel: '',
    totalCount: 0,
    displayTotalCount: '0',
    totalCountLabel: ''
  };
  
  // 计算目标值和比较值 - 使用原始值
  const targetValue = fieldInfo.values.reduce((sum, value) => {
    return sum + value.targetValue;
  }, 0);
  
  const compareValue = fieldInfo.values.reduce((sum, value) => {
    return sum + value.compareValue;
  }, 0);
  
  // 计算平均值相关数据
  if (config.averageField) {
    // 计算平均值计算字段总数 - 使用原始值
    const totalAverageValue = fieldInfo.values.reduce((sum, value) => {
      return sum + value.averageValue;
    }, 0);
    
    // 保存平均值计算字段的总数
    indicatorData.totalCount = totalAverageValue;
    indicatorData.totalCountLabel = `${config.averageFieldName || '数量'}总计`;
    
    // 应用用户设置的单位和是否显示为正数
    indicatorData.displayTotalCount = formatNumberWithUnit(
      totalAverageValue, 
      config.totalCountUnit || '个', 
      config.showTotalAsPositive
    );
    
    // 计算每单位平均值（例如：每单销售额、每人销售额等）
    const average = totalAverageValue !== 0 ? parseFloat((targetValue / totalAverageValue).toFixed(2)) : 0;
    indicatorData.average = average;
    indicatorData.averageLabel = `${config.averageFieldName || '单位'}均值`;
    
    // 应用用户设置的单位和是否显示为正数
    indicatorData.displayAverage = formatNumberWithUnit(
      average, 
      config.averageUnit || '个', 
      config.showAverageAsPositive
    );
  }
  
  // 计算百分比
  let rawPercentage = compareValue !== 0 ? (targetValue / compareValue) * 100 : 0;
  // 根据用户设置决定是否显示为正数
  indicatorData.percentage = Math.round(config.showPercentageAsPositive ? Math.abs(rawPercentage) : rawPercentage);
  
  // 设置指标数据
  indicatorData.value = targetValue;
  // 应用用户设置的单位和是否显示为正数
  indicatorData.displayValue = formatNumberWithUnit(
    targetValue, 
    config.targetUnit || '个', 
    config.showTargetAsPositive
  );
  
  console.log('计算后的指标数据:', indicatorData);
  return indicatorData;
}

// 配置/创建模式下获取统计数据
export async function getConfigModeData(config: IIndicatorConfig): Promise<IIndicatorData> {
  console.log('进入配置/创建模式下获取数据');

  // 获取查询配置信息
  const queryConfig = getQueryConfig(config);
  console.log('配置/创建模式下获取数据的查询配置:', queryConfig);
  // 获取数据
  const data = await dashboard.getPreviewData(queryConfig);
  console.log('配置/创建模式下获取到的数据:', data);
  // 结构化处理数据
  const structuredData = structureData(config, data);
  console.log('配置/创建模式下结构化处理的数据:', structuredData);
  // 计算指标数据
  const res = calculateIndicatorData(config, structuredData);
  console.log('配置/创建模式下计算得到的指标数据:', res);
  
  return res
}

// 视图模式下获取统计数据
export async function getViewModeData(config: IIndicatorConfig): Promise<IIndicatorData>  {
    console.log('进入视图模式下获取数据');
    // 获取数据
    const data = await dashboard.getData();
    console.log('视图模式下获取到的数据:', data);
    // 结构化处理数据
    const structuredData = structureData(config, data);
    console.log('视图模式下结构化处理的数据:', structuredData);
    // 计算指标数据
    const res = calculateIndicatorData(config, structuredData);
    console.log('视图模式下计算得到的指标数据:', res);

    return res;
}