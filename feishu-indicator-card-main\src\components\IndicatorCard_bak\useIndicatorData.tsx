import { useEffect, useState } from 'react';
import { bitable, dashboard, SourceType, Rollup } from "@lark-base-open/js-sdk";

// 导出接口定义，使其可以在 index.tsx 中使用
export interface ISelectOption {
  label: string;
  value: string;
}

export interface IIndicatorConfig {
  tableId: string;
  tableName: string;
  targetField: string;
  targetFieldName: string;
  compareField: string;
  compareFieldName: string;
  title: string;
}

export interface IIndicatorData {
  value: number;
  percentage: number;
  title: string;
}

export function useIndicatorData(config: IIndicatorConfig): IIndicatorData {
  const [data, setData] = useState<IIndicatorData>({
    value: 0,
    percentage: 0,
    title: config.title || ''
  });

  // 获取视图模式下的数据
  const getViewModeData = async (config: IIndicatorConfig) => {
    console.log('进入视图模式下获取数据');

    const [targetData, compareData] = await Promise.all([
        getViewModeFieldSummary(config.tableId, config.targetField),
        getViewModeFieldSummary(config.tableId, config.compareField)
    ]);
    console.log('视图模式下计算汇总数据 - 目标:', targetData, '比较:', compareData);

    return {
    targetSum: Number(targetData[1]?.[0]?.value ?? 0),
    compareSum: Number(compareData[1]?.[0]?.value ?? 0)
    };
  };

  // 获取视图模式下的单个字段汇总数据
  const getViewModeFieldSummary = async (tableId: string, fieldId: string) => {
    await dashboard.saveConfig({
    dataConditions: [{
        tableId: tableId,
        dataRange: { type: SourceType.ALL },
        series: [{ fieldId: fieldId, rollup: Rollup.SUM }]
    }]
    });
    return dashboard.getData();
  };

  // 获取预览模式下的数据
  const getPreviewModeData = async (config: IIndicatorConfig) => {
    console.log('进入预览模式下获取数据');
    const [targetData, compareData] = await Promise.all([
        getPreviewModeFieldSummary(config.tableId, config.targetField),
        getPreviewModeFieldSummary(config.tableId, config.compareField)
    ]);
    console.log('预览模式下计算汇总数据 - 目标:', targetData, '比较:', compareData);

    return {
        targetSum: Number(targetData[1]?.[0]?.value ?? 0).toFixed(2),
        compareSum: Number(compareData[1]?.[0]?.value ?? 0).toFixed(2)
    };
  };

  // 获取预览模式下单个字段的汇总数据
  const getPreviewModeFieldSummary = async (tableId: string, fieldId: string) => {
    return dashboard.getPreviewData({
        tableId: tableId,
        dataRange: { type: SourceType.ALL },
        series: [{ fieldId: fieldId, rollup: Rollup.SUM }]
    });
  };

  // 计算指标数据
  const calculateIndicatorData = (targetSum: number, compareSum: number): IIndicatorData => {
    const percentage = compareSum ? (targetSum / compareSum * 100) : 0;
    return {
        value: targetSum,
        percentage: Number(percentage.toFixed(2)),
        title: config.title
    };
  };

  // 加载和计算数据
  useEffect(() => {
    const loadData = async () => {
      if (!config.tableId || !config.targetField || !config.compareField) return;
      
      try {
        console.log('Loading data with config:', config);
        
        const { targetSum, compareSum } = await (dashboard.state === 'View' 
          ? getViewModeData(config)
          : getPreviewModeData(config));

        // 确保数值转换正确
        const targetValue = Number(targetSum);
        const compareValue = Number(compareSum);
        
        console.log(`计算真实数据总和 - 目标: ${targetValue}, 比较: ${compareValue}`);
        
        const result = calculateIndicatorData(targetValue, compareValue);
        console.log(`设置真实数据 - 值: ${result.value}, 百分比: ${result.percentage}%`);
        
        setData(result);
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    };

    // 添加配置变更监听
    if (config.tableId && config.targetField && config.compareField) {
      loadData();
      // 增加 title 变更的监听
      const intervalId = setInterval(loadData, 10000);
      return () => clearInterval(intervalId);
    }
  }, [config.tableId, config.targetField, config.compareField, config.title]);

  return data;
}

// 加载表格列表的Hook
// 修改 useTableList 的返回值结构
export function useTableList(): ISelectOption[] {
  const [tables, setTables] = useState<ISelectOption[]>([]);

  useEffect(() => {
    const loadTables = async () => {
    try {
        const base = await bitable.base;
        const tableList = await base.getTableList();
        
        const tablesWithNames = await Promise.all(
        tableList.map(async (table) => {
            try {
            const tableMeta = await table.getMeta();
            return {
                label: tableMeta.name,
                value: JSON.stringify({
                id: table.id,
                name: tableMeta.name
                })
            };
            } catch (error) {
            console.error('Failed to get table meta:', error);
            const fallbackName = `表格 ${table.id.slice(-4)}`;
            return {
                label: fallbackName,
                value: JSON.stringify({
                id: table.id,
                name: fallbackName
                })
            };
            }
        })
        );
        
        setTables(tablesWithNames);
    } catch (error) {
        console.error('Failed to load tables from bitable:', error);
    }
    };
    
    loadTables();
  }, []);

  return tables;
}

// 修改 useFieldList 的返回值结构
export function useFieldList(tableId: string): ISelectOption[] {
  const [fields, setFields] = useState<ISelectOption[]>([]);

  useEffect(() => {
    const loadFields = async () => {
    if (!tableId) return;
    try {
        const table = await bitable.base.getTableById(tableId);
        const fieldMetaList = await table.getFieldMetaList();
        
        const numberFields = fieldMetaList
        .filter(field => field.type === 2)
        .map(field => ({
            label: field.name,
            value: JSON.stringify({
            id: field.id,
            name: field.name
            })
        }));
        
        setFields(numberFields);
    } catch (error) {
        console.error('Failed to load fields:', error);
    }
    };
    loadFields();
  }, [tableId]);

  return fields;
}