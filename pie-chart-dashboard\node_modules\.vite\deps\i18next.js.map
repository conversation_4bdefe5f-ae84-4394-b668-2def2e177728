{"version": 3, "sources": ["../../i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last && last.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = function (obj, path) {\n  let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code && code.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return this.forward(args, 'log', '', true);\n  }\n  warn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return this.forward(args, 'warn', '', true);\n  }\n  error() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return this.forward(args, 'error', '');\n  }\n  deprecate() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(_ref => {\n        let [observer, numTimesAdded] = _ref;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(_ref2 => {\n        let [observer, numTimesAdded] = _ref2;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value) {\n    let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      silent: false\n    };\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n      silent: false\n    };\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n      silent: false,\n      skipCopy: false\n    };\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    if (this.options.compatibilityAPI === 'v1') return {\n      ...{},\n      ...this.getResource(lng, ns)\n    };\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      if (this.processors[processor]) value = this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nclass Translator extends EventEmitter {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    if (key === undefined || key === null) {\n      return false;\n    }\n    const resolved = this.resolve(key, options);\n    return resolved && resolved.res !== undefined;\n  }\n  extractFromKey(key, options) {\n    let nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let namespaces = options.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !options.keySeparator && !this.options.userDefinedNsSeparator && !options.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, options, lastKey) {\n    if (typeof options !== 'object' && this.options.overloadTranslationOptionHandler) {\n      options = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') options = {\n      ...options\n    };\n    if (!options) options = {};\n    if (keys === undefined || keys === null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = options.returnDetails !== undefined ? options.returnDetails : this.options.returnDetails;\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], options);\n    const namespace = namespaces[namespaces.length - 1];\n    const lng = options.lng || this.language;\n    const appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng && lng.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        const nsSeparator = options.nsSeparator || this.options.nsSeparator;\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(options)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(options)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, options);\n    let res = resolved && resolved.res;\n    const resUsedKey = resolved && resolved.usedKey || key;\n    const resExactUsedKey = resolved && resolved.exactUsedKey || key;\n    const resType = Object.prototype.toString.apply(res);\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const handleAsObject = !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\n    if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(res))) {\n      if (!options.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, {\n          ...options,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(options);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(res);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in res) {\n          if (Object.prototype.hasOwnProperty.call(res, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            copy[m] = this.translate(deepKey, {\n              ...options,\n              ...{\n                joinArrays: false,\n                ns: namespaces\n              }\n            });\n            if (copy[m] === deepKey) copy[m] = res[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, options, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const hasDefaultValue = Translator.hasDefaultValue(options);\n      const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, options) : '';\n      const defaultValueSuffixOrdinalFallback = options.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, {\n        ordinal: false\n      }) : '';\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const defaultValue = needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] || options[`defaultValue${defaultValueSuffix}`] || options[`defaultValue${defaultValueSuffixOrdinalFallback}`] || options.defaultValue;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...options,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n        } else {\n          lngs.push(options.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, options);\n          } else if (this.backendConnector && this.backendConnector.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, options);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, options);\n              if (needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, options[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, options, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = `${namespace}:${key}`;\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        if (this.options.compatibilityAPI !== 'v1') {\n          res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}:${key}` : key, usedDefault ? res : undefined);\n        } else {\n          res = this.options.parseMissingKeyHandler(res);\n        }\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(options);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, options, resolved, lastKey) {\n    var _this = this;\n    if (this.i18nFormat && this.i18nFormat.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...options\n      }, options.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!options.skipInterpolation) {\n      if (options.interpolation) this.interpolator.init({\n        ...options,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...options.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = options.replace && !isString(options.replace) ? options.replace : options;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, options.lng || this.language || resolved.usedLng, options);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) options.nest = false;\n      }\n      if (!options.lng && this.options.compatibilityAPI !== 'v1' && resolved && resolved.res) options.lng = this.language || resolved.usedLng;\n      if (options.nest !== false) res = this.interpolator.nest(res, function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (lastKey && lastKey[0] === args[0] && !options.context) {\n          _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return _this.translate(...args, key);\n      }, options);\n      if (options.interpolation) this.interpolator.reset();\n    }\n    const postProcess = options.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(options)\n        },\n        ...options\n      } : options, this);\n    }\n    return res;\n  }\n  resolve(keys) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, options);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const needsContextHandling = options.context !== undefined && (isString(options.context) || typeof options.context === 'number') && options.context !== '';\n      const codes = options.lngs ? options.lngs : this.languageUtils.toResolveHierarchy(options.lng || this.language, options.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils && this.utils.hasLoadedNamespace && !this.utils.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat && this.i18nFormat.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, options.count, options);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${options.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, options);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nconst capitalize = string => string.charAt(0).toUpperCase() + string.slice(1);\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      if (typeof Intl !== 'undefined' && typeof Intl.getCanonicalLocales !== 'undefined') {\n        try {\n          let formattedCode = Intl.getCanonicalLocales(code)[0];\n          if (formattedCode && this.options.lowerCaseLng) {\n            formattedCode = formattedCode.toLowerCase();\n          }\n          if (formattedCode) return formattedCode;\n        } catch (e) {}\n      }\n      const specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n      let p = code.split('-');\n      if (this.options.lowerCaseLng) {\n        p = p.map(part => part.toLowerCase());\n      } else if (p.length === 2) {\n        p[0] = p[0].toLowerCase();\n        p[1] = p[1].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n      } else if (p.length === 3) {\n        p[0] = p[0].toLowerCase();\n        if (p[1].length === 2) p[1] = p[1].toUpperCase();\n        if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n      }\n      return p.join('-');\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nlet sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nlet _rulesPluralsTypes = {\n  1: n => Number(n > 1),\n  2: n => Number(n != 1),\n  3: n => 0,\n  4: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  5: n => Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5),\n  6: n => Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2),\n  7: n => Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  8: n => Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3),\n  9: n => Number(n >= 2),\n  10: n => Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4),\n  11: n => Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3),\n  12: n => Number(n % 10 != 1 || n % 100 == 11),\n  13: n => Number(n !== 0),\n  14: n => Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3),\n  15: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  16: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2),\n  17: n => Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1),\n  18: n => Number(n == 0 ? 0 : n == 1 ? 1 : 2),\n  19: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3),\n  20: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2),\n  21: n => Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0),\n  22: n => Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3)\n};\nconst nonIntlVersions = ['v1', 'v2', 'v3'];\nconst intlVersions = ['v4'];\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst createRules = () => {\n  const rules = {};\n  sets.forEach(set => {\n    set.lngs.forEach(l => {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n};\nclass PluralResolver {\n  constructor(languageUtils) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    if ((!this.options.compatibilityJSON || intlVersions.includes(this.options.compatibilityJSON)) && (typeof Intl === 'undefined' || !Intl.PluralRules)) {\n      this.options.compatibilityJSON = 'v3';\n      this.logger.error('Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.');\n    }\n    this.rules = createRules();\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (this.shouldUseIntlApi()) {\n      const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n      const type = options.ordinal ? 'ordinal' : 'cardinal';\n      const cacheKey = JSON.stringify({\n        cleanedCode,\n        type\n      });\n      if (cacheKey in this.pluralRulesCache) {\n        return this.pluralRulesCache[cacheKey];\n      }\n      let rule;\n      try {\n        rule = new Intl.PluralRules(cleanedCode, {\n          type\n        });\n      } catch (err) {\n        if (!code.match(/-|_/)) return;\n        const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n        rule = this.getRule(lngPart, options);\n      }\n      this.pluralRulesCache[cacheKey] = rule;\n      return rule;\n    }\n    return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n  }\n  needsPlural(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (this.shouldUseIntlApi()) {\n      return rule && rule.resolvedOptions().pluralCategories.length > 1;\n    }\n    return rule && rule.numbers.length > 1;\n  }\n  getPluralFormsOfKey(code, key) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (!rule) {\n      return [];\n    }\n    if (this.shouldUseIntlApi()) {\n      return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n    }\n    return rule.numbers.map(number => this.getSuffix(code, number, options));\n  }\n  getSuffix(code, count) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const rule = this.getRule(code, options);\n    if (rule) {\n      if (this.shouldUseIntlApi()) {\n        return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n      }\n      return this.getSuffixRetroCompatible(rule, count);\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return '';\n  }\n  getSuffixRetroCompatible(rule, count) {\n    const idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n    let suffix = rule.numbers[idx];\n    if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      if (suffix === 2) {\n        suffix = 'plural';\n      } else if (suffix === 1) {\n        suffix = '';\n      }\n    }\n    const returnSuffix = () => this.options.prepend && suffix.toString() ? this.options.prepend + suffix.toString() : suffix.toString();\n    if (this.options.compatibilityJSON === 'v1') {\n      if (suffix === 1) return '';\n      if (typeof suffix === 'number') return `_plural_${suffix.toString()}`;\n      return returnSuffix();\n    } else if (this.options.compatibilityJSON === 'v2') {\n      return returnSuffix();\n    } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      return returnSuffix();\n    }\n    return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n  }\n  shouldUseIntlApi() {\n    return !nonIntlVersions.includes(this.options.compatibilityJSON);\n  }\n}\n\nconst deepFindWithDefaults = function (data, defaultData, key) {\n  let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '.';\n  let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options.interpolation && options.interpolation.format || (value => value);\n    this.init(options);\n  }\n  init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp && existingRegExp.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if (matchedSingleQuotes && matchedSingleQuotes.length % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (val, lng, options) => {\n    let optForCache = options;\n    if (options && options.interpolationkey && options.formatParams && options.formatParams[options.interpolationkey] && options[options.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [options.interpolationkey]: undefined\n      };\n    }\n    const key = lng + JSON.stringify(optForCache);\n    let formatter = cache[key];\n    if (!formatter) {\n      formatter = fn(getCleanedCode(lng), options);\n      cache[key] = formatter;\n    }\n    return formatter(val);\n  };\n};\nclass Formatter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.formats = {\n      number: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n    this.init(options);\n  }\n  init(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options && options.formatParams && options.formatParams[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    if (this.backend && this.backend.init) {\n      this.backend.init(services, options.backend, options);\n    }\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName) {\n    let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n    let callback = arguments.length > 5 ? arguments[5] : undefined;\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let callback = arguments.length > 3 ? arguments[3] : undefined;\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n    let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : () => {};\n    if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend && this.backend.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initImmediate: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  }\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initImmediate) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init() {\n    var _this = this;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!options.defaultNS && options.defaultNS !== false && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    if (this.options.compatibilityAPI !== 'v1') {\n      this.options.interpolation = {\n        ...defOpts.interpolation,\n        ...this.options.interpolation\n      };\n    }\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else if (typeof Intl !== 'undefined') {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        compatibilityJSON: this.options.compatibilityJSON,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', function (event) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        _this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', function (event) {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        _this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = function () {\n        return _this.store[fcName](...arguments);\n      };\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = function () {\n        _this.store[fcName](...arguments);\n        return _this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && this.options.compatibilityAPI !== 'v1' && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initImmediate) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng && usedLng.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      if (this.options.preload) {\n        this.options.preload.forEach(l => append(l));\n      }\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n  }\n  changeLanguage(lng, callback) {\n    var _this2 = this;\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        setLngProps(l);\n        this.translator.changeLanguage(l);\n        this.isLanguageChangingTo = undefined;\n        this.emit('languageChanged', l);\n        this.logger.log('languageChanged', l);\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve(function () {\n        return _this2.t(...arguments);\n      });\n      if (callback) callback(err, function () {\n        return _this2.t(...arguments);\n      });\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const l = isString(lngs) ? lngs : this.services.languageUtils.getBestMatchFromCodes(lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        if (this.services.languageDetector && this.services.languageDetector.cacheUserLanguage) this.services.languageDetector.cacheUserLanguage(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    var _this3 = this;\n    const fixedT = function (key, opts) {\n      let options;\n      if (typeof opts !== 'object') {\n        for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n          rest[_key3 - 2] = arguments[_key3];\n        }\n        options = _this3.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        options = {\n          ...opts\n        };\n      }\n      options.lng = options.lng || fixedT.lng;\n      options.lngs = options.lngs || fixedT.lngs;\n      options.ns = options.ns || fixedT.ns;\n      if (options.keyPrefix !== '') options.keyPrefix = options.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = _this3.options.keySeparator || '.';\n      let resultKey;\n      if (options.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${options.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = options.keyPrefix ? `${options.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return _this3.t(resultKey, options);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t() {\n    return this.translator && this.translator.translate(...arguments);\n  }\n  exists() {\n    return this.translator && this.translator.exists(...arguments);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages && this.languages.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services && this.services.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    return new I18n(options, callback);\n  }\n  cloneInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      clone.store = new ResourceStore(this.store.data, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', function (event) {\n      for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        args[_key4 - 1] = arguments[_key4];\n      }\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "mappings": ";;;AAAA,IAAM,WAAW,SAAO,OAAO,QAAQ;AACvC,IAAM,QAAQ,MAAM;AAClB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,UAAM;AACN,UAAM;AAAA,EACR,CAAC;AACD,UAAQ,UAAU;AAClB,UAAQ,SAAS;AACjB,SAAO;AACT;AACA,IAAM,aAAa,YAAU;AAC3B,MAAI,UAAU,KAAM,QAAO;AAC3B,SAAO,KAAK;AACd;AACA,IAAM,OAAO,CAAC,GAAG,GAAGA,OAAM;AACxB,IAAE,QAAQ,OAAK;AACb,QAAI,EAAE,CAAC,EAAG,CAAAA,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACtB,CAAC;AACH;AACA,IAAM,4BAA4B;AAClC,IAAM,WAAW,SAAO,OAAO,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ,2BAA2B,GAAG,IAAI;AACvG,IAAM,uBAAuB,YAAU,CAAC,UAAU,SAAS,MAAM;AACjE,IAAM,gBAAgB,CAAC,QAAQ,MAAM,UAAU;AAC7C,QAAM,QAAQ,CAAC,SAAS,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG;AACrD,MAAI,aAAa;AACjB,SAAO,aAAa,MAAM,SAAS,GAAG;AACpC,QAAI,qBAAqB,MAAM,EAAG,QAAO,CAAC;AAC1C,UAAM,MAAM,SAAS,MAAM,UAAU,CAAC;AACtC,QAAI,CAAC,OAAO,GAAG,KAAK,MAAO,QAAO,GAAG,IAAI,IAAI,MAAM;AACnD,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,eAAS,OAAO,GAAG;AAAA,IACrB,OAAO;AACL,eAAS,CAAC;AAAA,IACZ;AACA,MAAE;AAAA,EACJ;AACA,MAAI,qBAAqB,MAAM,EAAG,QAAO,CAAC;AAC1C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAG,SAAS,MAAM,UAAU,CAAC;AAAA,EAC/B;AACF;AACA,IAAM,UAAU,CAAC,QAAQ,MAAM,aAAa;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,MAAM,MAAM;AACtC,MAAI,QAAQ,UAAa,KAAK,WAAW,GAAG;AAC1C,QAAI,CAAC,IAAI;AACT;AAAA,EACF;AACA,MAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,MAAI,IAAI,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AACrC,MAAI,OAAO,cAAc,QAAQ,GAAG,MAAM;AAC1C,SAAO,KAAK,QAAQ,UAAa,EAAE,QAAQ;AACzC,QAAI,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;AAC3B,QAAI,EAAE,MAAM,GAAG,EAAE,SAAS,CAAC;AAC3B,WAAO,cAAc,QAAQ,GAAG,MAAM;AACtC,QAAI,QAAQ,KAAK,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,aAAa;AACzE,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,OAAK,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B;AACA,IAAM,WAAW,CAAC,QAAQ,MAAM,UAAU,WAAW;AACnD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,MAAM,MAAM;AACtC,MAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;AACpB,MAAI,CAAC,EAAE,KAAK,QAAQ;AACtB;AACA,IAAM,UAAU,CAAC,QAAQ,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,IAAI;AAC9B,MAAI,CAAC,IAAK,QAAO;AACjB,SAAO,IAAI,CAAC;AACd;AACA,IAAM,sBAAsB,CAAC,MAAM,aAAa,QAAQ;AACtD,QAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,MAAI,UAAU,QAAW;AACvB,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,aAAa,GAAG;AACjC;AACA,IAAM,aAAa,CAAC,QAAQ,QAAQ,cAAc;AAChD,aAAW,QAAQ,QAAQ;AACzB,QAAI,SAAS,eAAe,SAAS,eAAe;AAClD,UAAI,QAAQ,QAAQ;AAClB,YAAI,SAAS,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,aAAa,UAAU,SAAS,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,aAAa,QAAQ;AACxH,cAAI,UAAW,QAAO,IAAI,IAAI,OAAO,IAAI;AAAA,QAC3C,OAAO;AACL,qBAAW,OAAO,IAAI,GAAG,OAAO,IAAI,GAAG,SAAS;AAAA,QAClD;AAAA,MACF,OAAO;AACL,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,cAAc,SAAO,IAAI,QAAQ,uCAAuC,MAAM;AACpF,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,SAAS,UAAQ;AACrB,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,KAAK,QAAQ,cAAc,OAAK,WAAW,CAAC,CAAC;AAAA,EACtD;AACA,SAAO;AACT;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,kBAAkB,KAAK,UAAU,IAAI,OAAO;AAClD,QAAI,oBAAoB,QAAW;AACjC,aAAO;AAAA,IACT;AACA,UAAM,YAAY,IAAI,OAAO,OAAO;AACpC,QAAI,KAAK,YAAY,WAAW,KAAK,UAAU;AAC7C,WAAK,UAAU,OAAO,KAAK,YAAY,MAAM,CAAC;AAAA,IAChD;AACA,SAAK,UAAU,IAAI,SAAS,SAAS;AACrC,SAAK,YAAY,KAAK,OAAO;AAC7B,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AACtC,IAAM,iCAAiC,IAAI,YAAY,EAAE;AACzD,IAAM,sBAAsB,CAAC,KAAK,aAAa,iBAAiB;AAC9D,gBAAc,eAAe;AAC7B,iBAAe,gBAAgB;AAC/B,QAAM,gBAAgB,MAAM,OAAO,OAAK,YAAY,QAAQ,CAAC,IAAI,KAAK,aAAa,QAAQ,CAAC,IAAI,CAAC;AACjG,MAAI,cAAc,WAAW,EAAG,QAAO;AACvC,QAAM,IAAI,+BAA+B,UAAU,IAAI,cAAc,IAAI,OAAK,MAAM,MAAM,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG;AACjH,MAAI,UAAU,CAAC,EAAE,KAAK,GAAG;AACzB,MAAI,CAAC,SAAS;AACZ,UAAM,KAAK,IAAI,QAAQ,YAAY;AACnC,QAAI,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,UAAU,GAAG,EAAE,CAAC,GAAG;AAC3C,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,WAAW,SAAU,KAAK,MAAM;AACpC,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACvF,MAAI,CAAC,IAAK,QAAO;AACjB,MAAI,IAAI,IAAI,EAAG,QAAO,IAAI,IAAI;AAC9B,QAAM,SAAS,KAAK,MAAM,YAAY;AACtC,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAClC,QAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,aAAO;AAAA,IACT;AACA,QAAI;AACJ,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,UAAI,MAAM,GAAG;AACX,oBAAY;AAAA,MACd;AACA,kBAAY,OAAO,CAAC;AACpB,aAAO,QAAQ,QAAQ;AACvB,UAAI,SAAS,QAAW;AACtB,YAAI,CAAC,UAAU,UAAU,SAAS,EAAE,QAAQ,OAAO,IAAI,IAAI,MAAM,IAAI,OAAO,SAAS,GAAG;AACtF;AAAA,QACF;AACA,aAAK,IAAI,IAAI;AACb;AAAA,MACF;AAAA,IACF;AACA,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,UAAQ,QAAQ,KAAK,QAAQ,KAAK,GAAG;AAE5D,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,IAAI,MAAM;AACR,SAAK,OAAO,OAAO,IAAI;AAAA,EACzB;AAAA,EACA,KAAK,MAAM;AACT,SAAK,OAAO,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,MAAM;AACV,SAAK,OAAO,SAAS,IAAI;AAAA,EAC3B;AAAA,EACA,OAAO,MAAM,MAAM;AACjB,QAAI,WAAW,QAAQ,IAAI,EAAG,SAAQ,IAAI,EAAE,MAAM,SAAS,IAAI;AAAA,EACjE;AACF;AACA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,YAAY,gBAAgB;AAC1B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,KAAK,gBAAgB,OAAO;AAAA,EACnC;AAAA,EACA,KAAK,gBAAgB;AACnB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,SAAS,QAAQ,UAAU;AAChC,SAAK,SAAS,kBAAkB;AAChC,SAAK,UAAU;AACf,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EACA,MAAM;AACJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,QAAQ,MAAM,OAAO,IAAI,IAAI;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,WAAO,KAAK,QAAQ,MAAM,QAAQ,IAAI,IAAI;AAAA,EAC5C;AAAA,EACA,QAAQ;AACN,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,WAAO,KAAK,QAAQ,MAAM,SAAS,EAAE;AAAA,EACvC;AAAA,EACA,YAAY;AACV,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,WAAO,KAAK,QAAQ,MAAM,QAAQ,wBAAwB,IAAI;AAAA,EAChE;AAAA,EACA,QAAQ,MAAM,KAAK,QAAQ,WAAW;AACpC,QAAI,aAAa,CAAC,KAAK,MAAO,QAAO;AACrC,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,MAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC;AACnE,WAAO,KAAK,OAAO,GAAG,EAAE,IAAI;AAAA,EAC9B;AAAA,EACA,OAAO,YAAY;AACjB,WAAO,IAAI,QAAO,KAAK,QAAQ;AAAA,MAC7B,GAAG;AAAA,QACD,QAAQ,GAAG,KAAK,MAAM,IAAI,UAAU;AAAA,MACtC;AAAA,MACA,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,MAAM,SAAS;AACb,cAAU,WAAW,KAAK;AAC1B,YAAQ,SAAS,QAAQ,UAAU,KAAK;AACxC,WAAO,IAAI,QAAO,KAAK,QAAQ,OAAO;AAAA,EACxC;AACF;AACA,IAAI,aAAa,IAAI,OAAO;AAE5B,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,GAAG,QAAQ,UAAU;AACnB,WAAO,MAAM,GAAG,EAAE,QAAQ,WAAS;AACjC,UAAI,CAAC,KAAK,UAAU,KAAK,EAAG,MAAK,UAAU,KAAK,IAAI,oBAAI,IAAI;AAC5D,YAAM,eAAe,KAAK,UAAU,KAAK,EAAE,IAAI,QAAQ,KAAK;AAC5D,WAAK,UAAU,KAAK,EAAE,IAAI,UAAU,eAAe,CAAC;AAAA,IACtD,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO,UAAU;AACnB,QAAI,CAAC,KAAK,UAAU,KAAK,EAAG;AAC5B,QAAI,CAAC,UAAU;AACb,aAAO,KAAK,UAAU,KAAK;AAC3B;AAAA,IACF;AACA,SAAK,UAAU,KAAK,EAAE,OAAO,QAAQ;AAAA,EACvC;AAAA,EACA,KAAK,OAAO;AACV,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AACA,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,YAAM,SAAS,MAAM,KAAK,KAAK,UAAU,KAAK,EAAE,QAAQ,CAAC;AACzD,aAAO,QAAQ,UAAQ;AACrB,YAAI,CAAC,UAAU,aAAa,IAAI;AAChC,iBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,mBAAS,GAAG,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU,GAAG,GAAG;AACvB,YAAM,SAAS,MAAM,KAAK,KAAK,UAAU,GAAG,EAAE,QAAQ,CAAC;AACvD,aAAO,QAAQ,WAAS;AACtB,YAAI,CAAC,UAAU,aAAa,IAAI;AAChC,iBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,mBAAS,MAAM,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAM,gBAAN,cAA4B,aAAa;AAAA,EACvC,YAAY,MAAM;AAChB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,IAAI,CAAC,aAAa;AAAA,MAClB,WAAW;AAAA,IACb;AACA,UAAM;AACN,SAAK,OAAO,QAAQ,CAAC;AACrB,SAAK,UAAU;AACf,QAAI,KAAK,QAAQ,iBAAiB,QAAW;AAC3C,WAAK,QAAQ,eAAe;AAAA,IAC9B;AACA,QAAI,KAAK,QAAQ,wBAAwB,QAAW;AAClD,WAAK,QAAQ,sBAAsB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,cAAc,IAAI;AAChB,QAAI,KAAK,QAAQ,GAAG,QAAQ,EAAE,IAAI,GAAG;AACnC,WAAK,QAAQ,GAAG,KAAK,EAAE;AAAA,IACzB;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,UAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,EAAE;AACxC,QAAI,QAAQ,IAAI;AACd,WAAK,QAAQ,GAAG,OAAO,OAAO,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,YAAY,KAAK,IAAI,KAAK;AACxB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,KAAK,QAAQ;AAC9F,UAAM,sBAAsB,QAAQ,wBAAwB,SAAY,QAAQ,sBAAsB,KAAK,QAAQ;AACnH,QAAI;AACJ,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AAAA,IACtB,OAAO;AACL,aAAO,CAAC,KAAK,EAAE;AACf,UAAI,KAAK;AACP,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAK,KAAK,GAAG,GAAG;AAAA,QAClB,WAAW,SAAS,GAAG,KAAK,cAAc;AACxC,eAAK,KAAK,GAAG,IAAI,MAAM,YAAY,CAAC;AAAA,QACtC,OAAO;AACL,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,KAAK,MAAM,IAAI;AACtC,QAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,IAAI,QAAQ,GAAG,IAAI,IAAI;AACnD,YAAM,KAAK,CAAC;AACZ,WAAK,KAAK,CAAC;AACX,YAAM,KAAK,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,IAC9B;AACA,QAAI,UAAU,CAAC,uBAAuB,CAAC,SAAS,GAAG,EAAG,QAAO;AAC7D,WAAO,SAAS,KAAK,QAAQ,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,EAAE,EAAE,GAAG,KAAK,YAAY;AAAA,EACtF;AAAA,EACA,YAAY,KAAK,IAAI,KAAK,OAAO;AAC/B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,QAAQ;AAAA,IACV;AACA,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,KAAK,QAAQ;AAC9F,QAAI,OAAO,CAAC,KAAK,EAAE;AACnB,QAAI,IAAK,QAAO,KAAK,OAAO,eAAe,IAAI,MAAM,YAAY,IAAI,GAAG;AACxE,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AACpB,cAAQ;AACR,WAAK,KAAK,CAAC;AAAA,IACb;AACA,SAAK,cAAc,EAAE;AACrB,YAAQ,KAAK,MAAM,MAAM,KAAK;AAC9B,QAAI,CAAC,QAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7D;AAAA,EACA,aAAa,KAAK,IAAI,WAAW;AAC/B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,QAAQ;AAAA,IACV;AACA,eAAW,KAAK,WAAW;AACzB,UAAI,SAAS,UAAU,CAAC,CAAC,KAAK,MAAM,QAAQ,UAAU,CAAC,CAAC,EAAG,MAAK,YAAY,KAAK,IAAI,GAAG,UAAU,CAAC,GAAG;AAAA,QACpG,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,QAAI,CAAC,QAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB,KAAK,IAAI,WAAW,MAAM,WAAW;AACrD,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AACA,QAAI,OAAO,CAAC,KAAK,EAAE;AACnB,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AACpB,aAAO;AACP,kBAAY;AACZ,WAAK,KAAK,CAAC;AAAA,IACb;AACA,SAAK,cAAc,EAAE;AACrB,QAAI,OAAO,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC;AACxC,QAAI,CAAC,QAAQ,SAAU,aAAY,KAAK,MAAM,KAAK,UAAU,SAAS,CAAC;AACvE,QAAI,MAAM;AACR,iBAAW,MAAM,WAAW,SAAS;AAAA,IACvC,OAAO;AACL,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AACA,YAAQ,KAAK,MAAM,MAAM,IAAI;AAC7B,QAAI,CAAC,QAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,SAAS;AAAA,EAC5D;AAAA,EACA,qBAAqB,KAAK,IAAI;AAC5B,QAAI,KAAK,kBAAkB,KAAK,EAAE,GAAG;AACnC,aAAO,KAAK,KAAK,GAAG,EAAE,EAAE;AAAA,IAC1B;AACA,SAAK,iBAAiB,EAAE;AACxB,SAAK,KAAK,WAAW,KAAK,EAAE;AAAA,EAC9B;AAAA,EACA,kBAAkB,KAAK,IAAI;AACzB,WAAO,KAAK,YAAY,KAAK,EAAE,MAAM;AAAA,EACvC;AAAA,EACA,kBAAkB,KAAK,IAAI;AACzB,QAAI,CAAC,GAAI,MAAK,KAAK,QAAQ;AAC3B,QAAI,KAAK,QAAQ,qBAAqB,KAAM,QAAO;AAAA,MACjD,GAAG,CAAC;AAAA,MACJ,GAAG,KAAK,YAAY,KAAK,EAAE;AAAA,IAC7B;AACA,WAAO,KAAK,YAAY,KAAK,EAAE;AAAA,EACjC;AAAA,EACA,kBAAkB,KAAK;AACrB,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA,EACA,4BAA4B,KAAK;AAC/B,UAAM,OAAO,KAAK,kBAAkB,GAAG;AACvC,UAAM,IAAI,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC;AACxC,WAAO,CAAC,CAAC,EAAE,KAAK,OAAK,KAAK,CAAC,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;AAAA,EACjE;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,iBAAiB,QAAQ;AACvB,SAAK,WAAW,OAAO,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,OAAO,YAAY,OAAO,KAAK,SAAS,YAAY;AAClD,eAAW,QAAQ,eAAa;AAC9B,UAAI,KAAK,WAAW,SAAS,EAAG,SAAQ,KAAK,WAAW,SAAS,EAAE,QAAQ,OAAO,KAAK,SAAS,UAAU;AAAA,IAC5G,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,aAAN,MAAM,oBAAmB,aAAa;AAAA,EACpC,YAAY,UAAU;AACpB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM;AACN,SAAK,CAAC,iBAAiB,iBAAiB,kBAAkB,gBAAgB,oBAAoB,cAAc,OAAO,GAAG,UAAU,IAAI;AACpI,SAAK,UAAU;AACf,QAAI,KAAK,QAAQ,iBAAiB,QAAW;AAC3C,WAAK,QAAQ,eAAe;AAAA,IAC9B;AACA,SAAK,SAAS,WAAW,OAAO,YAAY;AAAA,EAC9C;AAAA,EACA,eAAe,KAAK;AAClB,QAAI,IAAK,MAAK,WAAW;AAAA,EAC3B;AAAA,EACA,OAAO,KAAK;AACV,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,eAAe,CAAC;AAAA,IAClB;AACA,QAAI,QAAQ,UAAa,QAAQ,MAAM;AACrC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,QAAQ,KAAK,OAAO;AAC1C,WAAO,YAAY,SAAS,QAAQ;AAAA,EACtC;AAAA,EACA,eAAe,KAAK,SAAS;AAC3B,QAAI,cAAc,QAAQ,gBAAgB,SAAY,QAAQ,cAAc,KAAK,QAAQ;AACzF,QAAI,gBAAgB,OAAW,eAAc;AAC7C,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,KAAK,QAAQ;AAC9F,QAAI,aAAa,QAAQ,MAAM,KAAK,QAAQ,aAAa,CAAC;AAC1D,UAAM,uBAAuB,eAAe,IAAI,QAAQ,WAAW,IAAI;AACvE,UAAM,uBAAuB,CAAC,KAAK,QAAQ,2BAA2B,CAAC,QAAQ,gBAAgB,CAAC,KAAK,QAAQ,0BAA0B,CAAC,QAAQ,eAAe,CAAC,oBAAoB,KAAK,aAAa,YAAY;AAClN,QAAI,wBAAwB,CAAC,sBAAsB;AACjD,YAAM,IAAI,IAAI,MAAM,KAAK,aAAa,aAAa;AACnD,UAAI,KAAK,EAAE,SAAS,GAAG;AACrB,eAAO;AAAA,UACL;AAAA,UACA,YAAY,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI;AAAA,QACpD;AAAA,MACF;AACA,YAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,UAAI,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,QAAQ,GAAG,QAAQ,MAAM,CAAC,CAAC,IAAI,GAAI,cAAa,MAAM,MAAM;AACrI,YAAM,MAAM,KAAK,YAAY;AAAA,IAC/B;AACA,WAAO;AAAA,MACL;AAAA,MACA,YAAY,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,UAAU,MAAM,SAAS,SAAS;AAChC,QAAI,OAAO,YAAY,YAAY,KAAK,QAAQ,kCAAkC;AAChF,gBAAU,KAAK,QAAQ,iCAAiC,SAAS;AAAA,IACnE;AACA,QAAI,OAAO,YAAY,SAAU,WAAU;AAAA,MACzC,GAAG;AAAA,IACL;AACA,QAAI,CAAC,QAAS,WAAU,CAAC;AACzB,QAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,QAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO,CAAC,OAAO,IAAI,CAAC;AAC9C,UAAM,gBAAgB,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB,KAAK,QAAQ;AACjG,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,KAAK,QAAQ;AAC9F,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,eAAe,KAAK,KAAK,SAAS,CAAC,GAAG,OAAO;AACtD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,UAAM,MAAM,QAAQ,OAAO,KAAK;AAChC,UAAM,0BAA0B,QAAQ,2BAA2B,KAAK,QAAQ;AAChF,QAAI,OAAO,IAAI,YAAY,MAAM,UAAU;AACzC,UAAI,yBAAyB;AAC3B,cAAM,cAAc,QAAQ,eAAe,KAAK,QAAQ;AACxD,YAAI,eAAe;AACjB,iBAAO;AAAA,YACL,KAAK,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG;AAAA,YACrC,SAAS;AAAA,YACT,cAAc;AAAA,YACd,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,YAAY,KAAK,qBAAqB,OAAO;AAAA,UAC/C;AAAA,QACF;AACA,eAAO,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,eAAO;AAAA,UACL,KAAK;AAAA,UACL,SAAS;AAAA,UACT,cAAc;AAAA,UACd,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,YAAY,KAAK,qBAAqB,OAAO;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,QAAQ,MAAM,OAAO;AAC3C,QAAI,MAAM,YAAY,SAAS;AAC/B,UAAM,aAAa,YAAY,SAAS,WAAW;AACnD,UAAM,kBAAkB,YAAY,SAAS,gBAAgB;AAC7D,UAAM,UAAU,OAAO,UAAU,SAAS,MAAM,GAAG;AACnD,UAAM,WAAW,CAAC,mBAAmB,qBAAqB,iBAAiB;AAC3E,UAAM,aAAa,QAAQ,eAAe,SAAY,QAAQ,aAAa,KAAK,QAAQ;AACxF,UAAM,6BAA6B,CAAC,KAAK,cAAc,KAAK,WAAW;AACvE,UAAM,iBAAiB,CAAC,SAAS,GAAG,KAAK,OAAO,QAAQ,aAAa,OAAO,QAAQ;AACpF,QAAI,8BAA8B,OAAO,kBAAkB,SAAS,QAAQ,OAAO,IAAI,KAAK,EAAE,SAAS,UAAU,KAAK,MAAM,QAAQ,GAAG,IAAI;AACzI,UAAI,CAAC,QAAQ,iBAAiB,CAAC,KAAK,QAAQ,eAAe;AACzD,YAAI,CAAC,KAAK,QAAQ,uBAAuB;AACvC,eAAK,OAAO,KAAK,iEAAiE;AAAA,QACpF;AACA,cAAM,IAAI,KAAK,QAAQ,wBAAwB,KAAK,QAAQ,sBAAsB,YAAY,KAAK;AAAA,UACjG,GAAG;AAAA,UACH,IAAI;AAAA,QACN,CAAC,IAAI,QAAQ,GAAG,KAAK,KAAK,QAAQ;AAClC,YAAI,eAAe;AACjB,mBAAS,MAAM;AACf,mBAAS,aAAa,KAAK,qBAAqB,OAAO;AACvD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,UAAI,cAAc;AAChB,cAAM,iBAAiB,MAAM,QAAQ,GAAG;AACxC,cAAMC,QAAO,iBAAiB,CAAC,IAAI,CAAC;AACpC,cAAM,cAAc,iBAAiB,kBAAkB;AACvD,mBAAW,KAAK,KAAK;AACnB,cAAI,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,GAAG;AAChD,kBAAM,UAAU,GAAG,WAAW,GAAG,YAAY,GAAG,CAAC;AACjD,YAAAA,MAAK,CAAC,IAAI,KAAK,UAAU,SAAS;AAAA,cAChC,GAAG;AAAA,cACH,GAAG;AAAA,gBACD,YAAY;AAAA,gBACZ,IAAI;AAAA,cACN;AAAA,YACF,CAAC;AACD,gBAAIA,MAAK,CAAC,MAAM,QAAS,CAAAA,MAAK,CAAC,IAAI,IAAI,CAAC;AAAA,UAC1C;AAAA,QACF;AACA,cAAMA;AAAA,MACR;AAAA,IACF,WAAW,8BAA8B,SAAS,UAAU,KAAK,MAAM,QAAQ,GAAG,GAAG;AACnF,YAAM,IAAI,KAAK,UAAU;AACzB,UAAI,IAAK,OAAM,KAAK,kBAAkB,KAAK,MAAM,SAAS,OAAO;AAAA,IACnE,OAAO;AACL,UAAI,cAAc;AAClB,UAAI,UAAU;AACd,YAAM,sBAAsB,QAAQ,UAAU,UAAa,CAAC,SAAS,QAAQ,KAAK;AAClF,YAAM,kBAAkB,YAAW,gBAAgB,OAAO;AAC1D,YAAM,qBAAqB,sBAAsB,KAAK,eAAe,UAAU,KAAK,QAAQ,OAAO,OAAO,IAAI;AAC9G,YAAM,oCAAoC,QAAQ,WAAW,sBAAsB,KAAK,eAAe,UAAU,KAAK,QAAQ,OAAO;AAAA,QACnI,SAAS;AAAA,MACX,CAAC,IAAI;AACL,YAAM,wBAAwB,uBAAuB,CAAC,QAAQ,WAAW,QAAQ,UAAU,KAAK,KAAK,eAAe,iBAAiB;AACrI,YAAM,eAAe,yBAAyB,QAAQ,eAAe,KAAK,QAAQ,eAAe,MAAM,KAAK,QAAQ,eAAe,kBAAkB,EAAE,KAAK,QAAQ,eAAe,iCAAiC,EAAE,KAAK,QAAQ;AACnO,UAAI,CAAC,KAAK,cAAc,GAAG,KAAK,iBAAiB;AAC/C,sBAAc;AACd,cAAM;AAAA,MACR;AACA,UAAI,CAAC,KAAK,cAAc,GAAG,GAAG;AAC5B,kBAAU;AACV,cAAM;AAAA,MACR;AACA,YAAM,iCAAiC,QAAQ,kCAAkC,KAAK,QAAQ;AAC9F,YAAM,gBAAgB,kCAAkC,UAAU,SAAY;AAC9E,YAAM,gBAAgB,mBAAmB,iBAAiB,OAAO,KAAK,QAAQ;AAC9E,UAAI,WAAW,eAAe,eAAe;AAC3C,aAAK,OAAO,IAAI,gBAAgB,cAAc,cAAc,KAAK,WAAW,KAAK,gBAAgB,eAAe,GAAG;AACnH,YAAI,cAAc;AAChB,gBAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,YAC3B,GAAG;AAAA,YACH,cAAc;AAAA,UAChB,CAAC;AACD,cAAI,MAAM,GAAG,IAAK,MAAK,OAAO,KAAK,iLAAiL;AAAA,QACtN;AACA,YAAI,OAAO,CAAC;AACZ,cAAM,eAAe,KAAK,cAAc,iBAAiB,KAAK,QAAQ,aAAa,QAAQ,OAAO,KAAK,QAAQ;AAC/G,YAAI,KAAK,QAAQ,kBAAkB,cAAc,gBAAgB,aAAa,CAAC,GAAG;AAChF,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,iBAAK,KAAK,aAAa,CAAC,CAAC;AAAA,UAC3B;AAAA,QACF,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAC/C,iBAAO,KAAK,cAAc,mBAAmB,QAAQ,OAAO,KAAK,QAAQ;AAAA,QAC3E,OAAO;AACL,eAAK,KAAK,QAAQ,OAAO,KAAK,QAAQ;AAAA,QACxC;AACA,cAAM,OAAO,CAAC,GAAG,GAAG,yBAAyB;AAC3C,gBAAM,oBAAoB,mBAAmB,yBAAyB,MAAM,uBAAuB;AACnG,cAAI,KAAK,QAAQ,mBAAmB;AAClC,iBAAK,QAAQ,kBAAkB,GAAG,WAAW,GAAG,mBAAmB,eAAe,OAAO;AAAA,UAC3F,WAAW,KAAK,oBAAoB,KAAK,iBAAiB,aAAa;AACrE,iBAAK,iBAAiB,YAAY,GAAG,WAAW,GAAG,mBAAmB,eAAe,OAAO;AAAA,UAC9F;AACA,eAAK,KAAK,cAAc,GAAG,WAAW,GAAG,GAAG;AAAA,QAC9C;AACA,YAAI,KAAK,QAAQ,aAAa;AAC5B,cAAI,KAAK,QAAQ,sBAAsB,qBAAqB;AAC1D,iBAAK,QAAQ,cAAY;AACvB,oBAAM,WAAW,KAAK,eAAe,YAAY,UAAU,OAAO;AAClE,kBAAI,yBAAyB,QAAQ,eAAe,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,GAAG,KAAK,QAAQ,eAAe,MAAM,IAAI,GAAG;AACtJ,yBAAS,KAAK,GAAG,KAAK,QAAQ,eAAe,MAAM;AAAA,cACrD;AACA,uBAAS,QAAQ,YAAU;AACzB,qBAAK,CAAC,QAAQ,GAAG,MAAM,QAAQ,QAAQ,eAAe,MAAM,EAAE,KAAK,YAAY;AAAA,cACjF,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,MAAM,KAAK,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,YAAM,KAAK,kBAAkB,KAAK,MAAM,SAAS,UAAU,OAAO;AAClE,UAAI,WAAW,QAAQ,OAAO,KAAK,QAAQ,4BAA6B,OAAM,GAAG,SAAS,IAAI,GAAG;AACjG,WAAK,WAAW,gBAAgB,KAAK,QAAQ,wBAAwB;AACnE,YAAI,KAAK,QAAQ,qBAAqB,MAAM;AAC1C,gBAAM,KAAK,QAAQ,uBAAuB,KAAK,QAAQ,8BAA8B,GAAG,SAAS,IAAI,GAAG,KAAK,KAAK,cAAc,MAAM,MAAS;AAAA,QACjJ,OAAO;AACL,gBAAM,KAAK,QAAQ,uBAAuB,GAAG;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe;AACjB,eAAS,MAAM;AACf,eAAS,aAAa,KAAK,qBAAqB,OAAO;AACvD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,KAAK,KAAK,SAAS,UAAU,SAAS;AACtD,QAAI,QAAQ;AACZ,QAAI,KAAK,cAAc,KAAK,WAAW,OAAO;AAC5C,YAAM,KAAK,WAAW,MAAM,KAAK;AAAA,QAC/B,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL,GAAG,QAAQ,OAAO,KAAK,YAAY,SAAS,SAAS,SAAS,QAAQ,SAAS,SAAS;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH,WAAW,CAAC,QAAQ,mBAAmB;AACrC,UAAI,QAAQ,cAAe,MAAK,aAAa,KAAK;AAAA,QAChD,GAAG;AAAA,QACH,GAAG;AAAA,UACD,eAAe;AAAA,YACb,GAAG,KAAK,QAAQ;AAAA,YAChB,GAAG,QAAQ;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,kBAAkB,SAAS,GAAG,MAAM,WAAW,QAAQ,iBAAiB,QAAQ,cAAc,oBAAoB,SAAY,QAAQ,cAAc,kBAAkB,KAAK,QAAQ,cAAc;AACvM,UAAI;AACJ,UAAI,iBAAiB;AACnB,cAAM,KAAK,IAAI,MAAM,KAAK,aAAa,aAAa;AACpD,kBAAU,MAAM,GAAG;AAAA,MACrB;AACA,UAAI,OAAO,QAAQ,WAAW,CAAC,SAAS,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC7E,UAAI,KAAK,QAAQ,cAAc,iBAAkB,QAAO;AAAA,QACtD,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL;AACA,YAAM,KAAK,aAAa,YAAY,KAAK,MAAM,QAAQ,OAAO,KAAK,YAAY,SAAS,SAAS,OAAO;AACxG,UAAI,iBAAiB;AACnB,cAAM,KAAK,IAAI,MAAM,KAAK,aAAa,aAAa;AACpD,cAAM,UAAU,MAAM,GAAG;AACzB,YAAI,UAAU,QAAS,SAAQ,OAAO;AAAA,MACxC;AACA,UAAI,CAAC,QAAQ,OAAO,KAAK,QAAQ,qBAAqB,QAAQ,YAAY,SAAS,IAAK,SAAQ,MAAM,KAAK,YAAY,SAAS;AAChI,UAAI,QAAQ,SAAS,MAAO,OAAM,KAAK,aAAa,KAAK,KAAK,WAAY;AACxE,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,YAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,QAAQ,SAAS;AACzD,gBAAM,OAAO,KAAK,6CAA6C,KAAK,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE;AAC1F,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,UAAU,GAAG,MAAM,GAAG;AAAA,MACrC,GAAG,OAAO;AACV,UAAI,QAAQ,cAAe,MAAK,aAAa,MAAM;AAAA,IACrD;AACA,UAAM,cAAc,QAAQ,eAAe,KAAK,QAAQ;AACxD,UAAM,qBAAqB,SAAS,WAAW,IAAI,CAAC,WAAW,IAAI;AACnE,QAAI,QAAQ,UAAa,QAAQ,QAAQ,sBAAsB,mBAAmB,UAAU,QAAQ,uBAAuB,OAAO;AAChI,YAAM,cAAc,OAAO,oBAAoB,KAAK,KAAK,KAAK,WAAW,KAAK,QAAQ,0BAA0B;AAAA,QAC9G,cAAc;AAAA,UACZ,GAAG;AAAA,UACH,YAAY,KAAK,qBAAqB,OAAO;AAAA,QAC/C;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,IAAI;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,MAAM;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,IAAI,EAAG,QAAO,CAAC,IAAI;AAChC,SAAK,QAAQ,OAAK;AAChB,UAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,YAAM,YAAY,KAAK,eAAe,GAAG,OAAO;AAChD,YAAM,MAAM,UAAU;AACtB,gBAAU;AACV,UAAI,aAAa,UAAU;AAC3B,UAAI,KAAK,QAAQ,WAAY,cAAa,WAAW,OAAO,KAAK,QAAQ,UAAU;AACnF,YAAM,sBAAsB,QAAQ,UAAU,UAAa,CAAC,SAAS,QAAQ,KAAK;AAClF,YAAM,wBAAwB,uBAAuB,CAAC,QAAQ,WAAW,QAAQ,UAAU,KAAK,KAAK,eAAe,iBAAiB;AACrI,YAAM,uBAAuB,QAAQ,YAAY,WAAc,SAAS,QAAQ,OAAO,KAAK,OAAO,QAAQ,YAAY,aAAa,QAAQ,YAAY;AACxJ,YAAM,QAAQ,QAAQ,OAAO,QAAQ,OAAO,KAAK,cAAc,mBAAmB,QAAQ,OAAO,KAAK,UAAU,QAAQ,WAAW;AACnI,iBAAW,QAAQ,QAAM;AACvB,YAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,iBAAS;AACT,YAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,SAAS,KAAK,MAAM,sBAAsB,CAAC,KAAK,MAAM,mBAAmB,MAAM,GAAG;AACnI,2BAAiB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI;AACxC,eAAK,OAAO,KAAK,QAAQ,OAAO,oBAAoB,MAAM,KAAK,IAAI,CAAC,sCAAsC,MAAM,wBAAwB,0NAA0N;AAAA,QACpW;AACA,cAAM,QAAQ,UAAQ;AACpB,cAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,oBAAU;AACV,gBAAM,YAAY,CAAC,GAAG;AACtB,cAAI,KAAK,cAAc,KAAK,WAAW,eAAe;AACpD,iBAAK,WAAW,cAAc,WAAW,KAAK,MAAM,IAAI,OAAO;AAAA,UACjE,OAAO;AACL,gBAAI;AACJ,gBAAI,oBAAqB,gBAAe,KAAK,eAAe,UAAU,MAAM,QAAQ,OAAO,OAAO;AAClG,kBAAM,aAAa,GAAG,KAAK,QAAQ,eAAe;AAClD,kBAAM,gBAAgB,GAAG,KAAK,QAAQ,eAAe,UAAU,KAAK,QAAQ,eAAe;AAC3F,gBAAI,qBAAqB;AACvB,wBAAU,KAAK,MAAM,YAAY;AACjC,kBAAI,QAAQ,WAAW,aAAa,QAAQ,aAAa,MAAM,GAAG;AAChE,0BAAU,KAAK,MAAM,aAAa,QAAQ,eAAe,KAAK,QAAQ,eAAe,CAAC;AAAA,cACxF;AACA,kBAAI,uBAAuB;AACzB,0BAAU,KAAK,MAAM,UAAU;AAAA,cACjC;AAAA,YACF;AACA,gBAAI,sBAAsB;AACxB,oBAAM,aAAa,GAAG,GAAG,GAAG,KAAK,QAAQ,gBAAgB,GAAG,QAAQ,OAAO;AAC3E,wBAAU,KAAK,UAAU;AACzB,kBAAI,qBAAqB;AACvB,0BAAU,KAAK,aAAa,YAAY;AACxC,oBAAI,QAAQ,WAAW,aAAa,QAAQ,aAAa,MAAM,GAAG;AAChE,4BAAU,KAAK,aAAa,aAAa,QAAQ,eAAe,KAAK,QAAQ,eAAe,CAAC;AAAA,gBAC/F;AACA,oBAAI,uBAAuB;AACzB,4BAAU,KAAK,aAAa,UAAU;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI;AACJ,iBAAO,cAAc,UAAU,IAAI,GAAG;AACpC,gBAAI,CAAC,KAAK,cAAc,KAAK,GAAG;AAC9B,6BAAe;AACf,sBAAQ,KAAK,YAAY,MAAM,IAAI,aAAa,OAAO;AAAA,YACzD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,KAAK;AACjB,WAAO,QAAQ,UAAa,EAAE,CAAC,KAAK,QAAQ,cAAc,QAAQ,SAAS,EAAE,CAAC,KAAK,QAAQ,qBAAqB,QAAQ;AAAA,EAC1H;AAAA,EACA,YAAY,MAAM,IAAI,KAAK;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,KAAK,cAAc,KAAK,WAAW,YAAa,QAAO,KAAK,WAAW,YAAY,MAAM,IAAI,KAAK,OAAO;AAC7G,WAAO,KAAK,cAAc,YAAY,MAAM,IAAI,KAAK,OAAO;AAAA,EAC9D;AAAA,EACA,uBAAuB;AACrB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,cAAc,CAAC,gBAAgB,WAAW,WAAW,WAAW,OAAO,QAAQ,eAAe,MAAM,gBAAgB,eAAe,iBAAiB,iBAAiB,cAAc,eAAe,eAAe;AACvN,UAAM,2BAA2B,QAAQ,WAAW,CAAC,SAAS,QAAQ,OAAO;AAC7E,QAAI,OAAO,2BAA2B,QAAQ,UAAU;AACxD,QAAI,4BAA4B,OAAO,QAAQ,UAAU,aAAa;AACpE,WAAK,QAAQ,QAAQ;AAAA,IACvB;AACA,QAAI,KAAK,QAAQ,cAAc,kBAAkB;AAC/C,aAAO;AAAA,QACL,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,CAAC,0BAA0B;AAC7B,aAAO;AAAA,QACL,GAAG;AAAA,MACL;AACA,iBAAW,OAAO,aAAa;AAC7B,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,gBAAgB,SAAS;AAC9B,UAAM,SAAS;AACf,eAAW,UAAU,SAAS;AAC5B,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,MAAM,KAAK,WAAW,OAAO,UAAU,GAAG,OAAO,MAAM,KAAK,WAAc,QAAQ,MAAM,GAAG;AAC3I,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,aAAa,YAAU,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AAC5E,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,gBAAgB,KAAK,QAAQ,iBAAiB;AACnD,SAAK,SAAS,WAAW,OAAO,eAAe;AAAA,EACjD;AAAA,EACA,sBAAsB,MAAM;AAC1B,WAAO,eAAe,IAAI;AAC1B,QAAI,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,EAAG,QAAO;AAC3C,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,QAAI,EAAE,WAAW,EAAG,QAAO;AAC3B,MAAE,IAAI;AACN,QAAI,EAAE,EAAE,SAAS,CAAC,EAAE,YAAY,MAAM,IAAK,QAAO;AAClD,WAAO,KAAK,mBAAmB,EAAE,KAAK,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAO,eAAe,IAAI;AAC1B,QAAI,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,EAAG,QAAO;AAC3C,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,WAAO,KAAK,mBAAmB,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,mBAAmB,MAAM;AACvB,QAAI,SAAS,IAAI,KAAK,KAAK,QAAQ,GAAG,IAAI,IAAI;AAC5C,UAAI,OAAO,SAAS,eAAe,OAAO,KAAK,wBAAwB,aAAa;AAClF,YAAI;AACF,cAAI,gBAAgB,KAAK,oBAAoB,IAAI,EAAE,CAAC;AACpD,cAAI,iBAAiB,KAAK,QAAQ,cAAc;AAC9C,4BAAgB,cAAc,YAAY;AAAA,UAC5C;AACA,cAAI,cAAe,QAAO;AAAA,QAC5B,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,YAAM,eAAe,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAC5E,UAAI,IAAI,KAAK,MAAM,GAAG;AACtB,UAAI,KAAK,QAAQ,cAAc;AAC7B,YAAI,EAAE,IAAI,UAAQ,KAAK,YAAY,CAAC;AAAA,MACtC,WAAW,EAAE,WAAW,GAAG;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY;AACxB,UAAE,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY;AACxB,YAAI,aAAa,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,IAAI,GAAI,GAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,YAAY,CAAC;AAAA,MACzF,WAAW,EAAE,WAAW,GAAG;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY;AACxB,YAAI,EAAE,CAAC,EAAE,WAAW,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY;AAC/C,YAAI,EAAE,CAAC,MAAM,SAAS,EAAE,CAAC,EAAE,WAAW,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY;AACjE,YAAI,aAAa,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,IAAI,GAAI,GAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,YAAY,CAAC;AACvF,YAAI,aAAa,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,IAAI,GAAI,GAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,YAAY,CAAC;AAAA,MACzF;AACA,aAAO,EAAE,KAAK,GAAG;AAAA,IACnB;AACA,WAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,eAAe,KAAK,YAAY,IAAI;AAAA,EACpF;AAAA,EACA,gBAAgB,MAAM;AACpB,QAAI,KAAK,QAAQ,SAAS,kBAAkB,KAAK,QAAQ,0BAA0B;AACjF,aAAO,KAAK,wBAAwB,IAAI;AAAA,IAC1C;AACA,WAAO,CAAC,KAAK,iBAAiB,CAAC,KAAK,cAAc,UAAU,KAAK,cAAc,QAAQ,IAAI,IAAI;AAAA,EACjG;AAAA,EACA,sBAAsB,OAAO;AAC3B,QAAI,CAAC,MAAO,QAAO;AACnB,QAAI;AACJ,UAAM,QAAQ,UAAQ;AACpB,UAAI,MAAO;AACX,YAAM,aAAa,KAAK,mBAAmB,IAAI;AAC/C,UAAI,CAAC,KAAK,QAAQ,iBAAiB,KAAK,gBAAgB,UAAU,EAAG,SAAQ;AAAA,IAC/E,CAAC;AACD,QAAI,CAAC,SAAS,KAAK,QAAQ,eAAe;AACxC,YAAM,QAAQ,UAAQ;AACpB,YAAI,MAAO;AACX,cAAM,UAAU,KAAK,wBAAwB,IAAI;AACjD,YAAI,KAAK,gBAAgB,OAAO,EAAG,QAAO,QAAQ;AAClD,gBAAQ,KAAK,QAAQ,cAAc,KAAK,kBAAgB;AACtD,cAAI,iBAAiB,QAAS,QAAO;AACrC,cAAI,aAAa,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,EAAG;AAC/D,cAAI,aAAa,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,KAAK,aAAa,UAAU,GAAG,aAAa,QAAQ,GAAG,CAAC,MAAM,QAAS,QAAO;AAC1I,cAAI,aAAa,QAAQ,OAAO,MAAM,KAAK,QAAQ,SAAS,EAAG,QAAO;AAAA,QACxE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,CAAC,MAAO,SAAQ,KAAK,iBAAiB,KAAK,QAAQ,WAAW,EAAE,CAAC;AACrE,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,WAAW,MAAM;AAChC,QAAI,CAAC,UAAW,QAAO,CAAC;AACxB,QAAI,OAAO,cAAc,WAAY,aAAY,UAAU,IAAI;AAC/D,QAAI,SAAS,SAAS,EAAG,aAAY,CAAC,SAAS;AAC/C,QAAI,MAAM,QAAQ,SAAS,EAAG,QAAO;AACrC,QAAI,CAAC,KAAM,QAAO,UAAU,WAAW,CAAC;AACxC,QAAI,QAAQ,UAAU,IAAI;AAC1B,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,sBAAsB,IAAI,CAAC;AAC9D,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,mBAAmB,IAAI,CAAC;AAC3D,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,wBAAwB,IAAI,CAAC;AAChE,QAAI,CAAC,MAAO,SAAQ,UAAU;AAC9B,WAAO,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,mBAAmB,MAAM,cAAc;AACrC,UAAM,gBAAgB,KAAK,iBAAiB,gBAAgB,KAAK,QAAQ,eAAe,CAAC,GAAG,IAAI;AAChG,UAAM,QAAQ,CAAC;AACf,UAAM,UAAU,OAAK;AACnB,UAAI,CAAC,EAAG;AACR,UAAI,KAAK,gBAAgB,CAAC,GAAG;AAC3B,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,aAAK,OAAO,KAAK,uDAAuD,CAAC,EAAE;AAAA,MAC7E;AAAA,IACF;AACA,QAAI,SAAS,IAAI,MAAM,KAAK,QAAQ,GAAG,IAAI,MAAM,KAAK,QAAQ,GAAG,IAAI,KAAK;AACxE,UAAI,KAAK,QAAQ,SAAS,eAAgB,SAAQ,KAAK,mBAAmB,IAAI,CAAC;AAC/E,UAAI,KAAK,QAAQ,SAAS,kBAAkB,KAAK,QAAQ,SAAS,cAAe,SAAQ,KAAK,sBAAsB,IAAI,CAAC;AACzH,UAAI,KAAK,QAAQ,SAAS,cAAe,SAAQ,KAAK,wBAAwB,IAAI,CAAC;AAAA,IACrF,WAAW,SAAS,IAAI,GAAG;AACzB,cAAQ,KAAK,mBAAmB,IAAI,CAAC;AAAA,IACvC;AACA,kBAAc,QAAQ,QAAM;AAC1B,UAAI,MAAM,QAAQ,EAAE,IAAI,EAAG,SAAQ,KAAK,mBAAmB,EAAE,CAAC;AAAA,IAChE,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAI,OAAO,CAAC;AAAA,EACV,MAAM,CAAC,OAAO,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,SAAS,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACrI,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,SAAS,MAAM,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC7Y,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC5I,IAAI,CAAC,CAAC;AAAA,EACN,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACtD,IAAI,CAAC,GAAG,GAAG,CAAC;AAAA,EACZ,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG;AAAA,EACxB,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,IAAI,CAAC,GAAG,GAAG,CAAC;AAAA,EACZ,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,OAAO,IAAI;AAAA,EAClB,IAAI,CAAC,GAAG,GAAG,CAAC;AAAA,EACZ,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EACf,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EACnB,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,EAChB,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EACf,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,EAAE;AAAA,EACb,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,CAAC;AAAA,EACZ,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,KAAK;AAAA,EACZ,IAAI,CAAC,GAAG,GAAG,CAAC;AAAA,EACZ,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;AAAA,EACjB,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,CAAC;AAAA,EACT,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,EAAE;AAAA,EACb,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,IAAI;AAAA,EACX,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EACf,IAAI;AACN,GAAG;AAAA,EACD,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE;AAAA,EACjB,IAAI;AACN,CAAC;AACD,IAAI,qBAAqB;AAAA,EACvB,GAAG,OAAK,OAAO,IAAI,CAAC;AAAA,EACpB,GAAG,OAAK,OAAO,KAAK,CAAC;AAAA,EACrB,GAAG,OAAK;AAAA,EACR,GAAG,OAAK,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC;AAAA,EACvH,GAAG,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,EAC/G,GAAG,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,EACpD,GAAG,OAAK,OAAO,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC;AAAA,EACjG,GAAG,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC;AAAA,EAClE,GAAG,OAAK,OAAO,KAAK,CAAC;AAAA,EACrB,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC;AAAA,EACpE,IAAI,OAAK,OAAO,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EACvF,IAAI,OAAK,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO,EAAE;AAAA,EAC5C,IAAI,OAAK,OAAO,MAAM,CAAC;AAAA,EACvB,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EACxD,IAAI,OAAK,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC;AAAA,EACzG,IAAI,OAAK,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EAClE,IAAI,OAAK,OAAO,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,EAC9D,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EAC3C,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAC7G,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAC1E,IAAI,OAAK,OAAO,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC;AAAA,EAC1F,IAAI,OAAK,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,IAAI,CAAC;AACpF;AACA,IAAM,kBAAkB,CAAC,MAAM,MAAM,IAAI;AACzC,IAAM,eAAe,CAAC,IAAI;AAC1B,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,cAAc,MAAM;AACxB,QAAM,QAAQ,CAAC;AACf,OAAK,QAAQ,SAAO;AAClB,QAAI,KAAK,QAAQ,OAAK;AACpB,YAAM,CAAC,IAAI;AAAA,QACT,SAAS,IAAI;AAAA,QACb,SAAS,mBAAmB,IAAI,EAAE;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,eAAe;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,SAAS,WAAW,OAAO,gBAAgB;AAChD,SAAK,CAAC,KAAK,QAAQ,qBAAqB,aAAa,SAAS,KAAK,QAAQ,iBAAiB,OAAO,OAAO,SAAS,eAAe,CAAC,KAAK,cAAc;AACpJ,WAAK,QAAQ,oBAAoB;AACjC,WAAK,OAAO,MAAM,oJAAoJ;AAAA,IACxK;AACA,SAAK,QAAQ,YAAY;AACzB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,KAAK,KAAK;AAChB,SAAK,MAAM,GAAG,IAAI;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,MAAM;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,KAAK,iBAAiB,GAAG;AAC3B,YAAM,cAAc,eAAe,SAAS,QAAQ,OAAO,IAAI;AAC/D,YAAM,OAAO,QAAQ,UAAU,YAAY;AAC3C,YAAM,WAAW,KAAK,UAAU;AAAA,QAC9B;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,YAAY,KAAK,kBAAkB;AACrC,eAAO,KAAK,iBAAiB,QAAQ;AAAA,MACvC;AACA,UAAI;AACJ,UAAI;AACF,eAAO,IAAI,KAAK,YAAY,aAAa;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,YAAI,CAAC,KAAK,MAAM,KAAK,EAAG;AACxB,cAAM,UAAU,KAAK,cAAc,wBAAwB,IAAI;AAC/D,eAAO,KAAK,QAAQ,SAAS,OAAO;AAAA,MACtC;AACA,WAAK,iBAAiB,QAAQ,IAAI;AAClC,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,KAAK,cAAc,wBAAwB,IAAI,CAAC;AAAA,EACxF;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,OAAO,KAAK,QAAQ,MAAM,OAAO;AACvC,QAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAO,QAAQ,KAAK,gBAAgB,EAAE,iBAAiB,SAAS;AAAA,IAClE;AACA,WAAO,QAAQ,KAAK,QAAQ,SAAS;AAAA,EACvC;AAAA,EACA,oBAAoB,MAAM,KAAK;AAC7B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,WAAO,KAAK,YAAY,MAAM,OAAO,EAAE,IAAI,YAAU,GAAG,GAAG,GAAG,MAAM,EAAE;AAAA,EACxE;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,OAAO,KAAK,QAAQ,MAAM,OAAO;AACvC,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AACA,QAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAO,KAAK,gBAAgB,EAAE,iBAAiB,KAAK,CAAC,iBAAiB,oBAAoB,cAAc,eAAe,IAAI,cAAc,eAAe,CAAC,EAAE,IAAI,oBAAkB,GAAG,KAAK,QAAQ,OAAO,GAAG,QAAQ,UAAU,UAAU,KAAK,QAAQ,OAAO,KAAK,EAAE,GAAG,cAAc,EAAE;AAAA,IACvR;AACA,WAAO,KAAK,QAAQ,IAAI,YAAU,KAAK,UAAU,MAAM,QAAQ,OAAO,CAAC;AAAA,EACzE;AAAA,EACA,UAAU,MAAM,OAAO;AACrB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,OAAO,KAAK,QAAQ,MAAM,OAAO;AACvC,QAAI,MAAM;AACR,UAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAO,GAAG,KAAK,QAAQ,OAAO,GAAG,QAAQ,UAAU,UAAU,KAAK,QAAQ,OAAO,KAAK,EAAE,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,MAC/G;AACA,aAAO,KAAK,yBAAyB,MAAM,KAAK;AAAA,IAClD;AACA,SAAK,OAAO,KAAK,6BAA6B,IAAI,EAAE;AACpD,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB,MAAM,OAAO;AACpC,UAAM,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,CAAC;AAC3E,QAAI,SAAS,KAAK,QAAQ,GAAG;AAC7B,QAAI,KAAK,QAAQ,wBAAwB,KAAK,QAAQ,WAAW,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG;AAC3F,UAAI,WAAW,GAAG;AAChB,iBAAS;AAAA,MACX,WAAW,WAAW,GAAG;AACvB,iBAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,eAAe,MAAM,KAAK,QAAQ,WAAW,OAAO,SAAS,IAAI,KAAK,QAAQ,UAAU,OAAO,SAAS,IAAI,OAAO,SAAS;AAClI,QAAI,KAAK,QAAQ,sBAAsB,MAAM;AAC3C,UAAI,WAAW,EAAG,QAAO;AACzB,UAAI,OAAO,WAAW,SAAU,QAAO,WAAW,OAAO,SAAS,CAAC;AACnE,aAAO,aAAa;AAAA,IACtB,WAAW,KAAK,QAAQ,sBAAsB,MAAM;AAClD,aAAO,aAAa;AAAA,IACtB,WAAW,KAAK,QAAQ,wBAAwB,KAAK,QAAQ,WAAW,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG;AAClG,aAAO,aAAa;AAAA,IACtB;AACA,WAAO,KAAK,QAAQ,WAAW,IAAI,SAAS,IAAI,KAAK,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI,SAAS;AAAA,EACvG;AAAA,EACA,mBAAmB;AACjB,WAAO,CAAC,gBAAgB,SAAS,KAAK,QAAQ,iBAAiB;AAAA,EACjE;AACF;AAEA,IAAM,uBAAuB,SAAU,MAAM,aAAa,KAAK;AAC7D,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACvF,MAAI,sBAAsB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC9F,MAAI,OAAO,oBAAoB,MAAM,aAAa,GAAG;AACrD,MAAI,CAAC,QAAQ,uBAAuB,SAAS,GAAG,GAAG;AACjD,WAAO,SAAS,MAAM,KAAK,YAAY;AACvC,QAAI,SAAS,OAAW,QAAO,SAAS,aAAa,KAAK,YAAY;AAAA,EACxE;AACA,SAAO;AACT;AACA,IAAM,YAAY,SAAO,IAAI,QAAQ,OAAO,MAAM;AAClD,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,SAAS,WAAW,OAAO,cAAc;AAC9C,SAAK,UAAU;AACf,SAAK,SAAS,QAAQ,iBAAiB,QAAQ,cAAc,WAAW,WAAS;AACjF,SAAK,KAAK,OAAO;AAAA,EACnB;AAAA,EACA,OAAO;AACL,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,CAAC,QAAQ,cAAe,SAAQ,gBAAgB;AAAA,MAClD,aAAa;AAAA,IACf;AACA,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,QAAQ;AACZ,SAAK,SAAS,aAAa,SAAY,WAAW;AAClD,SAAK,cAAc,gBAAgB,SAAY,cAAc;AAC7D,SAAK,sBAAsB,wBAAwB,SAAY,sBAAsB;AACrF,SAAK,SAAS,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAC9D,SAAK,SAAS,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAC9D,SAAK,kBAAkB,mBAAmB;AAC1C,SAAK,iBAAiB,iBAAiB,KAAK,kBAAkB;AAC9D,SAAK,iBAAiB,KAAK,iBAAiB,KAAK,kBAAkB;AACnE,SAAK,gBAAgB,gBAAgB,YAAY,aAAa,IAAI,wBAAwB,YAAY,KAAK;AAC3G,SAAK,gBAAgB,gBAAgB,YAAY,aAAa,IAAI,wBAAwB,YAAY,GAAG;AACzG,SAAK,0BAA0B,2BAA2B;AAC1D,SAAK,cAAc,eAAe;AAClC,SAAK,eAAe,iBAAiB,SAAY,eAAe;AAChE,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,QAAS,MAAK,KAAK,KAAK,OAAO;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,UAAM,mBAAmB,CAAC,gBAAgB,YAAY;AACpD,UAAI,kBAAkB,eAAe,WAAW,SAAS;AACvD,uBAAe,YAAY;AAC3B,eAAO;AAAA,MACT;AACA,aAAO,IAAI,OAAO,SAAS,GAAG;AAAA,IAChC;AACA,SAAK,SAAS,iBAAiB,KAAK,QAAQ,GAAG,KAAK,MAAM,QAAQ,KAAK,MAAM,EAAE;AAC/E,SAAK,iBAAiB,iBAAiB,KAAK,gBAAgB,GAAG,KAAK,MAAM,GAAG,KAAK,cAAc,QAAQ,KAAK,cAAc,GAAG,KAAK,MAAM,EAAE;AAC3I,SAAK,gBAAgB,iBAAiB,KAAK,eAAe,GAAG,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE;AAAA,EAC7G;AAAA,EACA,YAAY,KAAK,MAAM,KAAK,SAAS;AACnC,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,cAAc,oBAAoB,CAAC;AAClH,UAAM,eAAe,SAAO;AAC1B,UAAI,IAAI,QAAQ,KAAK,eAAe,IAAI,GAAG;AACzC,cAAM,OAAO,qBAAqB,MAAM,aAAa,KAAK,KAAK,QAAQ,cAAc,KAAK,QAAQ,mBAAmB;AACrH,eAAO,KAAK,eAAe,KAAK,OAAO,MAAM,QAAW,KAAK;AAAA,UAC3D,GAAG;AAAA,UACH,GAAG;AAAA,UACH,kBAAkB;AAAA,QACpB,CAAC,IAAI;AAAA,MACP;AACA,YAAM,IAAI,IAAI,MAAM,KAAK,eAAe;AACxC,YAAM,IAAI,EAAE,MAAM,EAAE,KAAK;AACzB,YAAM,IAAI,EAAE,KAAK,KAAK,eAAe,EAAE,KAAK;AAC5C,aAAO,KAAK,OAAO,qBAAqB,MAAM,aAAa,GAAG,KAAK,QAAQ,cAAc,KAAK,QAAQ,mBAAmB,GAAG,GAAG,KAAK;AAAA,QAClI,GAAG;AAAA,QACH,GAAG;AAAA,QACH,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,SAAK,YAAY;AACjB,UAAM,8BAA8B,WAAW,QAAQ,+BAA+B,KAAK,QAAQ;AACnG,UAAM,kBAAkB,WAAW,QAAQ,iBAAiB,QAAQ,cAAc,oBAAoB,SAAY,QAAQ,cAAc,kBAAkB,KAAK,QAAQ,cAAc;AACrL,UAAM,QAAQ,CAAC;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,SAAO,UAAU,GAAG;AAAA,IACjC,GAAG;AAAA,MACD,OAAO,KAAK;AAAA,MACZ,WAAW,SAAO,KAAK,cAAc,UAAU,KAAK,OAAO,GAAG,CAAC,IAAI,UAAU,GAAG;AAAA,IAClF,CAAC;AACD,UAAM,QAAQ,UAAQ;AACpB,iBAAW;AACX,aAAO,QAAQ,KAAK,MAAM,KAAK,GAAG,GAAG;AACnC,cAAM,aAAa,MAAM,CAAC,EAAE,KAAK;AACjC,gBAAQ,aAAa,UAAU;AAC/B,YAAI,UAAU,QAAW;AACvB,cAAI,OAAO,gCAAgC,YAAY;AACrD,kBAAM,OAAO,4BAA4B,KAAK,OAAO,OAAO;AAC5D,oBAAQ,SAAS,IAAI,IAAI,OAAO;AAAA,UAClC,WAAW,WAAW,OAAO,UAAU,eAAe,KAAK,SAAS,UAAU,GAAG;AAC/E,oBAAQ;AAAA,UACV,WAAW,iBAAiB;AAC1B,oBAAQ,MAAM,CAAC;AACf;AAAA,UACF,OAAO;AACL,iBAAK,OAAO,KAAK,8BAA8B,UAAU,sBAAsB,GAAG,EAAE;AACpF,oBAAQ;AAAA,UACV;AAAA,QACF,WAAW,CAAC,SAAS,KAAK,KAAK,CAAC,KAAK,qBAAqB;AACxD,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,cAAM,IAAI,QAAQ,MAAM,CAAC,GAAG,SAAS;AACrC,YAAI,iBAAiB;AACnB,eAAK,MAAM,aAAa,MAAM;AAC9B,eAAK,MAAM,aAAa,MAAM,CAAC,EAAE;AAAA,QACnC,OAAO;AACL,eAAK,MAAM,YAAY;AAAA,QACzB;AACA;AACA,YAAI,YAAY,KAAK,aAAa;AAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK,KAAK,IAAI;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,mBAAmB,CAAC,KAAK,qBAAqB;AAClD,YAAM,MAAM,KAAK;AACjB,UAAI,IAAI,QAAQ,GAAG,IAAI,EAAG,QAAO;AACjC,YAAM,IAAI,IAAI,MAAM,IAAI,OAAO,GAAG,GAAG,OAAO,CAAC;AAC7C,UAAI,gBAAgB,IAAI,EAAE,CAAC,CAAC;AAC5B,YAAM,EAAE,CAAC;AACT,sBAAgB,KAAK,YAAY,eAAe,aAAa;AAC7D,YAAM,sBAAsB,cAAc,MAAM,IAAI;AACpD,YAAM,sBAAsB,cAAc,MAAM,IAAI;AACpD,UAAI,uBAAuB,oBAAoB,SAAS,MAAM,KAAK,CAAC,uBAAuB,oBAAoB,SAAS,MAAM,GAAG;AAC/H,wBAAgB,cAAc,QAAQ,MAAM,GAAG;AAAA,MACjD;AACA,UAAI;AACF,wBAAgB,KAAK,MAAM,aAAa;AACxC,YAAI,iBAAkB,iBAAgB;AAAA,UACpC,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF,SAAS,GAAG;AACV,aAAK,OAAO,KAAK,oDAAoD,GAAG,IAAI,CAAC;AAC7E,eAAO,GAAG,GAAG,GAAG,GAAG,GAAG,aAAa;AAAA,MACrC;AACA,UAAI,cAAc,gBAAgB,cAAc,aAAa,QAAQ,KAAK,MAAM,IAAI,GAAI,QAAO,cAAc;AAC7G,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,KAAK,cAAc,KAAK,GAAG,GAAG;AAC3C,UAAI,aAAa,CAAC;AAClB,sBAAgB;AAAA,QACd,GAAG;AAAA,MACL;AACA,sBAAgB,cAAc,WAAW,CAAC,SAAS,cAAc,OAAO,IAAI,cAAc,UAAU;AACpG,oBAAc,qBAAqB;AACnC,aAAO,cAAc;AACrB,UAAI,WAAW;AACf,UAAI,MAAM,CAAC,EAAE,QAAQ,KAAK,eAAe,MAAM,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,GAAG;AAC3E,cAAM,IAAI,MAAM,CAAC,EAAE,MAAM,KAAK,eAAe,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACtE,cAAM,CAAC,IAAI,EAAE,MAAM;AACnB,qBAAa;AACb,mBAAW;AAAA,MACb;AACA,cAAQ,GAAG,iBAAiB,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,aAAa,GAAG,aAAa;AACrF,UAAI,SAAS,MAAM,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,EAAG,QAAO;AAC1D,UAAI,CAAC,SAAS,KAAK,EAAG,SAAQ,WAAW,KAAK;AAC9C,UAAI,CAAC,OAAO;AACV,aAAK,OAAO,KAAK,qBAAqB,MAAM,CAAC,CAAC,gBAAgB,GAAG,EAAE;AACnE,gBAAQ;AAAA,MACV;AACA,UAAI,UAAU;AACZ,gBAAQ,WAAW,OAAO,CAAC,GAAG,MAAM,KAAK,OAAO,GAAG,GAAG,QAAQ,KAAK;AAAA,UACjE,GAAG;AAAA,UACH,kBAAkB,MAAM,CAAC,EAAE,KAAK;AAAA,QAClC,CAAC,GAAG,MAAM,KAAK,CAAC;AAAA,MAClB;AACA,YAAM,IAAI,QAAQ,MAAM,CAAC,GAAG,KAAK;AACjC,WAAK,OAAO,YAAY;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,eAAa;AAClC,MAAI,aAAa,UAAU,YAAY,EAAE,KAAK;AAC9C,QAAM,gBAAgB,CAAC;AACvB,MAAI,UAAU,QAAQ,GAAG,IAAI,IAAI;AAC/B,UAAM,IAAI,UAAU,MAAM,GAAG;AAC7B,iBAAa,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK;AACrC,UAAM,SAAS,EAAE,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC;AAChD,QAAI,eAAe,cAAc,OAAO,QAAQ,GAAG,IAAI,GAAG;AACxD,UAAI,CAAC,cAAc,SAAU,eAAc,WAAW,OAAO,KAAK;AAAA,IACpE,WAAW,eAAe,kBAAkB,OAAO,QAAQ,GAAG,IAAI,GAAG;AACnE,UAAI,CAAC,cAAc,MAAO,eAAc,QAAQ,OAAO,KAAK;AAAA,IAC9D,OAAO;AACL,YAAM,OAAO,OAAO,MAAM,GAAG;AAC7B,WAAK,QAAQ,SAAO;AAClB,YAAI,KAAK;AACP,gBAAM,CAAC,KAAK,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG;AACpC,gBAAM,MAAM,KAAK,KAAK,GAAG,EAAE,KAAK,EAAE,QAAQ,YAAY,EAAE;AACxD,gBAAM,aAAa,IAAI,KAAK;AAC5B,cAAI,CAAC,cAAc,UAAU,EAAG,eAAc,UAAU,IAAI;AAC5D,cAAI,QAAQ,QAAS,eAAc,UAAU,IAAI;AACjD,cAAI,QAAQ,OAAQ,eAAc,UAAU,IAAI;AAChD,cAAI,CAAC,MAAM,GAAG,EAAG,eAAc,UAAU,IAAI,SAAS,KAAK,EAAE;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,wBAAwB,QAAM;AAClC,QAAM,QAAQ,CAAC;AACf,SAAO,CAAC,KAAK,KAAK,YAAY;AAC5B,QAAI,cAAc;AAClB,QAAI,WAAW,QAAQ,oBAAoB,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ,gBAAgB,KAAK,QAAQ,QAAQ,gBAAgB,GAAG;AACtJ,oBAAc;AAAA,QACZ,GAAG;AAAA,QACH,CAAC,QAAQ,gBAAgB,GAAG;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,MAAM,MAAM,KAAK,UAAU,WAAW;AAC5C,QAAI,YAAY,MAAM,GAAG;AACzB,QAAI,CAAC,WAAW;AACd,kBAAY,GAAG,eAAe,GAAG,GAAG,OAAO;AAC3C,YAAM,GAAG,IAAI;AAAA,IACf;AACA,WAAO,UAAU,GAAG;AAAA,EACtB;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,cAAc;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,SAAS,WAAW,OAAO,WAAW;AAC3C,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,MACb,QAAQ,sBAAsB,CAAC,KAAK,QAAQ;AAC1C,cAAM,YAAY,IAAI,KAAK,aAAa,KAAK;AAAA,UAC3C,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,UAAU,sBAAsB,CAAC,KAAK,QAAQ;AAC5C,cAAM,YAAY,IAAI,KAAK,aAAa,KAAK;AAAA,UAC3C,GAAG;AAAA,UACH,OAAO;AAAA,QACT,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,UAAU,sBAAsB,CAAC,KAAK,QAAQ;AAC5C,cAAM,YAAY,IAAI,KAAK,eAAe,KAAK;AAAA,UAC7C,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,cAAc,sBAAsB,CAAC,KAAK,QAAQ;AAChD,cAAM,YAAY,IAAI,KAAK,mBAAmB,KAAK;AAAA,UACjD,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,KAAK,IAAI,SAAS,KAAK;AAAA,MACxD,CAAC;AAAA,MACD,MAAM,sBAAsB,CAAC,KAAK,QAAQ;AACxC,cAAM,YAAY,IAAI,KAAK,WAAW,KAAK;AAAA,UACzC,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,IACH;AACA,SAAK,KAAK,OAAO;AAAA,EACnB;AAAA,EACA,KAAK,UAAU;AACb,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MAChF,eAAe,CAAC;AAAA,IAClB;AACA,SAAK,kBAAkB,QAAQ,cAAc,mBAAmB;AAAA,EAClE;AAAA,EACA,IAAI,MAAM,IAAI;AACZ,SAAK,QAAQ,KAAK,YAAY,EAAE,KAAK,CAAC,IAAI;AAAA,EAC5C;AAAA,EACA,UAAU,MAAM,IAAI;AAClB,SAAK,QAAQ,KAAK,YAAY,EAAE,KAAK,CAAC,IAAI,sBAAsB,EAAE;AAAA,EACpE;AAAA,EACA,OAAO,OAAO,QAAQ,KAAK;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM,UAAU,OAAO,MAAM,KAAK,eAAe;AACjD,QAAI,QAAQ,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI,KAAK,QAAQ,KAAK,OAAK,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG;AAC9H,YAAM,YAAY,QAAQ,UAAU,OAAK,EAAE,QAAQ,GAAG,IAAI,EAAE;AAC5D,cAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,QAAQ,OAAO,GAAG,SAAS,CAAC,EAAE,KAAK,KAAK,eAAe;AAAA,IACtF;AACA,UAAM,SAAS,QAAQ,OAAO,CAAC,KAAK,MAAM;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,eAAe,CAAC;AACpB,UAAI,KAAK,QAAQ,UAAU,GAAG;AAC5B,YAAI,YAAY;AAChB,YAAI;AACF,gBAAM,aAAa,WAAW,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ,gBAAgB,KAAK,CAAC;AACzG,gBAAM,IAAI,WAAW,UAAU,WAAW,OAAO,QAAQ,UAAU,QAAQ,OAAO;AAClF,sBAAY,KAAK,QAAQ,UAAU,EAAE,KAAK,GAAG;AAAA,YAC3C,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AAAA,QACH,SAAS,OAAO;AACd,eAAK,OAAO,KAAK,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,MACT,OAAO;AACL,aAAK,OAAO,KAAK,oCAAoC,UAAU,EAAE;AAAA,MACnE;AACA,aAAO;AAAA,IACT,GAAG,KAAK;AACR,WAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB,CAAC,GAAG,SAAS;AACjC,MAAI,EAAE,QAAQ,IAAI,MAAM,QAAW;AACjC,WAAO,EAAE,QAAQ,IAAI;AACrB,MAAE;AAAA,EACJ;AACF;AACA,IAAM,YAAN,cAAwB,aAAa;AAAA,EACnC,YAAY,SAAS,OAAO,UAAU;AACpC,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAM;AACN,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,gBAAgB,SAAS;AAC9B,SAAK,UAAU;AACf,SAAK,SAAS,WAAW,OAAO,kBAAkB;AAClD,SAAK,eAAe,CAAC;AACrB,SAAK,mBAAmB,QAAQ,oBAAoB;AACpD,SAAK,eAAe;AACpB,SAAK,aAAa,QAAQ,cAAc,IAAI,QAAQ,aAAa;AACjE,SAAK,eAAe,QAAQ,gBAAgB,IAAI,QAAQ,eAAe;AACvE,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK,WAAW,KAAK,QAAQ,MAAM;AACrC,WAAK,QAAQ,KAAK,UAAU,QAAQ,SAAS,OAAO;AAAA,IACtD;AAAA,EACF;AAAA,EACA,UAAU,WAAW,YAAY,SAAS,UAAU;AAClD,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU,CAAC;AACjB,UAAM,kBAAkB,CAAC;AACzB,UAAM,mBAAmB,CAAC;AAC1B,cAAU,QAAQ,SAAO;AACvB,UAAI,mBAAmB;AACvB,iBAAW,QAAQ,QAAM;AACvB,cAAM,OAAO,GAAG,GAAG,IAAI,EAAE;AACzB,YAAI,CAAC,QAAQ,UAAU,KAAK,MAAM,kBAAkB,KAAK,EAAE,GAAG;AAC5D,eAAK,MAAM,IAAI,IAAI;AAAA,QACrB,WAAW,KAAK,MAAM,IAAI,IAAI,EAAG;AAAA,iBAAW,KAAK,MAAM,IAAI,MAAM,GAAG;AAClE,cAAI,QAAQ,IAAI,MAAM,OAAW,SAAQ,IAAI,IAAI;AAAA,QACnD,OAAO;AACL,eAAK,MAAM,IAAI,IAAI;AACnB,6BAAmB;AACnB,cAAI,QAAQ,IAAI,MAAM,OAAW,SAAQ,IAAI,IAAI;AACjD,cAAI,OAAO,IAAI,MAAM,OAAW,QAAO,IAAI,IAAI;AAC/C,cAAI,iBAAiB,EAAE,MAAM,OAAW,kBAAiB,EAAE,IAAI;AAAA,QACjE;AAAA,MACF,CAAC;AACD,UAAI,CAAC,iBAAkB,iBAAgB,GAAG,IAAI;AAAA,IAChD,CAAC;AACD,QAAI,OAAO,KAAK,MAAM,EAAE,UAAU,OAAO,KAAK,OAAO,EAAE,QAAQ;AAC7D,WAAK,MAAM,KAAK;AAAA,QACd;AAAA,QACA,cAAc,OAAO,KAAK,OAAO,EAAE;AAAA,QACnC,QAAQ,CAAC;AAAA,QACT,QAAQ,CAAC;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,QAAQ,OAAO,KAAK,MAAM;AAAA,MAC1B,SAAS,OAAO,KAAK,OAAO;AAAA,MAC5B,iBAAiB,OAAO,KAAK,eAAe;AAAA,MAC5C,kBAAkB,OAAO,KAAK,gBAAgB;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO,MAAM,KAAK,MAAM;AACtB,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,UAAM,MAAM,EAAE,CAAC;AACf,UAAM,KAAK,EAAE,CAAC;AACd,QAAI,IAAK,MAAK,KAAK,iBAAiB,KAAK,IAAI,GAAG;AAChD,QAAI,CAAC,OAAO,MAAM;AAChB,WAAK,MAAM,kBAAkB,KAAK,IAAI,MAAM,QAAW,QAAW;AAAA,QAChE,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,SAAK,MAAM,IAAI,IAAI,MAAM,KAAK;AAC9B,QAAI,OAAO,KAAM,MAAK,MAAM,IAAI,IAAI;AACpC,UAAM,SAAS,CAAC;AAChB,SAAK,MAAM,QAAQ,OAAK;AACtB,eAAS,EAAE,QAAQ,CAAC,GAAG,GAAG,EAAE;AAC5B,oBAAc,GAAG,IAAI;AACrB,UAAI,IAAK,GAAE,OAAO,KAAK,GAAG;AAC1B,UAAI,EAAE,iBAAiB,KAAK,CAAC,EAAE,MAAM;AACnC,eAAO,KAAK,EAAE,MAAM,EAAE,QAAQ,OAAK;AACjC,cAAI,CAAC,OAAO,CAAC,EAAG,QAAO,CAAC,IAAI,CAAC;AAC7B,gBAAM,aAAa,EAAE,OAAO,CAAC;AAC7B,cAAI,WAAW,QAAQ;AACrB,uBAAW,QAAQ,OAAK;AACtB,kBAAI,OAAO,CAAC,EAAE,CAAC,MAAM,OAAW,QAAO,CAAC,EAAE,CAAC,IAAI;AAAA,YACjD,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,UAAE,OAAO;AACT,YAAI,EAAE,OAAO,QAAQ;AACnB,YAAE,SAAS,EAAE,MAAM;AAAA,QACrB,OAAO;AACL,YAAE,SAAS;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,KAAK,UAAU,MAAM;AAC1B,SAAK,QAAQ,KAAK,MAAM,OAAO,OAAK,CAAC,EAAE,IAAI;AAAA,EAC7C;AAAA,EACA,KAAK,KAAK,IAAI,QAAQ;AACpB,QAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,KAAK;AACpF,QAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,QAAI,CAAC,IAAI,OAAQ,QAAO,SAAS,MAAM,CAAC,CAAC;AACzC,QAAI,KAAK,gBAAgB,KAAK,kBAAkB;AAC9C,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,SAAK;AACL,UAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,WAAK;AACL,UAAI,KAAK,aAAa,SAAS,GAAG;AAChC,cAAM,OAAO,KAAK,aAAa,MAAM;AACrC,aAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,MAChF;AACA,UAAI,OAAO,QAAQ,QAAQ,KAAK,YAAY;AAC1C,mBAAW,MAAM;AACf,eAAK,KAAK,KAAK,MAAM,KAAK,IAAI,QAAQ,QAAQ,GAAG,OAAO,GAAG,QAAQ;AAAA,QACrE,GAAG,IAAI;AACP;AAAA,MACF;AACA,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,UAAM,KAAK,KAAK,QAAQ,MAAM,EAAE,KAAK,KAAK,OAAO;AACjD,QAAI,GAAG,WAAW,GAAG;AACnB,UAAI;AACF,cAAM,IAAI,GAAG,KAAK,EAAE;AACpB,YAAI,KAAK,OAAO,EAAE,SAAS,YAAY;AACrC,YAAE,KAAK,UAAQ,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,QAAQ;AAAA,QACrD,OAAO;AACL,mBAAS,MAAM,CAAC;AAAA,QAClB;AAAA,MACF,SAAS,KAAK;AACZ,iBAAS,GAAG;AAAA,MACd;AACA;AAAA,IACF;AACA,WAAO,GAAG,KAAK,IAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,eAAe,WAAW,YAAY;AACpC,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,OAAO,KAAK,gEAAgE;AACjF,aAAO,YAAY,SAAS;AAAA,IAC9B;AACA,QAAI,SAAS,SAAS,EAAG,aAAY,KAAK,cAAc,mBAAmB,SAAS;AACpF,QAAI,SAAS,UAAU,EAAG,cAAa,CAAC,UAAU;AAClD,UAAM,SAAS,KAAK,UAAU,WAAW,YAAY,SAAS,QAAQ;AACtE,QAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,UAAI,CAAC,OAAO,QAAQ,OAAQ,UAAS;AACrC,aAAO;AAAA,IACT;AACA,WAAO,OAAO,QAAQ,UAAQ;AAC5B,WAAK,QAAQ,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,KAAK,WAAW,YAAY,UAAU;AACpC,SAAK,eAAe,WAAW,YAAY,CAAC,GAAG,QAAQ;AAAA,EACzD;AAAA,EACA,OAAO,WAAW,YAAY,UAAU;AACtC,SAAK,eAAe,WAAW,YAAY;AAAA,MACzC,QAAQ;AAAA,IACV,GAAG,QAAQ;AAAA,EACb;AAAA,EACA,QAAQ,MAAM;AACZ,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,UAAM,MAAM,EAAE,CAAC;AACf,UAAM,KAAK,EAAE,CAAC;AACd,SAAK,KAAK,KAAK,IAAI,QAAQ,QAAW,QAAW,CAAC,KAAK,SAAS;AAC9D,UAAI,IAAK,MAAK,OAAO,KAAK,GAAG,MAAM,qBAAqB,EAAE,iBAAiB,GAAG,WAAW,GAAG;AAC5F,UAAI,CAAC,OAAO,KAAM,MAAK,OAAO,IAAI,GAAG,MAAM,oBAAoB,EAAE,iBAAiB,GAAG,IAAI,IAAI;AAC7F,WAAK,OAAO,MAAM,KAAK,IAAI;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,YAAY,WAAW,WAAW,KAAK,eAAe,UAAU;AAC9D,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,MAAM;AAAA,IAAC;AACrF,QAAI,KAAK,SAAS,SAAS,KAAK,SAAS,MAAM,sBAAsB,CAAC,KAAK,SAAS,MAAM,mBAAmB,SAAS,GAAG;AACvH,WAAK,OAAO,KAAK,qBAAqB,GAAG,uBAAuB,SAAS,wBAAwB,0NAA0N;AAC3T;AAAA,IACF;AACA,QAAI,QAAQ,UAAa,QAAQ,QAAQ,QAAQ,GAAI;AACrD,QAAI,KAAK,WAAW,KAAK,QAAQ,QAAQ;AACvC,YAAM,OAAO;AAAA,QACX,GAAG;AAAA,QACH;AAAA,MACF;AACA,YAAM,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO;AAChD,UAAI,GAAG,SAAS,GAAG;AACjB,YAAI;AACF,cAAI;AACJ,cAAI,GAAG,WAAW,GAAG;AACnB,gBAAI,GAAG,WAAW,WAAW,KAAK,eAAe,IAAI;AAAA,UACvD,OAAO;AACL,gBAAI,GAAG,WAAW,WAAW,KAAK,aAAa;AAAA,UACjD;AACA,cAAI,KAAK,OAAO,EAAE,SAAS,YAAY;AACrC,cAAE,KAAK,UAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG;AAAA,UAC3C,OAAO;AACL,gBAAI,MAAM,CAAC;AAAA,UACb;AAAA,QACF,SAAS,KAAK;AACZ,cAAI,GAAG;AAAA,QACT;AAAA,MACF,OAAO;AACL,WAAG,WAAW,WAAW,KAAK,eAAe,KAAK,IAAI;AAAA,MACxD;AAAA,IACF;AACA,QAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAG;AACjC,SAAK,MAAM,YAAY,UAAU,CAAC,GAAG,WAAW,KAAK,aAAa;AAAA,EACpE;AACF;AAEA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AAAA,EACP,eAAe;AAAA,EACf,IAAI,CAAC,aAAa;AAAA,EAClB,WAAW,CAAC,aAAa;AAAA,EACzB,aAAa,CAAC,KAAK;AAAA,EACnB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,aAAa;AAAA,EACb,yBAAyB;AAAA,EACzB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,kCAAkC,UAAQ;AACxC,QAAI,MAAM,CAAC;AACX,QAAI,OAAO,KAAK,CAAC,MAAM,SAAU,OAAM,KAAK,CAAC;AAC7C,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,KAAI,eAAe,KAAK,CAAC;AAChD,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,KAAI,eAAe,KAAK,CAAC;AAChD,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY,OAAO,KAAK,CAAC,MAAM,UAAU;AAC9D,YAAM,UAAU,KAAK,CAAC,KAAK,KAAK,CAAC;AACjC,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,YAAI,GAAG,IAAI,QAAQ,GAAG;AAAA,MACxB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,aAAa;AAAA,IACb,QAAQ,WAAS;AAAA,IACjB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,aAAa;AAAA,IACb,iBAAiB;AAAA,EACnB;AACF;AACA,IAAM,mBAAmB,aAAW;AAClC,MAAI,SAAS,QAAQ,EAAE,EAAG,SAAQ,KAAK,CAAC,QAAQ,EAAE;AAClD,MAAI,SAAS,QAAQ,WAAW,EAAG,SAAQ,cAAc,CAAC,QAAQ,WAAW;AAC7E,MAAI,SAAS,QAAQ,UAAU,EAAG,SAAQ,aAAa,CAAC,QAAQ,UAAU;AAC1E,MAAI,QAAQ,iBAAiB,QAAQ,cAAc,QAAQ,QAAQ,IAAI,GAAG;AACxE,YAAQ,gBAAgB,QAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAEA,IAAM,OAAO,MAAM;AAAC;AACpB,IAAM,sBAAsB,UAAQ;AAClC,QAAM,OAAO,OAAO,oBAAoB,OAAO,eAAe,IAAI,CAAC;AACnE,OAAK,QAAQ,SAAO;AAClB,QAAI,OAAO,KAAK,GAAG,MAAM,YAAY;AACnC,WAAK,GAAG,IAAI,KAAK,GAAG,EAAE,KAAK,IAAI;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AACA,IAAM,OAAN,MAAM,cAAa,aAAa;AAAA,EAC9B,cAAc;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,UAAM;AACN,SAAK,UAAU,iBAAiB,OAAO;AACvC,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,MACb,UAAU,CAAC;AAAA,IACb;AACA,wBAAoB,IAAI;AACxB,QAAI,YAAY,CAAC,KAAK,iBAAiB,CAAC,QAAQ,SAAS;AACvD,UAAI,CAAC,KAAK,QAAQ,eAAe;AAC/B,aAAK,KAAK,SAAS,QAAQ;AAC3B,eAAO;AAAA,MACT;AACA,iBAAW,MAAM;AACf,aAAK,KAAK,SAAS,QAAQ;AAAA,MAC7B,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,QAAQ;AACZ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,SAAK,iBAAiB;AACtB,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU,CAAC;AAAA,IACb;AACA,QAAI,CAAC,QAAQ,aAAa,QAAQ,cAAc,SAAS,QAAQ,IAAI;AACnE,UAAI,SAAS,QAAQ,EAAE,GAAG;AACxB,gBAAQ,YAAY,QAAQ;AAAA,MAC9B,WAAW,QAAQ,GAAG,QAAQ,aAAa,IAAI,GAAG;AAChD,gBAAQ,YAAY,QAAQ,GAAG,CAAC;AAAA,MAClC;AAAA,IACF;AACA,UAAM,UAAU,IAAI;AACpB,SAAK,UAAU;AAAA,MACb,GAAG;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG,iBAAiB,OAAO;AAAA,IAC7B;AACA,QAAI,KAAK,QAAQ,qBAAqB,MAAM;AAC1C,WAAK,QAAQ,gBAAgB;AAAA,QAC3B,GAAG,QAAQ;AAAA,QACX,GAAG,KAAK,QAAQ;AAAA,MAClB;AAAA,IACF;AACA,QAAI,QAAQ,iBAAiB,QAAW;AACtC,WAAK,QAAQ,0BAA0B,QAAQ;AAAA,IACjD;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,WAAK,QAAQ,yBAAyB,QAAQ;AAAA,IAChD;AACA,UAAM,sBAAsB,mBAAiB;AAC3C,UAAI,CAAC,cAAe,QAAO;AAC3B,UAAI,OAAO,kBAAkB,WAAY,QAAO,IAAI,cAAc;AAClE,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,UAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAW,KAAK,oBAAoB,KAAK,QAAQ,MAAM,GAAG,KAAK,OAAO;AAAA,MACxE,OAAO;AACL,mBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,MACpC;AACA,UAAI;AACJ,UAAI,KAAK,QAAQ,WAAW;AAC1B,oBAAY,KAAK,QAAQ;AAAA,MAC3B,WAAW,OAAO,SAAS,aAAa;AACtC,oBAAY;AAAA,MACd;AACA,YAAM,KAAK,IAAI,aAAa,KAAK,OAAO;AACxC,WAAK,QAAQ,IAAI,cAAc,KAAK,QAAQ,WAAW,KAAK,OAAO;AACnE,YAAM,IAAI,KAAK;AACf,QAAE,SAAS;AACX,QAAE,gBAAgB,KAAK;AACvB,QAAE,gBAAgB;AAClB,QAAE,iBAAiB,IAAI,eAAe,IAAI;AAAA,QACxC,SAAS,KAAK,QAAQ;AAAA,QACtB,mBAAmB,KAAK,QAAQ;AAAA,QAChC,sBAAsB,KAAK,QAAQ;AAAA,MACrC,CAAC;AACD,UAAI,cAAc,CAAC,KAAK,QAAQ,cAAc,UAAU,KAAK,QAAQ,cAAc,WAAW,QAAQ,cAAc,SAAS;AAC3H,UAAE,YAAY,oBAAoB,SAAS;AAC3C,UAAE,UAAU,KAAK,GAAG,KAAK,OAAO;AAChC,aAAK,QAAQ,cAAc,SAAS,EAAE,UAAU,OAAO,KAAK,EAAE,SAAS;AAAA,MACzE;AACA,QAAE,eAAe,IAAI,aAAa,KAAK,OAAO;AAC9C,QAAE,QAAQ;AAAA,QACR,oBAAoB,KAAK,mBAAmB,KAAK,IAAI;AAAA,MACvD;AACA,QAAE,mBAAmB,IAAI,UAAU,oBAAoB,KAAK,QAAQ,OAAO,GAAG,EAAE,eAAe,GAAG,KAAK,OAAO;AAC9G,QAAE,iBAAiB,GAAG,KAAK,SAAU,OAAO;AAC1C,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,eAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,QACjC;AACA,cAAM,KAAK,OAAO,GAAG,IAAI;AAAA,MAC3B,CAAC;AACD,UAAI,KAAK,QAAQ,kBAAkB;AACjC,UAAE,mBAAmB,oBAAoB,KAAK,QAAQ,gBAAgB;AACtE,YAAI,EAAE,iBAAiB,KAAM,GAAE,iBAAiB,KAAK,GAAG,KAAK,QAAQ,WAAW,KAAK,OAAO;AAAA,MAC9F;AACA,UAAI,KAAK,QAAQ,YAAY;AAC3B,UAAE,aAAa,oBAAoB,KAAK,QAAQ,UAAU;AAC1D,YAAI,EAAE,WAAW,KAAM,GAAE,WAAW,KAAK,IAAI;AAAA,MAC/C;AACA,WAAK,aAAa,IAAI,WAAW,KAAK,UAAU,KAAK,OAAO;AAC5D,WAAK,WAAW,GAAG,KAAK,SAAU,OAAO;AACvC,iBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,eAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,QACnC;AACA,cAAM,KAAK,OAAO,GAAG,IAAI;AAAA,MAC3B,CAAC;AACD,WAAK,QAAQ,SAAS,QAAQ,OAAK;AACjC,YAAI,EAAE,KAAM,GAAE,KAAK,IAAI;AAAA,MACzB,CAAC;AAAA,IACH;AACA,SAAK,SAAS,KAAK,QAAQ,cAAc;AACzC,QAAI,CAAC,SAAU,YAAW;AAC1B,QAAI,KAAK,QAAQ,eAAe,CAAC,KAAK,SAAS,oBAAoB,CAAC,KAAK,QAAQ,KAAK;AACpF,YAAM,QAAQ,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW;AACnF,UAAI,MAAM,SAAS,KAAK,MAAM,CAAC,MAAM,MAAO,MAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,IACxE;AACA,QAAI,CAAC,KAAK,SAAS,oBAAoB,CAAC,KAAK,QAAQ,KAAK;AACxD,WAAK,OAAO,KAAK,yDAAyD;AAAA,IAC5E;AACA,UAAM,WAAW,CAAC,eAAe,qBAAqB,qBAAqB,mBAAmB;AAC9F,aAAS,QAAQ,YAAU;AACzB,WAAK,MAAM,IAAI,WAAY;AACzB,eAAO,MAAM,MAAM,MAAM,EAAE,GAAG,SAAS;AAAA,MACzC;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,CAAC,eAAe,gBAAgB,qBAAqB,sBAAsB;AACnG,oBAAgB,QAAQ,YAAU;AAChC,WAAK,MAAM,IAAI,WAAY;AACzB,cAAM,MAAM,MAAM,EAAE,GAAG,SAAS;AAChC,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,UAAM,WAAW,MAAM;AACvB,UAAM,OAAO,MAAM;AACjB,YAAM,SAAS,CAAC,KAAKD,OAAM;AACzB,aAAK,iBAAiB;AACtB,YAAI,KAAK,iBAAiB,CAAC,KAAK,qBAAsB,MAAK,OAAO,KAAK,uEAAuE;AAC9I,aAAK,gBAAgB;AACrB,YAAI,CAAC,KAAK,QAAQ,QAAS,MAAK,OAAO,IAAI,eAAe,KAAK,OAAO;AACtE,aAAK,KAAK,eAAe,KAAK,OAAO;AACrC,iBAAS,QAAQA,EAAC;AAClB,iBAAS,KAAKA,EAAC;AAAA,MACjB;AACA,UAAI,KAAK,aAAa,KAAK,QAAQ,qBAAqB,QAAQ,CAAC,KAAK,cAAe,QAAO,OAAO,MAAM,KAAK,EAAE,KAAK,IAAI,CAAC;AAC1H,WAAK,eAAe,KAAK,QAAQ,KAAK,MAAM;AAAA,IAC9C;AACA,QAAI,KAAK,QAAQ,aAAa,CAAC,KAAK,QAAQ,eAAe;AACzD,WAAK;AAAA,IACP,OAAO;AACL,iBAAW,MAAM,CAAC;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,UAAU;AACtB,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,QAAI,eAAe;AACnB,UAAM,UAAU,SAAS,QAAQ,IAAI,WAAW,KAAK;AACrD,QAAI,OAAO,aAAa,WAAY,gBAAe;AACnD,QAAI,CAAC,KAAK,QAAQ,aAAa,KAAK,QAAQ,yBAAyB;AACnE,UAAI,WAAW,QAAQ,YAAY,MAAM,aAAa,CAAC,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ,WAAW,GAAI,QAAO,aAAa;AACvI,YAAM,SAAS,CAAC;AAChB,YAAM,SAAS,SAAO;AACpB,YAAI,CAAC,IAAK;AACV,YAAI,QAAQ,SAAU;AACtB,cAAM,OAAO,KAAK,SAAS,cAAc,mBAAmB,GAAG;AAC/D,aAAK,QAAQ,OAAK;AAChB,cAAI,MAAM,SAAU;AACpB,cAAI,OAAO,QAAQ,CAAC,IAAI,EAAG,QAAO,KAAK,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,UAAI,CAAC,SAAS;AACZ,cAAM,YAAY,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW;AACvF,kBAAU,QAAQ,OAAK,OAAO,CAAC,CAAC;AAAA,MAClC,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AACA,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,QAAQ,QAAQ,QAAQ,OAAK,OAAO,CAAC,CAAC;AAAA,MAC7C;AACA,WAAK,SAAS,iBAAiB,KAAK,QAAQ,KAAK,QAAQ,IAAI,OAAK;AAChE,YAAI,CAAC,KAAK,CAAC,KAAK,oBAAoB,KAAK,SAAU,MAAK,oBAAoB,KAAK,QAAQ;AACzF,qBAAa,CAAC;AAAA,MAChB,CAAC;AAAA,IACH,OAAO;AACL,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM,IAAI,UAAU;AAClC,UAAM,WAAW,MAAM;AACvB,QAAI,OAAO,SAAS,YAAY;AAC9B,iBAAW;AACX,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,YAAY;AAC5B,iBAAW;AACX,WAAK;AAAA,IACP;AACA,QAAI,CAAC,KAAM,QAAO,KAAK;AACvB,QAAI,CAAC,GAAI,MAAK,KAAK,QAAQ;AAC3B,QAAI,CAAC,SAAU,YAAW;AAC1B,SAAK,SAAS,iBAAiB,OAAO,MAAM,IAAI,SAAO;AACrD,eAAS,QAAQ;AACjB,eAAS,GAAG;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,+FAA+F;AAC5H,QAAI,CAAC,OAAO,KAAM,OAAM,IAAI,MAAM,0FAA0F;AAC5H,QAAI,OAAO,SAAS,WAAW;AAC7B,WAAK,QAAQ,UAAU;AAAA,IACzB;AACA,QAAI,OAAO,SAAS,YAAY,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO;AACzE,WAAK,QAAQ,SAAS;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,oBAAoB;AACtC,WAAK,QAAQ,mBAAmB;AAAA,IAClC;AACA,QAAI,OAAO,SAAS,cAAc;AAChC,WAAK,QAAQ,aAAa;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,iBAAiB;AACnC,oBAAc,iBAAiB,MAAM;AAAA,IACvC;AACA,QAAI,OAAO,SAAS,aAAa;AAC/B,WAAK,QAAQ,YAAY;AAAA,IAC3B;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,QAAQ,SAAS,KAAK,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,GAAG;AACrB,QAAI,CAAC,KAAK,CAAC,KAAK,UAAW;AAC3B,QAAI,CAAC,UAAU,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAI;AACvC,aAAS,KAAK,GAAG,KAAK,KAAK,UAAU,QAAQ,MAAM;AACjD,YAAM,YAAY,KAAK,UAAU,EAAE;AACnC,UAAI,CAAC,UAAU,KAAK,EAAE,QAAQ,SAAS,IAAI,GAAI;AAC/C,UAAI,KAAK,MAAM,4BAA4B,SAAS,GAAG;AACrD,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,KAAK,UAAU;AAC5B,QAAI,SAAS;AACb,SAAK,uBAAuB;AAC5B,UAAM,WAAW,MAAM;AACvB,SAAK,KAAK,oBAAoB,GAAG;AACjC,UAAM,cAAc,OAAK;AACvB,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK,SAAS,cAAc,mBAAmB,CAAC;AACjE,WAAK,mBAAmB;AACxB,WAAK,oBAAoB,CAAC;AAAA,IAC5B;AACA,UAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAI,GAAG;AACL,oBAAY,CAAC;AACb,aAAK,WAAW,eAAe,CAAC;AAChC,aAAK,uBAAuB;AAC5B,aAAK,KAAK,mBAAmB,CAAC;AAC9B,aAAK,OAAO,IAAI,mBAAmB,CAAC;AAAA,MACtC,OAAO;AACL,aAAK,uBAAuB;AAAA,MAC9B;AACA,eAAS,QAAQ,WAAY;AAC3B,eAAO,OAAO,EAAE,GAAG,SAAS;AAAA,MAC9B,CAAC;AACD,UAAI,SAAU,UAAS,KAAK,WAAY;AACtC,eAAO,OAAO,EAAE,GAAG,SAAS;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,UAAM,SAAS,UAAQ;AACrB,UAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS,iBAAkB,QAAO,CAAC;AAC7D,YAAM,IAAI,SAAS,IAAI,IAAI,OAAO,KAAK,SAAS,cAAc,sBAAsB,IAAI;AACxF,UAAI,GAAG;AACL,YAAI,CAAC,KAAK,UAAU;AAClB,sBAAY,CAAC;AAAA,QACf;AACA,YAAI,CAAC,KAAK,WAAW,SAAU,MAAK,WAAW,eAAe,CAAC;AAC/D,YAAI,KAAK,SAAS,oBAAoB,KAAK,SAAS,iBAAiB,kBAAmB,MAAK,SAAS,iBAAiB,kBAAkB,CAAC;AAAA,MAC5I;AACA,WAAK,cAAc,GAAG,SAAO;AAC3B,aAAK,KAAK,CAAC;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,CAAC,OAAO,KAAK,SAAS,oBAAoB,CAAC,KAAK,SAAS,iBAAiB,OAAO;AACnF,aAAO,KAAK,SAAS,iBAAiB,OAAO,CAAC;AAAA,IAChD,WAAW,CAAC,OAAO,KAAK,SAAS,oBAAoB,KAAK,SAAS,iBAAiB,OAAO;AACzF,UAAI,KAAK,SAAS,iBAAiB,OAAO,WAAW,GAAG;AACtD,aAAK,SAAS,iBAAiB,OAAO,EAAE,KAAK,MAAM;AAAA,MACrD,OAAO;AACL,aAAK,SAAS,iBAAiB,OAAO,MAAM;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,aAAO,GAAG;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,IAAI,WAAW;AAC5B,QAAI,SAAS;AACb,UAAM,SAAS,SAAU,KAAK,MAAM;AAClC,UAAI;AACJ,UAAI,OAAO,SAAS,UAAU;AAC5B,iBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,eAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,QACnC;AACA,kBAAU,OAAO,QAAQ,iCAAiC,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,MACpF,OAAO;AACL,kBAAU;AAAA,UACR,GAAG;AAAA,QACL;AAAA,MACF;AACA,cAAQ,MAAM,QAAQ,OAAO,OAAO;AACpC,cAAQ,OAAO,QAAQ,QAAQ,OAAO;AACtC,cAAQ,KAAK,QAAQ,MAAM,OAAO;AAClC,UAAI,QAAQ,cAAc,GAAI,SAAQ,YAAY,QAAQ,aAAa,aAAa,OAAO;AAC3F,YAAM,eAAe,OAAO,QAAQ,gBAAgB;AACpD,UAAI;AACJ,UAAI,QAAQ,aAAa,MAAM,QAAQ,GAAG,GAAG;AAC3C,oBAAY,IAAI,IAAI,OAAK,GAAG,QAAQ,SAAS,GAAG,YAAY,GAAG,CAAC,EAAE;AAAA,MACpE,OAAO;AACL,oBAAY,QAAQ,YAAY,GAAG,QAAQ,SAAS,GAAG,YAAY,GAAG,GAAG,KAAK;AAAA,MAChF;AACA,aAAO,OAAO,EAAE,WAAW,OAAO;AAAA,IACpC;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,WAAO,KAAK;AACZ,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAAA,EACA,IAAI;AACF,WAAO,KAAK,cAAc,KAAK,WAAW,UAAU,GAAG,SAAS;AAAA,EAClE;AAAA,EACA,SAAS;AACP,WAAO,KAAK,cAAc,KAAK,WAAW,OAAO,GAAG,SAAS;AAAA,EAC/D;AAAA,EACA,oBAAoB,IAAI;AACtB,SAAK,QAAQ,YAAY;AAAA,EAC3B;AAAA,EACA,mBAAmB,IAAI;AACrB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,OAAO,KAAK,mDAAmD,KAAK,SAAS;AAClF,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,QAAQ;AAC7C,WAAK,OAAO,KAAK,8DAA8D,KAAK,SAAS;AAC7F,aAAO;AAAA,IACT;AACA,UAAM,MAAM,QAAQ,OAAO,KAAK,oBAAoB,KAAK,UAAU,CAAC;AACpE,UAAM,cAAc,KAAK,UAAU,KAAK,QAAQ,cAAc;AAC9D,UAAM,UAAU,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AACxD,QAAI,IAAI,YAAY,MAAM,SAAU,QAAO;AAC3C,UAAM,iBAAiB,CAAC,GAAG,MAAM;AAC/B,YAAM,YAAY,KAAK,SAAS,iBAAiB,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;AAClE,aAAO,cAAc,MAAM,cAAc,KAAK,cAAc;AAAA,IAC9D;AACA,QAAI,QAAQ,UAAU;AACpB,YAAM,YAAY,QAAQ,SAAS,MAAM,cAAc;AACvD,UAAI,cAAc,OAAW,QAAO;AAAA,IACtC;AACA,QAAI,KAAK,kBAAkB,KAAK,EAAE,EAAG,QAAO;AAC5C,QAAI,CAAC,KAAK,SAAS,iBAAiB,WAAW,KAAK,QAAQ,aAAa,CAAC,KAAK,QAAQ,wBAAyB,QAAO;AACvH,QAAI,eAAe,KAAK,EAAE,MAAM,CAAC,eAAe,eAAe,SAAS,EAAE,GAAI,QAAO;AACrF,WAAO;AAAA,EACT;AAAA,EACA,eAAe,IAAI,UAAU;AAC3B,UAAM,WAAW,MAAM;AACvB,QAAI,CAAC,KAAK,QAAQ,IAAI;AACpB,UAAI,SAAU,UAAS;AACvB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,QAAI,SAAS,EAAE,EAAG,MAAK,CAAC,EAAE;AAC1B,OAAG,QAAQ,OAAK;AACd,UAAI,KAAK,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAG,MAAK,QAAQ,GAAG,KAAK,CAAC;AAAA,IAC5D,CAAC;AACD,SAAK,cAAc,SAAO;AACxB,eAAS,QAAQ;AACjB,UAAI,SAAU,UAAS,GAAG;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM,UAAU;AAC5B,UAAM,WAAW,MAAM;AACvB,QAAI,SAAS,IAAI,EAAG,QAAO,CAAC,IAAI;AAChC,UAAM,YAAY,KAAK,QAAQ,WAAW,CAAC;AAC3C,UAAM,UAAU,KAAK,OAAO,SAAO,UAAU,QAAQ,GAAG,IAAI,KAAK,KAAK,SAAS,cAAc,gBAAgB,GAAG,CAAC;AACjH,QAAI,CAAC,QAAQ,QAAQ;AACnB,UAAI,SAAU,UAAS;AACvB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,SAAK,QAAQ,UAAU,UAAU,OAAO,OAAO;AAC/C,SAAK,cAAc,SAAO;AACxB,eAAS,QAAQ;AACjB,UAAI,SAAU,UAAS,GAAG;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK;AACP,QAAI,CAAC,IAAK,OAAM,KAAK,qBAAqB,KAAK,aAAa,KAAK,UAAU,SAAS,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK;AACjH,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM,UAAU,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AACvb,UAAM,gBAAgB,KAAK,YAAY,KAAK,SAAS,iBAAiB,IAAI,aAAa,IAAI,CAAC;AAC5F,WAAO,QAAQ,QAAQ,cAAc,wBAAwB,GAAG,CAAC,IAAI,MAAM,IAAI,YAAY,EAAE,QAAQ,OAAO,IAAI,IAAI,QAAQ;AAAA,EAC9H;AAAA,EACA,OAAO,iBAAiB;AACtB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,WAAO,IAAI,MAAK,SAAS,QAAQ;AAAA,EACnC;AAAA,EACA,gBAAgB;AACd,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,UAAM,oBAAoB,QAAQ;AAClC,QAAI,kBAAmB,QAAO,QAAQ;AACtC,UAAM,gBAAgB;AAAA,MACpB,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,QACD,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,MAAK,aAAa;AACpC,QAAI,QAAQ,UAAU,UAAa,QAAQ,WAAW,QAAW;AAC/D,YAAM,SAAS,MAAM,OAAO,MAAM,OAAO;AAAA,IAC3C;AACA,UAAM,gBAAgB,CAAC,SAAS,YAAY,UAAU;AACtD,kBAAc,QAAQ,OAAK;AACzB,YAAM,CAAC,IAAI,KAAK,CAAC;AAAA,IACnB,CAAC;AACD,UAAM,WAAW;AAAA,MACf,GAAG,KAAK;AAAA,IACV;AACA,UAAM,SAAS,QAAQ;AAAA,MACrB,oBAAoB,MAAM,mBAAmB,KAAK,KAAK;AAAA,IACzD;AACA,QAAI,mBAAmB;AACrB,YAAM,QAAQ,IAAI,cAAc,KAAK,MAAM,MAAM,aAAa;AAC9D,YAAM,SAAS,gBAAgB,MAAM;AAAA,IACvC;AACA,UAAM,aAAa,IAAI,WAAW,MAAM,UAAU,aAAa;AAC/D,UAAM,WAAW,GAAG,KAAK,SAAU,OAAO;AACxC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,aAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,MACnC;AACA,YAAM,KAAK,OAAO,GAAG,IAAI;AAAA,IAC3B,CAAC;AACD,UAAM,KAAK,eAAe,QAAQ;AAClC,UAAM,WAAW,UAAU;AAC3B,UAAM,WAAW,iBAAiB,SAAS,QAAQ;AAAA,MACjD,oBAAoB,MAAM,mBAAmB,KAAK,KAAK;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,WAAW,KAAK;AAAA,MAChB,kBAAkB,KAAK;AAAA,IACzB;AAAA,EACF;AACF;AACA,IAAM,WAAW,KAAK,eAAe;AACrC,SAAS,iBAAiB,KAAK;AAE/B,IAAM,iBAAiB,SAAS;AAChC,IAAM,MAAM,SAAS;AACrB,IAAM,OAAO,SAAS;AACtB,IAAM,gBAAgB,SAAS;AAC/B,IAAM,kBAAkB,SAAS;AACjC,IAAM,MAAM,SAAS;AACrB,IAAM,iBAAiB,SAAS;AAChC,IAAM,YAAY,SAAS;AAC3B,IAAM,IAAI,SAAS;AACnB,IAAM,SAAS,SAAS;AACxB,IAAM,sBAAsB,SAAS;AACrC,IAAM,qBAAqB,SAAS;AACpC,IAAM,iBAAiB,SAAS;AAChC,IAAM,gBAAgB,SAAS;", "names": ["t", "copy"]}