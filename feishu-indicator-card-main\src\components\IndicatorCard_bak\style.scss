.indicator-wrapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 8px;
}

.indicator-main {
  height: 100%;
  display: flex;
  flex-direction: row;  // 改为横向布局
  gap: 16px;           // 添加间距
}

.indicator-display {
  flex: 7;             // 占据 70% 空间
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 24px 16px;
  
  .indicator-title {
    font-size: 14px;
    color: var(--semi-color-text-2);
    margin-bottom: 16px;
  }
  
  .indicator-number {
    font-size: 36px;
    font-weight: 600;
    color: var(--semi-color-text-0);
    margin-bottom: 8px;
    line-height: 1.2;
  }
  
  .indicator-percent {
    font-size: 14px;
    color: var(--semi-color-text-2);
  }
}

.indicator-config {
  flex: 3;             // 占据 30% 空间
  border-left: 1px solid var(--semi-color-border);  // 左边添加分隔线
  padding-left: 16px;  // 左侧添加内边距
  
  .config-form {
    margin-bottom: 16px;
    
    :global(.semi-form-field) {
      margin-bottom: 12px;
    }
  }
  
  .config-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 8px;
  }
}