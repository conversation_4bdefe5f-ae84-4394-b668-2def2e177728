.color-picker {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.color-picker-color-container {
    border: 2px solid transparent;
    border-radius: 25%;
}
.color-picker-color {
    width: 18px;
    height: 18px;
    border: 1px solid var(--bg-body);
    border-radius: 25%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selected-icon-container {
    height: 10px;
    width: 5px;
    border-right: 1px solid white;
    border-bottom: 1px solid white;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    transform: rotate(45deg) translate(-1px, -1px);
}

.semi-datepicker-day-selected .semi-datepicker-day-main {
    border-radius: 6px;
}
