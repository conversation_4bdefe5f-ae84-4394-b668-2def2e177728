body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.dashboard-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.config-panel {
  padding: 16px;
  
  .form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .semi-tabs-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .semi-tabs-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.chart-card {
  background: var(--semi-color-bg-2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--semi-color-text-0);
}

.chart-subtitle {
  font-size: 12px;
  color: var(--semi-color-text-2);
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--semi-color-danger);
}
