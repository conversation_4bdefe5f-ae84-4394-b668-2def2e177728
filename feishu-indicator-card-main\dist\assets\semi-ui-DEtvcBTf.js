import{r as $,g as Y,c as Yt,R as d,a as yr,b as Tt}from"./react-vendor-S849DFnq.js";var Ao={exports:{}},pn={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var da=$,ha=Symbol.for("react.element"),pa=Symbol.for("react.fragment"),fa=Object.prototype.hasOwnProperty,ga=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ma={key:!0,ref:!0,__self:!0,__source:!0};function No(r,e,t){var n,s={},o=null,i=null;t!==void 0&&(o=""+t),e.key!==void 0&&(o=""+e.key),e.ref!==void 0&&(i=e.ref);for(n in e)fa.call(e,n)&&!ma.hasOwnProperty(n)&&(s[n]=e[n]);if(r&&r.defaultProps)for(n in e=r.defaultProps,e)s[n]===void 0&&(s[n]=e[n]);return{$$typeof:ha,type:r,key:o,ref:i,props:s,_owner:ga.current}}pn.Fragment=pa;pn.jsx=No;pn.jsxs=No;Ao.exports=pn;var G1=Ao.exports;function ya(){}var ba=ya;const A=Y(ba);var va=Array.isArray,le=va;const _a=Y(le);var Sa=typeof Yt=="object"&&Yt&&Yt.Object===Object&&Yt,jo=Sa,Oa=jo,wa=typeof self=="object"&&self&&self.Object===Object&&self,Ca=Oa||wa||Function("return this")(),ve=Ca,Ta=ve,Ea=Ta.Symbol,ut=Ea,us=ut,Mo=Object.prototype,Pa=Mo.hasOwnProperty,$a=Mo.toString,St=us?us.toStringTag:void 0;function Ia(r){var e=Pa.call(r,St),t=r[St];try{r[St]=void 0;var n=!0}catch{}var s=$a.call(r);return n&&(e?r[St]=t:delete r[St]),s}var xa=Ia,Aa=Object.prototype,Na=Aa.toString;function ja(r){return Na.call(r)}var Ma=ja,ds=ut,Da=xa,Ra=Ma,La="[object Null]",ka="[object Undefined]",hs=ds?ds.toStringTag:void 0;function Fa(r){return r==null?r===void 0?ka:La:hs&&hs in Object(r)?Da(r):Ra(r)}var Re=Fa;function Ba(r){return r!=null&&typeof r=="object"}var _e=Ba,Ka=Re,Wa=_e,za="[object Symbol]";function Ha(r){return typeof r=="symbol"||Wa(r)&&Ka(r)==za}var fn=Ha,Va=le,Ua=fn,Ga=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Xa=/^\w*$/;function qa(r,e){if(Va(r))return!1;var t=typeof r;return t=="number"||t=="symbol"||t=="boolean"||r==null||Ua(r)?!0:Xa.test(r)||!Ga.test(r)||e!=null&&r in Object(e)}var Ya=qa;function Za(r){var e=typeof r;return r!=null&&(e=="object"||e=="function")}var me=Za,Ja=Re,Qa=me,el="[object AsyncFunction]",tl="[object Function]",nl="[object GeneratorFunction]",rl="[object Proxy]";function sl(r){if(!Qa(r))return!1;var e=Ja(r);return e==tl||e==nl||e==el||e==rl}var gn=sl;const fe=Y(gn);var ol=ve,il=ol["__core-js_shared__"],al=il,Vn=al,ps=function(){var r=/[^.]+$/.exec(Vn&&Vn.keys&&Vn.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}();function ll(r){return!!ps&&ps in r}var cl=ll,ul=Function.prototype,dl=ul.toString;function hl(r){if(r!=null){try{return dl.call(r)}catch{}try{return r+""}catch{}}return""}var Do=hl,pl=gn,fl=cl,gl=me,ml=Do,yl=/[\\^$.*+?()[\]{}|]/g,bl=/^\[object .+?Constructor\]$/,vl=Function.prototype,_l=Object.prototype,Sl=vl.toString,Ol=_l.hasOwnProperty,wl=RegExp("^"+Sl.call(Ol).replace(yl,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Cl(r){if(!gl(r)||fl(r))return!1;var e=pl(r)?wl:bl;return e.test(ml(r))}var Tl=Cl;function El(r,e){return r==null?void 0:r[e]}var Pl=El,$l=Tl,Il=Pl;function xl(r,e){var t=Il(r,e);return $l(t)?t:void 0}var Ze=xl,Al=Ze,Nl=Al(Object,"create"),mn=Nl,fs=mn;function jl(){this.__data__=fs?fs(null):{},this.size=0}var Ml=jl;function Dl(r){var e=this.has(r)&&delete this.__data__[r];return this.size-=e?1:0,e}var Rl=Dl,Ll=mn,kl="__lodash_hash_undefined__",Fl=Object.prototype,Bl=Fl.hasOwnProperty;function Kl(r){var e=this.__data__;if(Ll){var t=e[r];return t===kl?void 0:t}return Bl.call(e,r)?e[r]:void 0}var Wl=Kl,zl=mn,Hl=Object.prototype,Vl=Hl.hasOwnProperty;function Ul(r){var e=this.__data__;return zl?e[r]!==void 0:Vl.call(e,r)}var Gl=Ul,Xl=mn,ql="__lodash_hash_undefined__";function Yl(r,e){var t=this.__data__;return this.size+=this.has(r)?0:1,t[r]=Xl&&e===void 0?ql:e,this}var Zl=Yl,Jl=Ml,Ql=Rl,ec=Wl,tc=Gl,nc=Zl;function dt(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var n=r[e];this.set(n[0],n[1])}}dt.prototype.clear=Jl;dt.prototype.delete=Ql;dt.prototype.get=ec;dt.prototype.has=tc;dt.prototype.set=nc;var rc=dt;function sc(){this.__data__=[],this.size=0}var oc=sc;function ic(r,e){return r===e||r!==r&&e!==e}var It=ic,ac=It;function lc(r,e){for(var t=r.length;t--;)if(ac(r[t][0],e))return t;return-1}var yn=lc,cc=yn,uc=Array.prototype,dc=uc.splice;function hc(r){var e=this.__data__,t=cc(e,r);if(t<0)return!1;var n=e.length-1;return t==n?e.pop():dc.call(e,t,1),--this.size,!0}var pc=hc,fc=yn;function gc(r){var e=this.__data__,t=fc(e,r);return t<0?void 0:e[t][1]}var mc=gc,yc=yn;function bc(r){return yc(this.__data__,r)>-1}var vc=bc,_c=yn;function Sc(r,e){var t=this.__data__,n=_c(t,r);return n<0?(++this.size,t.push([r,e])):t[n][1]=e,this}var Oc=Sc,wc=oc,Cc=pc,Tc=mc,Ec=vc,Pc=Oc;function ht(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var n=r[e];this.set(n[0],n[1])}}ht.prototype.clear=wc;ht.prototype.delete=Cc;ht.prototype.get=Tc;ht.prototype.has=Ec;ht.prototype.set=Pc;var bn=ht,$c=Ze,Ic=ve,xc=$c(Ic,"Map"),br=xc,gs=rc,Ac=bn,Nc=br;function jc(){this.size=0,this.__data__={hash:new gs,map:new(Nc||Ac),string:new gs}}var Mc=jc;function Dc(r){var e=typeof r;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?r!=="__proto__":r===null}var Rc=Dc,Lc=Rc;function kc(r,e){var t=r.__data__;return Lc(e)?t[typeof e=="string"?"string":"hash"]:t.map}var vn=kc,Fc=vn;function Bc(r){var e=Fc(this,r).delete(r);return this.size-=e?1:0,e}var Kc=Bc,Wc=vn;function zc(r){return Wc(this,r).get(r)}var Hc=zc,Vc=vn;function Uc(r){return Vc(this,r).has(r)}var Gc=Uc,Xc=vn;function qc(r,e){var t=Xc(this,r),n=t.size;return t.set(r,e),this.size+=t.size==n?0:1,this}var Yc=qc,Zc=Mc,Jc=Kc,Qc=Hc,eu=Gc,tu=Yc;function pt(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var n=r[e];this.set(n[0],n[1])}}pt.prototype.clear=Zc;pt.prototype.delete=Jc;pt.prototype.get=Qc;pt.prototype.has=eu;pt.prototype.set=tu;var vr=pt,Ro=vr,nu="Expected a function";function _r(r,e){if(typeof r!="function"||e!=null&&typeof e!="function")throw new TypeError(nu);var t=function(){var n=arguments,s=e?e.apply(this,n):n[0],o=t.cache;if(o.has(s))return o.get(s);var i=r.apply(this,n);return t.cache=o.set(s,i)||o,i};return t.cache=new(_r.Cache||Ro),t}_r.Cache=Ro;var ru=_r,su=ru,ou=500;function iu(r){var e=su(r,function(n){return t.size===ou&&t.clear(),n}),t=e.cache;return e}var au=iu,lu=au,cu=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,uu=/\\(\\)?/g,du=lu(function(r){var e=[];return r.charCodeAt(0)===46&&e.push(""),r.replace(cu,function(t,n,s,o){e.push(s?o.replace(uu,"$1"):n||t)}),e}),hu=du;function pu(r,e){for(var t=-1,n=r==null?0:r.length,s=Array(n);++t<n;)s[t]=e(r[t],t,r);return s}var Lo=pu,ms=ut,fu=Lo,gu=le,mu=fn,ys=ms?ms.prototype:void 0,bs=ys?ys.toString:void 0;function ko(r){if(typeof r=="string")return r;if(gu(r))return fu(r,ko)+"";if(mu(r))return bs?bs.call(r):"";var e=r+"";return e=="0"&&1/r==-1/0?"-0":e}var yu=ko,bu=yu;function vu(r){return r==null?"":bu(r)}var _u=vu,Su=le,Ou=Ya,wu=hu,Cu=_u;function Tu(r,e){return Su(r)?r:Ou(r,e)?[r]:wu(Cu(r))}var ft=Tu,Eu=fn;function Pu(r){if(typeof r=="string"||Eu(r))return r;var e=r+"";return e=="0"&&1/r==-1/0?"-0":e}var _n=Pu,$u=ft,Iu=_n;function xu(r,e){e=$u(e,r);for(var t=0,n=e.length;r!=null&&t<n;)r=r[Iu(e[t++])];return t&&t==n?r:void 0}var Sr=xu,Au=Sr;function Nu(r,e,t){var n=r==null?void 0:Au(r,e);return n===void 0?t:n}var ju=Nu;const R=Y(ju),Fo=function(r){if(R(process,"env.NODE_ENV")==="development"){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];console.log(r,...t)}};class ye{static get cssClasses(){return{}}static get strings(){return{}}static get numbers(){return{}}static get defaultAdapter(){return{getProp:A,getProps:A,getState:A,getStates:A,setState:A,getContext:A,getContexts:A,getCache:A,setCache:A,getCaches:A,stopPropagation:A,persistEvent:A}}constructor(e){this._adapter=Object.assign(Object.assign({},ye.defaultAdapter),e)}getProp(e){return this._adapter.getProp(e)}getProps(){return this._adapter.getProps()}getState(e){return this._adapter.getState(e)}getStates(){return this._adapter.getStates()}setState(e,t){return this._adapter.setState(Object.assign({},e),t)}getContext(e){return this._adapter.getContext(e)}getContexts(){return this._adapter.getContexts()}getCaches(){return this._adapter.getCaches()}getCache(e){return this._adapter.getCache(e)}setCache(e,t){return e&&this._adapter.setCache(e,t)}stopPropagation(e){this._adapter.stopPropagation(e)}_isControlledComponent(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"value";const t=this.getProps();return e in t}_isInProps(e){const t=this.getProps();return e in t}init(e){}destroy(){}log(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];Fo(e,...n)}_persistEvent(e){this._adapter.persistEvent(e)}}function xt(r){return Object.keys(r).reduce((e,t)=>(t.substr(0,5)==="data-"&&(e[t]=r[t]),e),{})}const{hasOwnProperty:Mu}=Object.prototype;class ce extends $.Component{constructor(e){super(e),this.isControlled=t=>!!(t&&this.props&&typeof this.props=="object"&&Mu.call(this.props,t)),this.setStateAsync=t=>new Promise(n=>{this.setState(t,n)}),this.cache={},this.foundation=null}componentDidMount(){this.foundation&&typeof this.foundation.init=="function"&&this.foundation.init()}componentWillUnmount(){this.foundation&&typeof this.foundation.destroy=="function"&&this.foundation.destroy(),this.cache={}}get adapter(){return{getContext:e=>{if(this.context&&e)return this.context[e]},getContexts:()=>this.context,getProp:e=>this.props[e],getProps:()=>this.props,getState:e=>this.state[e],getStates:()=>this.state,setState:(e,t)=>this.setState(Object.assign({},e),t),getCache:e=>e&&this.cache[e],getCaches:()=>this.cache,setCache:(e,t)=>e&&(this.cache[e]=t),stopPropagation:e=>{try{e.stopPropagation(),e.nativeEvent&&e.nativeEvent.stopImmediatePropagation()}catch{}},persistEvent:e=>{e&&e.persist&&typeof e.persist=="function"&&e.persist()}}}log(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return Fo(e,...n)}getDataAttr(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props;return xt(e)}}ce.propTypes={};ce.defaultProps={};var Du=ve,Ru=function(){return Du.Date.now()},Lu=Ru,ku=/\s/;function Fu(r){for(var e=r.length;e--&&ku.test(r.charAt(e)););return e}var Bu=Fu,Ku=Bu,Wu=/^\s+/;function zu(r){return r&&r.slice(0,Ku(r)+1).replace(Wu,"")}var Hu=zu,Vu=Hu,vs=me,Uu=fn,_s=NaN,Gu=/^[-+]0x[0-9a-f]+$/i,Xu=/^0b[01]+$/i,qu=/^0o[0-7]+$/i,Yu=parseInt;function Zu(r){if(typeof r=="number")return r;if(Uu(r))return _s;if(vs(r)){var e=typeof r.valueOf=="function"?r.valueOf():r;r=vs(e)?e+"":e}if(typeof r!="string")return r===0?r:+r;r=Vu(r);var t=Xu.test(r);return t||qu.test(r)?Yu(r.slice(2),t?2:8):Gu.test(r)?_s:+r}var Ju=Zu,Qu=me,Un=Lu,Ss=Ju,ed="Expected a function",td=Math.max,nd=Math.min;function rd(r,e,t){var n,s,o,i,l,c,u=0,h=!1,f=!1,m=!0;if(typeof r!="function")throw new TypeError(ed);e=Ss(e)||0,Qu(t)&&(h=!!t.leading,f="maxWait"in t,o=f?td(Ss(t.maxWait)||0,e):o,m="trailing"in t?!!t.trailing:m);function y(S){var w=n,P=s;return n=s=void 0,u=S,i=r.apply(P,w),i}function v(S){return u=S,l=setTimeout(b,e),h?y(S):i}function p(S){var w=S-c,P=S-u,x=e-w;return f?nd(x,o-P):x}function g(S){var w=S-c,P=S-u;return c===void 0||w>=e||w<0||f&&P>=o}function b(){var S=Un();if(g(S))return _(S);l=setTimeout(b,p(S))}function _(S){return l=void 0,m&&n?y(S):(n=s=void 0,i)}function O(){l!==void 0&&clearTimeout(l),u=0,n=c=s=l=void 0}function T(){return l===void 0?i:_(Un())}function C(){var S=Un(),w=g(S);if(n=arguments,s=this,c=S,w){if(l===void 0)return v(c);if(f)return clearTimeout(l),l=setTimeout(b,e),y(c)}return l===void 0&&(l=setTimeout(b,e)),i}return C.cancel=O,C.flush=T,C}var sd=rd,od=sd,id=me,ad="Expected a function";function ld(r,e,t){var n=!0,s=!0;if(typeof r!="function")throw new TypeError(ad);return id(t)&&(n="leading"in t?!!t.leading:n,s="trailing"in t?!!t.trailing:s),od(r,e,{leading:n,maxWait:e,trailing:s})}var cd=ld;const Os=Y(cd);var Bo={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(r){(function(){var e={}.hasOwnProperty;function t(){for(var o="",i=0;i<arguments.length;i++){var l=arguments[i];l&&(o=s(o,n(l)))}return o}function n(o){if(typeof o=="string"||typeof o=="number")return o;if(typeof o!="object")return"";if(Array.isArray(o))return t.apply(null,o);if(o.toString!==Object.prototype.toString&&!o.toString.toString().includes("[native code]"))return o.toString();var i="";for(var l in o)e.call(o,l)&&o[l]&&(i=s(i,l));return i}function s(o,i){return i?o?o+" "+i:o+i:o}r.exports?(t.default=t,r.exports=t):window.classNames=t})()})(Bo);var ud=Bo.exports;const E=Y(ud);var Ko={exports:{}},dd="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",hd=dd,pd=hd;function Wo(){}function zo(){}zo.resetWarningCache=Wo;var fd=function(){function r(n,s,o,i,l,c){if(c!==pd){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}r.isRequired=r;function e(){return r}var t={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:e,element:r,elementType:r,instanceOf:e,node:r,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:zo,resetWarningCache:Wo};return t.PropTypes=t,t};Ko.exports=fd();var gd=Ko.exports;const a=Y(gd),D="semi",md=["default","error","warning","success"],Or={PREFIX:`${D}-typography`},Pt={TYPE:["primary","secondary","danger","warning","success","tertiary","quaternary"],SIZE:["normal","small","inherit"],SPACING:["normal","extended"]};var yd=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const bd=Or.PREFIX;class wr extends $.PureComponent{render(){const e=this.props,{component:t,className:n,children:s,forwardRef:o}=e,i=yd(e,["component","className","children","forwardRef"]),l=t,c=E(bd,n);return d.createElement(l,Object.assign({className:c,ref:o},i),s)}}wr.defaultProps={component:"article",style:{},className:""};wr.propTypes={component:a.string,style:a.object,className:a.string};function vd(r){return r===null}var _d=vd;const Sd=Y(_d);var Od=Re,wd=le,Cd=_e,Td="[object String]";function Ed(r){return typeof r=="string"||!wd(r)&&Cd(r)&&Od(r)==Td}var Pd=Ed;const ee=Y(Pd);var $d=bn;function Id(){this.__data__=new $d,this.size=0}var xd=Id;function Ad(r){var e=this.__data__,t=e.delete(r);return this.size=e.size,t}var Nd=Ad;function jd(r){return this.__data__.get(r)}var Md=jd;function Dd(r){return this.__data__.has(r)}var Rd=Dd,Ld=bn,kd=br,Fd=vr,Bd=200;function Kd(r,e){var t=this.__data__;if(t instanceof Ld){var n=t.__data__;if(!kd||n.length<Bd-1)return n.push([r,e]),this.size=++t.size,this;t=this.__data__=new Fd(n)}return t.set(r,e),this.size=t.size,this}var Wd=Kd,zd=bn,Hd=xd,Vd=Nd,Ud=Md,Gd=Rd,Xd=Wd;function gt(r){var e=this.__data__=new zd(r);this.size=e.size}gt.prototype.clear=Hd;gt.prototype.delete=Vd;gt.prototype.get=Ud;gt.prototype.has=Gd;gt.prototype.set=Xd;var Cr=gt,qd=Ze,Yd=function(){try{var r=qd(Object,"defineProperty");return r({},"",{}),r}catch{}}(),Ho=Yd,ws=Ho;function Zd(r,e,t){e=="__proto__"&&ws?ws(r,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):r[e]=t}var Tr=Zd,Jd=Tr,Qd=It;function eh(r,e,t){(t!==void 0&&!Qd(r[e],t)||t===void 0&&!(e in r))&&Jd(r,e,t)}var Vo=eh;function th(r){return function(e,t,n){for(var s=-1,o=Object(e),i=n(e),l=i.length;l--;){var c=i[r?l:++s];if(t(o[c],c,o)===!1)break}return e}}var nh=th,rh=nh,sh=rh(),Uo=sh,rn={exports:{}};rn.exports;(function(r,e){var t=ve,n=e&&!e.nodeType&&e,s=n&&!0&&r&&!r.nodeType&&r,o=s&&s.exports===n,i=o?t.Buffer:void 0,l=i?i.allocUnsafe:void 0;function c(u,h){if(h)return u.slice();var f=u.length,m=l?l(f):new u.constructor(f);return u.copy(m),m}r.exports=c})(rn,rn.exports);var Go=rn.exports,oh=ve,ih=oh.Uint8Array,Xo=ih,Cs=Xo;function ah(r){var e=new r.constructor(r.byteLength);return new Cs(e).set(new Cs(r)),e}var Er=ah,lh=Er;function ch(r,e){var t=e?lh(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.length)}var qo=ch;function uh(r,e){var t=-1,n=r.length;for(e||(e=Array(n));++t<n;)e[t]=r[t];return e}var Yo=uh,dh=me,Ts=Object.create,hh=function(){function r(){}return function(e){if(!dh(e))return{};if(Ts)return Ts(e);r.prototype=e;var t=new r;return r.prototype=void 0,t}}(),ph=hh;function fh(r,e){return function(t){return r(e(t))}}var Zo=fh,gh=Zo,mh=gh(Object.getPrototypeOf,Object),Pr=mh,yh=Object.prototype;function bh(r){var e=r&&r.constructor,t=typeof e=="function"&&e.prototype||yh;return r===t}var Sn=bh,vh=ph,_h=Pr,Sh=Sn;function Oh(r){return typeof r.constructor=="function"&&!Sh(r)?vh(_h(r)):{}}var Jo=Oh,wh=Re,Ch=_e,Th="[object Arguments]";function Eh(r){return Ch(r)&&wh(r)==Th}var Ph=Eh,Es=Ph,$h=_e,Qo=Object.prototype,Ih=Qo.hasOwnProperty,xh=Qo.propertyIsEnumerable,Ah=Es(function(){return arguments}())?Es:function(r){return $h(r)&&Ih.call(r,"callee")&&!xh.call(r,"callee")},At=Ah,Nh=9007199254740991;function jh(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Nh}var $r=jh,Mh=gn,Dh=$r;function Rh(r){return r!=null&&Dh(r.length)&&!Mh(r)}var mt=Rh,Lh=mt,kh=_e;function Fh(r){return kh(r)&&Lh(r)}var Bh=Fh,sn={exports:{}};function Kh(){return!1}var Wh=Kh;sn.exports;(function(r,e){var t=ve,n=Wh,s=e&&!e.nodeType&&e,o=s&&!0&&r&&!r.nodeType&&r,i=o&&o.exports===s,l=i?t.Buffer:void 0,c=l?l.isBuffer:void 0,u=c||n;r.exports=u})(sn,sn.exports);var Nt=sn.exports,zh=Re,Hh=Pr,Vh=_e,Uh="[object Object]",Gh=Function.prototype,Xh=Object.prototype,ei=Gh.toString,qh=Xh.hasOwnProperty,Yh=ei.call(Object);function Zh(r){if(!Vh(r)||zh(r)!=Uh)return!1;var e=Hh(r);if(e===null)return!0;var t=qh.call(e,"constructor")&&e.constructor;return typeof t=="function"&&t instanceof t&&ei.call(t)==Yh}var ti=Zh,Jh=Re,Qh=$r,ep=_e,tp="[object Arguments]",np="[object Array]",rp="[object Boolean]",sp="[object Date]",op="[object Error]",ip="[object Function]",ap="[object Map]",lp="[object Number]",cp="[object Object]",up="[object RegExp]",dp="[object Set]",hp="[object String]",pp="[object WeakMap]",fp="[object ArrayBuffer]",gp="[object DataView]",mp="[object Float32Array]",yp="[object Float64Array]",bp="[object Int8Array]",vp="[object Int16Array]",_p="[object Int32Array]",Sp="[object Uint8Array]",Op="[object Uint8ClampedArray]",wp="[object Uint16Array]",Cp="[object Uint32Array]",z={};z[mp]=z[yp]=z[bp]=z[vp]=z[_p]=z[Sp]=z[Op]=z[wp]=z[Cp]=!0;z[tp]=z[np]=z[fp]=z[rp]=z[gp]=z[sp]=z[op]=z[ip]=z[ap]=z[lp]=z[cp]=z[up]=z[dp]=z[hp]=z[pp]=!1;function Tp(r){return ep(r)&&Qh(r.length)&&!!z[Jh(r)]}var Ep=Tp;function Pp(r){return function(e){return r(e)}}var Ir=Pp,on={exports:{}};on.exports;(function(r,e){var t=jo,n=e&&!e.nodeType&&e,s=n&&!0&&r&&!r.nodeType&&r,o=s&&s.exports===n,i=o&&t.process,l=function(){try{var c=s&&s.require&&s.require("util").types;return c||i&&i.binding&&i.binding("util")}catch{}}();r.exports=l})(on,on.exports);var xr=on.exports,$p=Ep,Ip=Ir,Ps=xr,$s=Ps&&Ps.isTypedArray,xp=$s?Ip($s):$p,On=xp;function Ap(r,e){if(!(e==="constructor"&&typeof r[e]=="function")&&e!="__proto__")return r[e]}var ni=Ap,Np=Tr,jp=It,Mp=Object.prototype,Dp=Mp.hasOwnProperty;function Rp(r,e,t){var n=r[e];(!(Dp.call(r,e)&&jp(n,t))||t===void 0&&!(e in r))&&Np(r,e,t)}var Ar=Rp,Lp=Ar,kp=Tr;function Fp(r,e,t,n){var s=!t;t||(t={});for(var o=-1,i=e.length;++o<i;){var l=e[o],c=n?n(t[l],r[l],l,t,r):void 0;c===void 0&&(c=r[l]),s?kp(t,l,c):Lp(t,l,c)}return t}var yt=Fp;function Bp(r,e){for(var t=-1,n=Array(r);++t<r;)n[t]=e(t);return n}var Kp=Bp,Wp=9007199254740991,zp=/^(?:0|[1-9]\d*)$/;function Hp(r,e){var t=typeof r;return e=e??Wp,!!e&&(t=="number"||t!="symbol"&&zp.test(r))&&r>-1&&r%1==0&&r<e}var wn=Hp,Vp=Kp,Up=At,Gp=le,Xp=Nt,qp=wn,Yp=On,Zp=Object.prototype,Jp=Zp.hasOwnProperty;function Qp(r,e){var t=Gp(r),n=!t&&Up(r),s=!t&&!n&&Xp(r),o=!t&&!n&&!s&&Yp(r),i=t||n||s||o,l=i?Vp(r.length,String):[],c=l.length;for(var u in r)(e||Jp.call(r,u))&&!(i&&(u=="length"||s&&(u=="offset"||u=="parent")||o&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||qp(u,c)))&&l.push(u);return l}var ri=Qp;function ef(r){var e=[];if(r!=null)for(var t in Object(r))e.push(t);return e}var tf=ef,nf=me,rf=Sn,sf=tf,of=Object.prototype,af=of.hasOwnProperty;function lf(r){if(!nf(r))return sf(r);var e=rf(r),t=[];for(var n in r)n=="constructor"&&(e||!af.call(r,n))||t.push(n);return t}var cf=lf,uf=ri,df=cf,hf=mt;function pf(r){return hf(r)?uf(r,!0):df(r)}var jt=pf,ff=yt,gf=jt;function mf(r){return ff(r,gf(r))}var yf=mf,Is=Vo,bf=Go,vf=qo,_f=Yo,Sf=Jo,xs=At,As=le,Of=Bh,wf=Nt,Cf=gn,Tf=me,Ef=ti,Pf=On,Ns=ni,$f=yf;function If(r,e,t,n,s,o,i){var l=Ns(r,t),c=Ns(e,t),u=i.get(c);if(u){Is(r,t,u);return}var h=o?o(l,c,t+"",r,e,i):void 0,f=h===void 0;if(f){var m=As(c),y=!m&&wf(c),v=!m&&!y&&Pf(c);h=c,m||y||v?As(l)?h=l:Of(l)?h=_f(l):y?(f=!1,h=bf(c,!0)):v?(f=!1,h=vf(c,!0)):h=[]:Ef(c)||xs(c)?(h=l,xs(l)?h=$f(l):(!Tf(l)||Cf(l))&&(h=Sf(c))):f=!1}f&&(i.set(c,h),s(h,c,n,o,i),i.delete(c)),Is(r,t,h)}var xf=If,Af=Cr,Nf=Vo,jf=Uo,Mf=xf,Df=me,Rf=jt,Lf=ni;function si(r,e,t,n,s){r!==e&&jf(e,function(o,i){if(s||(s=new Af),Df(o))Mf(r,e,i,t,si,n,s);else{var l=n?n(Lf(r,i),o,i+"",r,e,s):void 0;l===void 0&&(l=o),Nf(r,i,l)}},Rf)}var kf=si;function Ff(r){return r}var Nr=Ff;function Bf(r,e,t){switch(t.length){case 0:return r.call(e);case 1:return r.call(e,t[0]);case 2:return r.call(e,t[0],t[1]);case 3:return r.call(e,t[0],t[1],t[2])}return r.apply(e,t)}var Kf=Bf,Wf=Kf,js=Math.max;function zf(r,e,t){return e=js(e===void 0?r.length-1:e,0),function(){for(var n=arguments,s=-1,o=js(n.length-e,0),i=Array(o);++s<o;)i[s]=n[e+s];s=-1;for(var l=Array(e+1);++s<e;)l[s]=n[s];return l[e]=t(i),Wf(r,this,l)}}var oi=zf;function Hf(r){return function(){return r}}var Vf=Hf,Uf=Vf,Ms=Ho,Gf=Nr,Xf=Ms?function(r,e){return Ms(r,"toString",{configurable:!0,enumerable:!1,value:Uf(e),writable:!0})}:Gf,qf=Xf,Yf=800,Zf=16,Jf=Date.now;function Qf(r){var e=0,t=0;return function(){var n=Jf(),s=Zf-(n-t);if(t=n,s>0){if(++e>=Yf)return arguments[0]}else e=0;return r.apply(void 0,arguments)}}var eg=Qf,tg=qf,ng=eg,rg=ng(tg),ii=rg,sg=Nr,og=oi,ig=ii;function ag(r,e){return ig(og(r,e,sg),r+"")}var lg=ag,cg=It,ug=mt,dg=wn,hg=me;function pg(r,e,t){if(!hg(t))return!1;var n=typeof e;return(n=="number"?ug(t)&&dg(e,t.length):n=="string"&&e in t)?cg(t[e],r):!1}var fg=pg,gg=lg,mg=fg;function yg(r){return gg(function(e,t){var n=-1,s=t.length,o=s>1?t[s-1]:void 0,i=s>2?t[2]:void 0;for(o=r.length>3&&typeof o=="function"?(s--,o):void 0,i&&mg(t[0],t[1],i)&&(o=s<3?void 0:o,s=1),e=Object(e);++n<s;){var l=t[n];l&&r(e,l,n,o)}return e})}var bg=yg,vg=kf,_g=bg,Sg=_g(function(r,e,t){vg(r,e,t)}),Og=Sg;const wg=Y(Og);function Cg(r,e){for(var t=-1,n=r==null?0:r.length;++t<n&&e(r[t],t,r)!==!1;);return r}var ai=Cg,Tg=Zo,Eg=Tg(Object.keys,Object),Pg=Eg,$g=Sn,Ig=Pg,xg=Object.prototype,Ag=xg.hasOwnProperty;function Ng(r){if(!$g(r))return Ig(r);var e=[];for(var t in Object(r))Ag.call(r,t)&&t!="constructor"&&e.push(t);return e}var li=Ng,jg=ri,Mg=li,Dg=mt;function Rg(r){return Dg(r)?jg(r):Mg(r)}var Cn=Rg,Lg=yt,kg=Cn;function Fg(r,e){return r&&Lg(e,kg(e),r)}var Bg=Fg,Kg=yt,Wg=jt;function zg(r,e){return r&&Kg(e,Wg(e),r)}var Hg=zg;function Vg(r,e){for(var t=-1,n=r==null?0:r.length,s=0,o=[];++t<n;){var i=r[t];e(i,t,r)&&(o[s++]=i)}return o}var Ug=Vg;function Gg(){return[]}var ci=Gg,Xg=Ug,qg=ci,Yg=Object.prototype,Zg=Yg.propertyIsEnumerable,Ds=Object.getOwnPropertySymbols,Jg=Ds?function(r){return r==null?[]:(r=Object(r),Xg(Ds(r),function(e){return Zg.call(r,e)}))}:qg,jr=Jg,Qg=yt,em=jr;function tm(r,e){return Qg(r,em(r),e)}var nm=tm;function rm(r,e){for(var t=-1,n=e.length,s=r.length;++t<n;)r[s+t]=e[t];return r}var Mr=rm,sm=Mr,om=Pr,im=jr,am=ci,lm=Object.getOwnPropertySymbols,cm=lm?function(r){for(var e=[];r;)sm(e,im(r)),r=om(r);return e}:am,ui=cm,um=yt,dm=ui;function hm(r,e){return um(r,dm(r),e)}var pm=hm,fm=Mr,gm=le;function mm(r,e,t){var n=e(r);return gm(r)?n:fm(n,t(r))}var di=mm,ym=di,bm=jr,vm=Cn;function _m(r){return ym(r,vm,bm)}var hi=_m,Sm=di,Om=ui,wm=jt;function Cm(r){return Sm(r,wm,Om)}var pi=Cm,Tm=Ze,Em=ve,Pm=Tm(Em,"DataView"),$m=Pm,Im=Ze,xm=ve,Am=Im(xm,"Promise"),Nm=Am,jm=Ze,Mm=ve,Dm=jm(Mm,"Set"),Rm=Dm,Lm=Ze,km=ve,Fm=Lm(km,"WeakMap"),Bm=Fm,or=$m,ir=br,ar=Nm,lr=Rm,cr=Bm,fi=Re,bt=Do,Rs="[object Map]",Km="[object Object]",Ls="[object Promise]",ks="[object Set]",Fs="[object WeakMap]",Bs="[object DataView]",Wm=bt(or),zm=bt(ir),Hm=bt(ar),Vm=bt(lr),Um=bt(cr),We=fi;(or&&We(new or(new ArrayBuffer(1)))!=Bs||ir&&We(new ir)!=Rs||ar&&We(ar.resolve())!=Ls||lr&&We(new lr)!=ks||cr&&We(new cr)!=Fs)&&(We=function(r){var e=fi(r),t=e==Km?r.constructor:void 0,n=t?bt(t):"";if(n)switch(n){case Wm:return Bs;case zm:return Rs;case Hm:return Ls;case Vm:return ks;case Um:return Fs}return e});var Mt=We,Gm=Object.prototype,Xm=Gm.hasOwnProperty;function qm(r){var e=r.length,t=new r.constructor(e);return e&&typeof r[0]=="string"&&Xm.call(r,"index")&&(t.index=r.index,t.input=r.input),t}var Ym=qm,Zm=Er;function Jm(r,e){var t=e?Zm(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.byteLength)}var Qm=Jm,ey=/\w*$/;function ty(r){var e=new r.constructor(r.source,ey.exec(r));return e.lastIndex=r.lastIndex,e}var ny=ty,Ks=ut,Ws=Ks?Ks.prototype:void 0,zs=Ws?Ws.valueOf:void 0;function ry(r){return zs?Object(zs.call(r)):{}}var sy=ry,oy=Er,iy=Qm,ay=ny,ly=sy,cy=qo,uy="[object Boolean]",dy="[object Date]",hy="[object Map]",py="[object Number]",fy="[object RegExp]",gy="[object Set]",my="[object String]",yy="[object Symbol]",by="[object ArrayBuffer]",vy="[object DataView]",_y="[object Float32Array]",Sy="[object Float64Array]",Oy="[object Int8Array]",wy="[object Int16Array]",Cy="[object Int32Array]",Ty="[object Uint8Array]",Ey="[object Uint8ClampedArray]",Py="[object Uint16Array]",$y="[object Uint32Array]";function Iy(r,e,t){var n=r.constructor;switch(e){case by:return oy(r);case uy:case dy:return new n(+r);case vy:return iy(r,t);case _y:case Sy:case Oy:case wy:case Cy:case Ty:case Ey:case Py:case $y:return cy(r,t);case hy:return new n;case py:case my:return new n(r);case fy:return ay(r);case gy:return new n;case yy:return ly(r)}}var xy=Iy,Ay=Mt,Ny=_e,jy="[object Map]";function My(r){return Ny(r)&&Ay(r)==jy}var Dy=My,Ry=Dy,Ly=Ir,Hs=xr,Vs=Hs&&Hs.isMap,ky=Vs?Ly(Vs):Ry,Fy=ky,By=Mt,Ky=_e,Wy="[object Set]";function zy(r){return Ky(r)&&By(r)==Wy}var Hy=zy,Vy=Hy,Uy=Ir,Us=xr,Gs=Us&&Us.isSet,Gy=Gs?Uy(Gs):Vy,Xy=Gy,qy=Cr,Yy=ai,Zy=Ar,Jy=Bg,Qy=Hg,eb=Go,tb=Yo,nb=nm,rb=pm,sb=hi,ob=pi,ib=Mt,ab=Ym,lb=xy,cb=Jo,ub=le,db=Nt,hb=Fy,pb=me,fb=Xy,gb=Cn,mb=jt,yb=1,bb=2,vb=4,gi="[object Arguments]",_b="[object Array]",Sb="[object Boolean]",Ob="[object Date]",wb="[object Error]",mi="[object Function]",Cb="[object GeneratorFunction]",Tb="[object Map]",Eb="[object Number]",yi="[object Object]",Pb="[object RegExp]",$b="[object Set]",Ib="[object String]",xb="[object Symbol]",Ab="[object WeakMap]",Nb="[object ArrayBuffer]",jb="[object DataView]",Mb="[object Float32Array]",Db="[object Float64Array]",Rb="[object Int8Array]",Lb="[object Int16Array]",kb="[object Int32Array]",Fb="[object Uint8Array]",Bb="[object Uint8ClampedArray]",Kb="[object Uint16Array]",Wb="[object Uint32Array]",W={};W[gi]=W[_b]=W[Nb]=W[jb]=W[Sb]=W[Ob]=W[Mb]=W[Db]=W[Rb]=W[Lb]=W[kb]=W[Tb]=W[Eb]=W[yi]=W[Pb]=W[$b]=W[Ib]=W[xb]=W[Fb]=W[Bb]=W[Kb]=W[Wb]=!0;W[wb]=W[mi]=W[Ab]=!1;function en(r,e,t,n,s,o){var i,l=e&yb,c=e&bb,u=e&vb;if(t&&(i=s?t(r,n,s,o):t(r)),i!==void 0)return i;if(!pb(r))return r;var h=ub(r);if(h){if(i=ab(r),!l)return tb(r,i)}else{var f=ib(r),m=f==mi||f==Cb;if(db(r))return eb(r,l);if(f==yi||f==gi||m&&!s){if(i=c||m?{}:cb(r),!l)return c?rb(r,Qy(i,r)):nb(r,Jy(i,r))}else{if(!W[f])return s?r:{};i=lb(r,f,l)}}o||(o=new qy);var y=o.get(r);if(y)return y;o.set(r,i),fb(r)?r.forEach(function(g){i.add(en(g,e,t,g,r,o))}):hb(r)&&r.forEach(function(g,b){i.set(b,en(g,e,t,b,r,o))});var v=u?c?ob:sb:c?mb:gb,p=h?void 0:v(r);return Yy(p||r,function(g,b){p&&(b=g,g=r[b]),Zy(i,b,en(g,e,t,b,r,o))}),i}var zb=en;function Hb(r){var e=r==null?0:r.length;return e?r[e-1]:void 0}var Vb=Hb;function Ub(r,e,t){var n=-1,s=r.length;e<0&&(e=-e>s?0:s+e),t=t>s?s:t,t<0&&(t+=s),s=e>t?0:t-e>>>0,e>>>=0;for(var o=Array(s);++n<s;)o[n]=r[n+e];return o}var Gb=Ub,Xb=Sr,qb=Gb;function Yb(r,e){return e.length<2?r:Xb(r,qb(e,0,-1))}var Zb=Yb,Jb=ft,Qb=Vb,ev=Zb,tv=_n;function nv(r,e){return e=Jb(e,r),r=ev(r,e),r==null||delete r[tv(Qb(e))]}var rv=nv,sv=ti;function ov(r){return sv(r)?void 0:r}var iv=ov,Xs=ut,av=At,lv=le,qs=Xs?Xs.isConcatSpreadable:void 0;function cv(r){return lv(r)||av(r)||!!(qs&&r&&r[qs])}var uv=cv,dv=Mr,hv=uv;function bi(r,e,t,n,s){var o=-1,i=r.length;for(t||(t=hv),s||(s=[]);++o<i;){var l=r[o];e>0&&t(l)?e>1?bi(l,e-1,t,n,s):dv(s,l):n||(s[s.length]=l)}return s}var pv=bi,fv=pv;function gv(r){var e=r==null?0:r.length;return e?fv(r,1):[]}var mv=gv,yv=mv,bv=oi,vv=ii;function _v(r){return vv(bv(r,void 0,yv),r+"")}var vi=_v,Sv=Lo,Ov=zb,wv=rv,Cv=ft,Tv=yt,Ev=iv,Pv=vi,$v=pi,Iv=1,xv=2,Av=4,Nv=Pv(function(r,e){var t={};if(r==null)return t;var n=!1;e=Sv(e,function(o){return o=Cv(o,r),n||(n=o.length>1),o}),Tv(r,$v(r),t),n&&(t=Ov(t,Iv|xv|Av,Ev));for(var s=e.length;s--;)wv(t,e[s]);return t}),jv=Nv;const Dt=Y(jv);function Mv(r){return r===void 0}var Dv=Mv;const Et=Y(Dv);var Rv="__lodash_hash_undefined__";function Lv(r){return this.__data__.set(r,Rv),this}var kv=Lv;function Fv(r){return this.__data__.has(r)}var Bv=Fv,Kv=vr,Wv=kv,zv=Bv;function an(r){var e=-1,t=r==null?0:r.length;for(this.__data__=new Kv;++e<t;)this.add(r[e])}an.prototype.add=an.prototype.push=Wv;an.prototype.has=zv;var Hv=an;function Vv(r,e){for(var t=-1,n=r==null?0:r.length;++t<n;)if(e(r[t],t,r))return!0;return!1}var Uv=Vv;function Gv(r,e){return r.has(e)}var Xv=Gv,qv=Hv,Yv=Uv,Zv=Xv,Jv=1,Qv=2;function e_(r,e,t,n,s,o){var i=t&Jv,l=r.length,c=e.length;if(l!=c&&!(i&&c>l))return!1;var u=o.get(r),h=o.get(e);if(u&&h)return u==e&&h==r;var f=-1,m=!0,y=t&Qv?new qv:void 0;for(o.set(r,e),o.set(e,r);++f<l;){var v=r[f],p=e[f];if(n)var g=i?n(p,v,f,e,r,o):n(v,p,f,r,e,o);if(g!==void 0){if(g)continue;m=!1;break}if(y){if(!Yv(e,function(b,_){if(!Zv(y,_)&&(v===b||s(v,b,t,n,o)))return y.push(_)})){m=!1;break}}else if(!(v===p||s(v,p,t,n,o))){m=!1;break}}return o.delete(r),o.delete(e),m}var _i=e_;function t_(r){var e=-1,t=Array(r.size);return r.forEach(function(n,s){t[++e]=[s,n]}),t}var n_=t_;function r_(r){var e=-1,t=Array(r.size);return r.forEach(function(n){t[++e]=n}),t}var s_=r_,Ys=ut,Zs=Xo,o_=It,i_=_i,a_=n_,l_=s_,c_=1,u_=2,d_="[object Boolean]",h_="[object Date]",p_="[object Error]",f_="[object Map]",g_="[object Number]",m_="[object RegExp]",y_="[object Set]",b_="[object String]",v_="[object Symbol]",__="[object ArrayBuffer]",S_="[object DataView]",Js=Ys?Ys.prototype:void 0,Gn=Js?Js.valueOf:void 0;function O_(r,e,t,n,s,o,i){switch(t){case S_:if(r.byteLength!=e.byteLength||r.byteOffset!=e.byteOffset)return!1;r=r.buffer,e=e.buffer;case __:return!(r.byteLength!=e.byteLength||!o(new Zs(r),new Zs(e)));case d_:case h_:case g_:return o_(+r,+e);case p_:return r.name==e.name&&r.message==e.message;case m_:case b_:return r==e+"";case f_:var l=a_;case y_:var c=n&c_;if(l||(l=l_),r.size!=e.size&&!c)return!1;var u=i.get(r);if(u)return u==e;n|=u_,i.set(r,e);var h=i_(l(r),l(e),n,s,o,i);return i.delete(r),h;case v_:if(Gn)return Gn.call(r)==Gn.call(e)}return!1}var w_=O_,Qs=hi,C_=1,T_=Object.prototype,E_=T_.hasOwnProperty;function P_(r,e,t,n,s,o){var i=t&C_,l=Qs(r),c=l.length,u=Qs(e),h=u.length;if(c!=h&&!i)return!1;for(var f=c;f--;){var m=l[f];if(!(i?m in e:E_.call(e,m)))return!1}var y=o.get(r),v=o.get(e);if(y&&v)return y==e&&v==r;var p=!0;o.set(r,e),o.set(e,r);for(var g=i;++f<c;){m=l[f];var b=r[m],_=e[m];if(n)var O=i?n(_,b,m,e,r,o):n(b,_,m,r,e,o);if(!(O===void 0?b===_||s(b,_,t,n,o):O)){p=!1;break}g||(g=m=="constructor")}if(p&&!g){var T=r.constructor,C=e.constructor;T!=C&&"constructor"in r&&"constructor"in e&&!(typeof T=="function"&&T instanceof T&&typeof C=="function"&&C instanceof C)&&(p=!1)}return o.delete(r),o.delete(e),p}var $_=P_,Xn=Cr,I_=_i,x_=w_,A_=$_,eo=Mt,to=le,no=Nt,N_=On,j_=1,ro="[object Arguments]",so="[object Array]",Zt="[object Object]",M_=Object.prototype,oo=M_.hasOwnProperty;function D_(r,e,t,n,s,o){var i=to(r),l=to(e),c=i?so:eo(r),u=l?so:eo(e);c=c==ro?Zt:c,u=u==ro?Zt:u;var h=c==Zt,f=u==Zt,m=c==u;if(m&&no(r)){if(!no(e))return!1;i=!0,h=!1}if(m&&!h)return o||(o=new Xn),i||N_(r)?I_(r,e,t,n,s,o):x_(r,e,c,t,n,s,o);if(!(t&j_)){var y=h&&oo.call(r,"__wrapped__"),v=f&&oo.call(e,"__wrapped__");if(y||v){var p=y?r.value():r,g=v?e.value():e;return o||(o=new Xn),s(p,g,t,n,o)}}return m?(o||(o=new Xn),A_(r,e,t,n,s,o)):!1}var R_=D_,L_=R_,io=_e;function Si(r,e,t,n,s){return r===e?!0:r==null||e==null||!io(r)&&!io(e)?r!==r&&e!==e:L_(r,e,t,n,Si,s)}var k_=Si,F_=k_;function B_(r,e){return F_(r,e)}var K_=B_;const de=Y(K_);var W_=li,z_=Mt,H_=At,V_=le,U_=mt,G_=Nt,X_=Sn,q_=On,Y_="[object Map]",Z_="[object Set]",J_=Object.prototype,Q_=J_.hasOwnProperty;function eS(r){if(r==null)return!0;if(U_(r)&&(V_(r)||typeof r=="string"||typeof r.splice=="function"||G_(r)||q_(r)||H_(r)))return!r.length;var e=z_(r);if(e==Y_||e==Z_)return!r.size;if(X_(r))return!W_(r).length;for(var t in r)if(Q_.call(r,t))return!1;return!0}var tS=eS;const Se=Y(tS);var nS=Uo,rS=Cn;function sS(r,e){return r&&nS(r,e,rS)}var oS=sS,iS=mt;function aS(r,e){return function(t,n){if(t==null)return t;if(!iS(t))return r(t,n);for(var s=t.length,o=e?s:-1,i=Object(t);(e?o--:++o<s)&&n(i[o],o,i)!==!1;);return t}}var lS=aS,cS=oS,uS=lS,dS=uS(cS),hS=dS,pS=Nr;function fS(r){return typeof r=="function"?r:pS}var gS=fS,mS=ai,yS=hS,bS=gS,vS=le;function _S(r,e){var t=vS(r)?mS:yS;return t(r,bS(e))}var SS=_S,OS=SS;const wS=Y(OS);function ot(r,e){r&&console.warn(`Warning: ${e}`)}function Rt(r){return r==null}class Oi{constructor(){this._eventMap=new Map}on(e,t){return e&&typeof t=="function"&&(this._eventMap.has(e)||this._eventMap.set(e,[]),this._eventMap.get(e).push(t)),this}once(e,t){var n=this;if(e&&typeof t=="function"){const s=function(){t(...arguments),n.off(e,s)};this.on(e,s)}}off(e,t){if(e)if(typeof t=="function"){const n=this._eventMap.get(e);if(Array.isArray(n)&&n.length){let s=-1;for(;(s=n.findIndex(o=>o===t))>-1;)n.splice(s,1)}}else Rt(t)&&this._eventMap.delete(e);return this}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return this._eventMap.has(e)?([...this._eventMap.get(e)].forEach(i=>i(...n)),!0):!1}}function nt(r){try{return r instanceof HTMLElement}catch{return typeof r=="object"&&r.nodeType===1&&typeof r.style=="object"&&typeof r.ownerDocument=="object"}}function CS(r){if(r&&typeof r=="object")return typeof r.toJSON=="function"?r.toJSON():["left","top","right","bottom","width","height"].reduce((t,n)=>(t[n]=r[n],t),{})}function ge(r){r.stopPropagation(),r.preventDefault()}function TS(r){return r.length===1&&r.match(/\S/)}function Lt(r,e){for(let t=0;t<r.length;t++)r[t]===e?(r[t].tabIndex=0,r[t].focus()):r[t].tabIndex=-1}function ES(r){r.length>0&&Lt(r,r[0])}function PS(r){r.length>0&&Lt(r,r[r.length-1])}function $S(r,e){let t,n;r.length>0&&(e===r[0]?t=r[r.length-1]:(n=r.indexOf(e),t=r[n-1]),Lt(r,t))}function IS(r,e){let t,n;r.length>0&&(e===r[r.length-1]?t=r[0]:(n=r.indexOf(e),t=r[n+1]),Lt(r,t))}function xS(r,e,t,n){let s,o;return!r||!t||!n||n.length>1?-1:(n=n.toLowerCase(),s=r.indexOf(e)+1,s>=r.length&&(s=0),o=t.indexOf(n,s),o===-1&&(o=t.indexOf(n,0)),o>=0?o:-1)}function AS(r,e){if(!r)return null;for(;r.parentElement&&R(r.parentElement,"attributes.role.value","")!==e;)r=r.parentElement;return r.parentElement}function NS(r,e){for(let t=0;t<r.length;t++){const n=r[t].attributes["data-popupid"];if(n&&n.value===e)return r[t]}return null}const Jt={TOP:/top/i,RIGHT:/right/i,BOTTOM:/bottom/i,LEFT:/left/i},et={left:0,top:0,height:0,width:0,scrollLeft:0,scrollTop:0};let jS=class extends ye{constructor(e){var t;super(Object.assign({},e)),t=this,this.removePortal=()=>{this._adapter.removePortal()},this.setDisplayNone=(n,s)=>{this._adapter.setDisplayNone(n,s)},this.updateStateIfCursorOnTrigger=n=>{var s,o;if(!((s=n==null?void 0:n.matches)===null||s===void 0)&&s.call(n,":hover")){const i=this._adapter.getEventName(),l=this.getState("triggerEventSet");(o=l[i.mouseEnter])===null||o===void 0||o.call(l)}},this.onResize=()=>{this.getState("visible")&&this.calcPosition()},this.delayShow=()=>{const n=this.getProp("mouseEnterDelay");this.clearDelayTimer(),n>0?this._timer=setTimeout(()=>{this.show(),this.clearDelayTimer()},n):this.show()},this.show=()=>{if(this._adapter.getAnimatingState())return;const n=this.getProp("content"),s=this.getProp("trigger"),o=this.getProp("clickTriggerToHide"),{visible:i,displayNone:l}=this.getStates();if(l&&this.setDisplayNone(!1),!i){if(this.clearDelayTimer(),this._adapter.on("portalInserted",()=>{this.calcPosition()}),s==="hover"){const c=()=>{var u;const h=this._adapter.getTriggerDOM();s&&!(!((u=h==null?void 0:h.matches)===null||u===void 0)&&u.call(h,":hover"))&&this.hide(),this._adapter.off("portalInserted",c)};this._adapter.on("portalInserted",c)}this._adapter.on("positionUpdated",()=>{this._togglePortalVisible(!0)}),this._adapter.insertPortal(n,{left:-9999,top:-9999}),s==="custom"&&this._adapter.registerClickOutsideHandler(()=>{}),(s==="click"||o||s==="contextMenu")&&this._adapter.registerClickOutsideHandler(this.hide),this._bindScrollEvent(),this._bindResizeEvent()}},this.calcPosition=function(n,s,o){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;n=(Se(n)?t._adapter.getTriggerBounding():n)||Object.assign({},et),o=(Se(o)?t._adapter.getPopupContainerRect():o)||Object.assign({},et),s=(Se(s)?t._adapter.getWrapperBounding():s)||Object.assign({},et);let l=t.calcPosStyle({triggerRect:n,wrapperRect:s,containerRect:o}),c=t.getProp("position");if(t.getProp("autoAdjustOverflow")){const{position:u,isHeightOverFlow:h,isWidthOverFlow:f}=t.adjustPosIfNeed(c,l,n,s,o);(c!==u||h||f)&&(c=u,l=t.calcPosStyle({triggerRect:n,wrapperRect:s,containerRect:o,position:c,spacing:null,isOverFlow:[h,f]}))}return i&&t._mounted&&t._adapter.setPosition(Object.assign(Object.assign({},l),{position:c})),l},this.delayHide=()=>{const n=this.getProp("mouseLeaveDelay");this.clearDelayTimer(),n>0?this._timer=setTimeout(()=>{this.hide(),this.clearDelayTimer()},n):this.hide()},this.hide=()=>{this.clearDelayTimer(),this._togglePortalVisible(!1),this._adapter.off("portalInserted"),this._adapter.off("positionUpdated")},this.handleContainerKeydown=n=>{const{guardFocus:s,closeOnEsc:o}=this.getProps();switch(n&&n.key){case"Escape":o&&this._handleEscKeyDown(n);break;case"Tab":if(s){const i=this._adapter.getContainer(),l=this._adapter.getFocusableElements(i);l.length&&(n.shiftKey?this._handleContainerShiftTabKeyDown(l,n):this._handleContainerTabKeyDown(l,n))}break}},this._timer=null}init(){const{wrapperId:e}=this.getProps();this._mounted=!0,this._bindEvent(),this._shouldShow(),this._initContainerPosition(),e||this._adapter.setId()}destroy(){this._mounted=!1,this.unBindEvent()}_bindEvent(){const e=this.getProp("trigger"),{triggerEventSet:t,portalEventSet:n}=this._generateEvent(e);this._bindTriggerEvent(t),this._bindPortalEvent(n),this._bindResizeEvent()}unBindEvent(){this._adapter.unregisterClickOutsideHandler(),this.unBindResizeEvent(),this.unBindScrollEvent(),clearTimeout(this._timer)}_bindTriggerEvent(e){this._adapter.registerTriggerEvent(e)}_bindPortalEvent(e){this._adapter.registerPortalEvent(e)}_bindResizeEvent(){this._adapter.registerResizeHandler(this.onResize)}unBindResizeEvent(){this._adapter.unregisterResizeHandler(this.onResize)}_adjustPos(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"reverse",s=arguments.length>3?arguments[3]:void 0;switch(n){case"reverse":return this._reversePos(e,t);case"expand":return this._expandPos(e,s);case"reduce":return this._reducePos(e);default:return this._reversePos(e,t)}}_reversePos(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1){if(Jt.TOP.test(e))return e.replace("top","bottom").replace("Top","Bottom");if(Jt.BOTTOM.test(e))return e.replace("bottom","top").replace("Bottom","Top")}else{if(Jt.LEFT.test(e))return e.replace("left","right").replace("Left","Right");if(Jt.RIGHT.test(e))return e.replace("right","left").replace("Right","Left")}return e}_expandPos(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;return e.concat(t)}_reducePos(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";const t=["Top","Bottom","Left","Right"].find(n=>e.endsWith(n));return t?e.replace(t,""):e}clearDelayTimer(){this._timer&&(clearTimeout(this._timer),this._timer=null)}_generateEvent(e){const t=this._adapter.getEventName(),n={[t.keydown]:o=>{this._handleTriggerKeydown(o)}};let s={};switch(e){case"focus":n[t.focus]=()=>{this.delayShow()},n[t.blur]=()=>{this.delayHide()},s=n;break;case"click":n[t.click]=()=>{this.show()},s={};break;case"hover":n[t.mouseEnter]=()=>{this.setCache("isClickToHide",!1),this.delayShow()},n[t.mouseLeave]=()=>{this.delayHide()},n[t.focus]=()=>{const{disableFocusListener:o}=this.getProps();!o&&this.delayShow()},n[t.blur]=()=>{const{disableFocusListener:o}=this.getProps();!o&&this.delayHide()},s=Object.assign({},n),this.getProp("clickToHide")&&(s[t.click]=()=>{this.setCache("isClickToHide",!0),this.hide()},s[t.mouseEnter]=()=>{this.getCache("isClickToHide")||this.delayShow()});break;case"custom":break;case"contextMenu":n[t.contextMenu]=o=>{o.preventDefault(),this.show()};break}return{triggerEventSet:n,portalEventSet:s}}_shouldShow(){this.getProp("visible")&&this.show()}_togglePortalVisible(e){const t=this.getState("visible"),n=this.getState("isInsert");(t!==e||n!==e)&&this._adapter.togglePortalVisible(e,()=>{e&&this._adapter.setInitialFocus(),this._adapter.notifyVisibleChange(e)})}_roundPixel(e){return typeof e=="number"?Math.round(e):e}calcTransformOrigin(e,t,n,s){if(e&&t&&n!=null&&s!=null){if(this.getProp("transformFromCenter")){if(["topLeft","bottomLeft"].includes(e))return`${this._roundPixel(t.width/2)}px ${-s*100}%`;if(["topRight","bottomRight"].includes(e))return`calc(100% - ${this._roundPixel(t.width/2)}px) ${-s*100}%`;if(["leftTop","rightTop"].includes(e))return`${-n*100}% ${this._roundPixel(t.height/2)}px`;if(["leftBottom","rightBottom"].includes(e))return`${-n*100}% calc(100% - ${this._roundPixel(t.height/2)}px)`}return`${-n*100}% ${-s*100}%`}return null}calcPosStyle(e){var t;const{spacing:n,isOverFlow:s}=e,{innerWidth:o}=window,i=(Se(e.triggerRect)?e.triggerRect:this._adapter.getTriggerBounding())||Object.assign({},et),l=(Se(e.containerRect)?e.containerRect:this._adapter.getPopupContainerRect())||Object.assign({},et),c=(Se(e.wrapperRect)?e.wrapperRect:this._adapter.getWrapperBounding())||Object.assign({},et),u=e.position!=null?e.position:this.getProp("position"),h=n??this.getProp("spacing"),{arrowPointAtCenter:f,showArrow:m,arrowBounding:y}=this.getProps(),v=m&&f;let p=h,g=0;if(typeof h!="number"){const L=u.includes("top")||u.includes("bottom");p=L?h.y:h.x,g=L?h.x:h.y}const b=R(y,"width",24),_=R(y,"width",24),O=R(y,"offsetY",0),T=6,C=6;let S,w,P=0,x=0;const M=i.left+i.width/2,j=i.top+i.height/2,B=T+b/2,K=C+_/2,Z=c.height-l.height,q=c.width-l.width,se=Z>0?Z:0,J=q>0?q:0,V=s&&s[0],U=s&&s[1],G=M-l.left<l.right-M,we=j-l.top<l.bottom-j,Ce=c.width>o,X=Math.abs((c==null?void 0:c.width)-((t=this._adapter.getContainer())===null||t===void 0?void 0:t.clientWidth))>1;switch(X&&(p=p*c.width/this._adapter.getContainer().clientWidth),u){case"top":S=U?G?l.left+c.width/2:l.right-c.width/2+J:M+g,w=V?l.bottom+se:i.top-p,P=-.5,x=-1;break;case"topLeft":S=U?Ce?l.left:l.right-c.width:v?M-B+g:i.left+g,w=V?l.bottom+se:i.top-p,x=-1;break;case"topRight":S=U?l.right+J:v?M+B+g:i.right+g,w=V?l.bottom+se:i.top-p,x=-1,P=-1;break;case"left":S=U?l.right+J-p+B:i.left-p,w=V?we?l.top+c.height/2:l.bottom-c.height/2+se:j+g,P=-1,x=-.5;break;case"leftTop":S=U?l.right+J-p+B:i.left-p,w=V?l.top:v?j-K+g:i.top+g,P=-1;break;case"leftBottom":S=U?l.right+J-p+B:i.left-p,w=V?l.bottom+se:v?j+K+g:i.bottom+g,P=-1,x=-1;break;case"bottom":S=U?G?l.left+c.width/2:l.right-c.width/2+J:M+g,w=V?l.top+K-p:i.top+i.height+p,P=-.5;break;case"bottomLeft":S=U?Ce?l.left:l.right-c.width:v?M-B+g:i.left+g,w=V?l.top+K-p:i.top+i.height+p;break;case"bottomRight":S=U?l.right+J:v?M+B+g:i.right+g,w=V?l.top+K-p:i.top+i.height+p,P=-1;break;case"right":S=U?l.left-p+B:i.right+p,w=V?we?l.top+c.height/2:l.bottom-c.height/2+se:j+g,x=-.5;break;case"rightTop":S=U?l.left-p+B:i.right+p,w=V?l.top:v?j-K+g:i.top+g;break;case"rightBottom":S=U?l.left-p+B:i.right+p,w=V?l.bottom+se:v?j+K+g:i.bottom+g,x=-1;break;case"leftTopOver":S=i.left-p,w=i.top-p;break;case"rightTopOver":S=i.right+p,w=i.top-p,P=-1;break;case"leftBottomOver":S=i.left-p,w=i.bottom+p,x=-1;break;case"rightBottomOver":S=i.right+p,w=i.bottom+p,P=-1,x=-1;break}const oe=this.calcTransformOrigin(u,i,P,x),ue=this._adapter.containerIsBody();if(S=S-l.left,w=w-l.top,X&&(S/=c.width/this._adapter.getContainer().clientWidth),X&&(w/=c.height/this._adapter.getContainer().clientHeight),ue&&!this._adapter.containerIsRelativeOrAbsolute()){const L=this._adapter.getDocumentElementBounding();S+=l.left-L.left,w+=l.top-L.top}S=ue?S:S+l.scrollLeft,w=ue?w:w+l.scrollTop;const he=i.height;if(this.getProp("showArrow")&&!f&&he<=(_/2+O)*2){const L=he/2-(O+_/2);(u.includes("Top")||u.includes("Bottom"))&&!u.includes("Over")&&(w=u.includes("Top")?w+L:w-L)}const ne={left:this._roundPixel(S),top:this._roundPixel(w)};let pe="";return P!=null&&(pe+=`translateX(${P*100}%) `,Object.defineProperty(ne,"translateX",{enumerable:!1,value:P})),x!=null&&(pe+=`translateY(${x*100}%) `,Object.defineProperty(ne,"translateY",{enumerable:!1,value:x})),oe!=null&&(ne.transformOrigin=oe),pe&&(ne.transform=pe),ne}isLR(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e.includes("left")||e.includes("right")}isTB(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e.includes("top")||e.includes("bottom")}isReverse(e,t,n){return e<n&&t>n}isOverFlow(e,t,n){return e<n&&t<n}isHalfOverFlow(e,t,n){return e<n||t<n}isHalfAllEnough(e,t,n){return e>=n||t>=n}getReverse(e,t,n,s){return e&&s||n}adjustPosIfNeed(e,t,n,s,o){const{innerWidth:i,innerHeight:l}=window,{margin:c}=this.getProps(),u=typeof c=="number"?c:c.marginLeft,h=typeof c=="number"?c:c.marginTop,f=typeof c=="number"?c:c.marginRight,m=typeof c=="number"?c:c.marginBottom;let y=!1,v=!1;const p=this.getProp("spacing");let g=p,b=0;if(typeof p!="number"){const _=e.includes("top")||e.includes("bottom");g=_?p.y:p.x,b=_?p.x:p.y}if(s.width>0&&s.height>0){const _=n.left,O=n.right,T=n.top,C=n.bottom,S=i-_,w=l-T,P=i-O,x=l-C,M=s.width>n.width,j=s.height>n.height,B=T-h<s.height+g&&x-m>s.height+g,K=_-u<s.width+g&&P-f>s.width+g,Z=x-m<s.height+g&&T-h>s.height+g,q=P-f<s.width+g&&_-u>s.width+g;w-m<s.height+g&&C-h>s.height+g,C-h<s.height+g&&w-m>s.height+g;const se=w<s.height+b&&C>s.height+b,J=C<s.height+b&&w>s.height+b,V=S<s.width+b&&O>s.width+b,U=O<s.width+b&&S>s.width+b,G=w<s.height+g&&C>s.height+g,we=C<s.height+g&&w>s.height+g,Ce=S<s.width&&O>s.width,X=O<s.width&&S>s.width,oe=T-o.top,ue=_-o.left,he=oe+n.height,ne=ue+n.width,pe=o.bottom-C,L=o.right-O,ke=pe+n.height,_t=L+n.width,ra=this.isReverse(oe-h,pe-m,s.height+g),sa=this.isReverse(ue-u,L-f,s.width+g),oa=this.isReverse(pe-m,oe-h,s.height+g),ia=this.isReverse(L-f,ue-u,s.width+g);this.isReverse(ke-m,he-h,s.height+g),this.isReverse(he-h,ke-m,s.height+g);const aa=this.isReverse(ke,he,s.height+b),la=this.isReverse(he,ke,s.height+b),ca=this.isReverse(_t,ne,s.width+b),ua=this.isReverse(ne,_t,s.width+b),Ae=n.height/2,Ne=n.width/2,jn=this.isOverFlow(T-h,x-m,s.height+g),Mn=this.isOverFlow(_-u,P-f,s.width+g),Dn=this.isOverFlow(C-h,w-m,s.height+g),Rn=this.isOverFlow(O-u,S-f,s.width+g),ss=this.isHalfOverFlow(C-Ae,w-Ae,(s.height+b)/2),os=this.isHalfOverFlow(O-Ne,S-Ne,(s.width+b)/2),Ht=this.isHalfAllEnough(C-Ae,w-Ae,(s.height+b)/2),Vt=this.isHalfAllEnough(O-Ne,S-Ne,(s.width+b)/2),Ln=this.isOverFlow(oe-h,pe-m,s.height+g),kn=this.isOverFlow(ue-u,L-f,s.width+g),Fn=this.isOverFlow(he-h,ke-m,s.height+g),Bn=this.isOverFlow(ne-u,_t-f,s.width+g),is=this.isHalfOverFlow(he-Ae,ke-Ae,(s.height+b)/2),as=this.isHalfOverFlow(ne-Ne,_t-Ne,(s.width+b)/2),Ut=this.isHalfAllEnough(he-Ae,ke-Ae,(s.height+b)/2),Gt=this.isHalfAllEnough(ne-Ne,_t-Ne,(s.width+b)/2),Kn=this.getReverse(jn,Ln,B,ra),Wn=this.getReverse(Mn,kn,K,sa),zn=this.getReverse(jn,Ln,Z,oa),Hn=this.getReverse(Mn,kn,q,ia),Je=this.getReverse(Dn,Fn,se,aa),Xt=this.getReverse(Dn,Fn,J,la),Qe=this.getReverse(Rn,Bn,V,ca),qt=this.getReverse(Rn,Bn,U,ua),ls=ss&&is,cs=os&&as;switch(e){case"top":Kn&&(e=this._adjustPos(e,!0)),cs&&(Qe||qt)&&(e=this._adjustPos(e,!0,"expand",Qe?"Right":"Left"));break;case"topLeft":Kn&&(e=this._adjustPos(e,!0)),Qe&&M&&(e=this._adjustPos(e)),v&&(Vt||Gt)&&(e=this._adjustPos(e,!0,"reduce"));break;case"topRight":Kn&&(e=this._adjustPos(e,!0)),qt&&M&&(e=this._adjustPos(e)),v&&(Vt||Gt)&&(e=this._adjustPos(e,!0,"reduce"));break;case"left":Wn&&(e=this._adjustPos(e)),ls&&(Je||Xt)&&(e=this._adjustPos(e,!1,"expand",Je?"Bottom":"Top"));break;case"leftTop":Wn&&(e=this._adjustPos(e)),Je&&j&&(e=this._adjustPos(e,!0)),y&&(Ht||Ut)&&(e=this._adjustPos(e,!1,"reduce"));break;case"leftBottom":Wn&&(e=this._adjustPos(e)),Xt&&j&&(e=this._adjustPos(e,!0)),y&&(Ht||Ut)&&(e=this._adjustPos(e,!1,"reduce"));break;case"bottom":zn&&(e=this._adjustPos(e,!0)),cs&&(Qe||qt)&&(e=this._adjustPos(e,!0,"expand",Qe?"Right":"Left"));break;case"bottomLeft":zn&&(e=this._adjustPos(e,!0)),Qe&&M&&(e=this._adjustPos(e)),v&&(Vt||Gt)&&(e=this._adjustPos(e,!0,"reduce"));break;case"bottomRight":zn&&(e=this._adjustPos(e,!0)),qt&&M&&(e=this._adjustPos(e)),v&&(Vt||Gt)&&(e=this._adjustPos(e,!0,"reduce"));break;case"right":Hn&&(e=this._adjustPos(e)),ls&&(Je||Xt)&&(e=this._adjustPos(e,!1,"expand",Je?"Bottom":"Top"));break;case"rightTop":Hn&&(e=this._adjustPos(e)),Je&&j&&(e=this._adjustPos(e,!0)),y&&(Ht||Ut)&&(e=this._adjustPos(e,!1,"reduce"));break;case"rightBottom":Hn&&(e=this._adjustPos(e)),Xt&&j&&(e=this._adjustPos(e,!0)),y&&(Ht||Ut)&&(e=this._adjustPos(e,!1,"reduce"));break;case"leftTopOver":G&&(e=this._adjustPos(e,!0)),Ce&&(e=this._adjustPos(e));break;case"leftBottomOver":we&&(e=this._adjustPos(e,!0)),Ce&&(e=this._adjustPos(e));break;case"rightTopOver":G&&(e=this._adjustPos(e,!0)),X&&(e=this._adjustPos(e));break;case"rightBottomOver":we&&(e=this._adjustPos(e,!0)),X&&(e=this._adjustPos(e));break}this.isTB(e)&&(y=jn&&Ln,e==="top"||e==="bottom"?v=os&&as||O<0||P<0:v=Rn&&Bn||O<0||P<0),this.isLR(e)&&(v=Mn&&kn,e==="left"||e==="right"?y=ss&&is||T<0||w<0:y=Dn&&Fn||T<0||w<0)}return{position:e,isHeightOverFlow:y,isWidthOverFlow:v}}_bindScrollEvent(){this._adapter.registerScrollHandler(()=>this.calcPosition())}unBindScrollEvent(){this._adapter.unregisterScrollHandler()}_initContainerPosition(){this._adapter.updateContainerPosition()}_handleTriggerKeydown(e){const{closeOnEsc:t,disableArrowKeyDown:n}=this.getProps(),s=this._adapter.getContainer(),o=this._adapter.getFocusableElements(s),i=o.length;switch(e&&e.key){case"Escape":ge(e),t&&this._handleEscKeyDown(e);break;case"ArrowUp":!n&&i&&this._handleTriggerArrowUpKeydown(o,e);break;case"ArrowDown":!n&&i&&this._handleTriggerArrowDownKeydown(o,e);break}}focusTrigger(){const{trigger:e,returnFocusOnClose:t,preventScroll:n}=this.getProps();if(t&&e!=="custom"){const s=this._adapter.getTriggerNode();s&&"focus"in s&&s.focus({preventScroll:n})}}_handleEscKeyDown(e){const{trigger:t}=this.getProps();t!=="custom"&&(this.focusTrigger(),this.hide()),this._adapter.notifyEscKeydown(e)}_handleContainerTabKeyDown(e,t){const{preventScroll:n}=this.getProps(),s=this._adapter.getActiveElement();e[e.length-1]===s&&(e[0].focus({preventScroll:n}),t.preventDefault())}_handleContainerShiftTabKeyDown(e,t){const{preventScroll:n}=this.getProps(),s=this._adapter.getActiveElement();e[0]===s&&(e[e.length-1].focus({preventScroll:n}),t.preventDefault())}_handleTriggerArrowDownKeydown(e,t){const{preventScroll:n}=this.getProps();e[0].focus({preventScroll:n}),t.preventDefault()}_handleTriggerArrowUpKeydown(e,t){const{preventScroll:n}=this.getProps();e[e.length-1].focus({preventScroll:n}),t.preventDefault()}};const MS={PREFIX:`${D}-tooltip`},He={POSITION_SET:["top","topLeft","topRight","left","leftTop","leftBottom","right","rightTop","rightBottom","bottom","bottomLeft","bottomRight","leftTopOver","rightTopOver","leftBottomOver","rightBottomOver"],TRIGGER_SET:["hover","focus","click","custom","contextMenu"],STATUS_DISABLED:"disabled",STATUS_LOADING:"loading"},je={ARROW_BOUNDING:{offsetX:0,offsetY:2,width:24,height:7},DEFAULT_Z_INDEX:1060,MOUSE_ENTER_DELAY:50,MOUSE_LEAVE_DELAY:50,SPACING:8,MARGIN:0};function DS(r){return`${r}-${new Date().getTime()}-${Math.random()}`}function RS(){var r,e;try{return(e=(r=crypto==null?void 0:crypto.randomUUID)===null||r===void 0?void 0:r.call(crypto))!==null&&e!==void 0?e:String(9987e3+-1e11).replace(/[018]/g,t=>(Number(t)^crypto.getRandomValues(new Uint8Array(1))[0]&15>>Number(t)/4).toString(16))}catch{return DS("semi")}}function ln(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{prefix:e="",length:t=7}=r,n="0123456789abcdefghijklmnopqrstuvwxyz",s=n.length;let o="";for(let i=0;i<t;i++){const l=Math.floor(Math.random()*s);o+=n.charAt(l)}return e?`${e}-${o}`:o}var LS=Ar,kS=ft,FS=wn,ao=me,BS=_n;function KS(r,e,t,n){if(!ao(r))return r;e=kS(e,r);for(var s=-1,o=e.length,i=o-1,l=r;l!=null&&++s<o;){var c=BS(e[s]),u=t;if(c==="__proto__"||c==="constructor"||c==="prototype")return r;if(s!=i){var h=l[c];u=n?n(h,c,l):void 0,u===void 0&&(u=ao(h)?h:FS(e[s+1])?[]:{})}LS(l,c,u),l=l[c]}return r}var wi=KS,WS=wi;function zS(r,e,t){return r==null?r:WS(r,e,t)}var HS=zS;const VS=Y(HS);class US{constructor(){this.config={}}}const qn=new US;var lo=function(r,e,t,n){function s(o){return o instanceof t?o:new t(function(i){i(o)})}return new(t||(t=Promise))(function(o,i){function l(h){try{u(n.next(h))}catch(f){i(f)}}function c(h){try{u(n.throw(h))}catch(f){i(f)}}function u(h){h.done?o(h.value):s(h.value).then(l,c)}u((n=n.apply(r,e||[])).next())})};function Qt(r,e){r&&typeof r.stopPropagation=="function"&&r.stopPropagation(),r.nativeEvent&&typeof r.nativeEvent.stopImmediatePropagation=="function"&&r.nativeEvent.stopImmediatePropagation()}const Me=r=>d.isValidElement(r)&&R(r.type,"elementType")==="Icon";function Ci(){return document?document.activeElement:null}function Ti(r){if(!nt(r))return[];const t=["input:not([disabled]):not([tabindex='-1'])","textarea:not([disabled]):not([tabindex='-1'])","button:not([disabled]):not([tabindex='-1'])","a[href]:not([tabindex='-1'])","select:not([disabled]):not([tabindex='-1'])","area[href]:not([tabindex='-1'])","iframe:not([tabindex='-1'])","object:not([tabindex='-1'])","*[tabindex]:not([tabindex='-1'])","*[contenteditable]:not([tabindex='-1'])"].join(",");return Array.from(r.querySelectorAll(t))}function Dr(r,e){return lo(this,void 0,void 0,function*(){if(e===0){yield r();return}else{yield new Promise(t=>{setTimeout(()=>lo(this,void 0,void 0,function*(){yield Dr(r,e-1),t()}),0)});return}})}function Le(r){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const t=()=>{var n,s;return((s=(n=qn==null?void 0:qn.config)===null||n===void 0?void 0:n.overrideDefaultProps)===null||s===void 0?void 0:s[r])||{}};return new Proxy(Object.assign({},e),{get(n,s,o){const i=t();return s in i?i[s]:Reflect.get(n,s,o)},set(n,s,o,i){return Reflect.set(n,s,o,i)},ownKeys(){const n=t();return Array.from(new Set([...Reflect.ownKeys(e),...Object.keys(n)]))},getOwnPropertyDescriptor(n,s){const o=t();return s in o?Reflect.getOwnPropertyDescriptor(o,s):Reflect.getOwnPropertyDescriptor(n,s)}})}const kt=d.createContext({}),GS=()=>document.body;class Tn extends $.PureComponent{constructor(e,t){var n;super(e),n=this,this.initContainer=function(s){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;var i,l;try{let c;if(!n.el||!(!((i=n.state)===null||i===void 0)&&i.container)||!Array.from(n.state.container.childNodes).includes(n.el)){n.el=document.createElement("div");const h=(n.props.getPopupContainer||s.getPopupContainer||GS)();return h.appendChild(n.el),n.addStyle(n.props.style),n.addClass(n.props.prefixCls,s,n.props.className),c=h,c}}catch(c){if(!o)throw c}return(l=n.state)===null||l===void 0?void 0:l.container},this.addStyle=function(){let s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(n.el)for(const o of Object.keys(s))n.el.style[o]=s[o]},this.addClass=function(s){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:n.context;const{direction:i}=o;for(var l=arguments.length,c=new Array(l>2?l-2:0),u=2;u<l;u++)c[u-2]=arguments[u];const h=E(s,...c,{[`${s}-rtl`]:i==="rtl"});n.el&&(n.el.className=h)},this.state={container:this.initContainer(t,!0)}}componentDidMount(){const e=this.initContainer(this.context);e!==this.state.container&&this.setState({container:e})}componentDidUpdate(e){const{didUpdate:t}=this.props;t&&t(e)}componentWillUnmount(){const{container:e}=this.state;e&&e.removeChild(this.el)}render(){const{state:e,props:t}=this;return e.container?yr.createPortal(t.children,this.el):null}}Tn.contextType=kt;Tn.defaultProps={prefixCls:`${D}-portal`};Tn.propTypes={children:a.node,prefixCls:a.string,getPopupContainer:a.func,className:a.string,didUpdate:a.func};var XS=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const qS=r=>{const{className:e,style:t}=r,n=XS(r,["className","style"]);return d.createElement("svg",Object.assign({"aria-hidden":!0,className:e,style:t},n,{width:"24",height:"7",viewBox:"0 0 24 7",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"}),d.createElement("path",{d:"M24 0V1C20 1 18.5 2 16.5 4C14.5 6 14 7 12 7C10 7 9.5 6 7.5 4C5.5 2 4 1 0 1V0H24Z"}))};var YS=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const ZS=r=>{const{className:e,style:t}=r,n=YS(r,["className","style"]);return d.createElement("svg",Object.assign({"aria-hidden":!0,className:e,style:t},n,{width:"7",height:"24",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor"}),d.createElement("path",{d:"M0 0L1 0C1 4, 2 5.5, 4 7.5S7,10 7,12S6 14.5, 4 16.5S1,20 1,24L0 24L0 0z"}))},JS=a.shape({offsetX:a.number,offsetY:a.number,width:a.number,height:a.number});class Rr extends d.Component{constructor(e){super(e),this.handleAnimationStart=()=>{var t,n;(n=(t=this.props).onAnimationStart)===null||n===void 0||n.call(t)},this.handleAnimationEnd=()=>{this.setState({currentClassName:this.props.endClassName,extraStyle:{animationFillMode:this.props.fillMode},isAnimating:!1},()=>{var t,n;(n=(t=this.props).onAnimationEnd)===null||n===void 0||n.call(t,!1)})},this.state={currentClassName:this.props.startClassName,extraStyle:{animationFillMode:this.props.fillMode},isAnimating:!0}}componentDidMount(){var e,t,n,s;(t=(e=this.props).onAnimationStart)===null||t===void 0||t.call(e),this.props.motion||((s=(n=this.props).onAnimationEnd)===null||s===void 0||s.call(n,!1),this.setState({isAnimating:!1}))}componentDidUpdate(e,t,n){const s=Object.keys(this.props).filter(o=>!de(this.props[o],e[o]));s.includes("animationState"),(s.includes("startClassName")||s.includes("replayKey")||s.includes("motion"))&&this.setState({currentClassName:this.props.startClassName,extraStyle:{animationFillMode:this.props.fillMode},isAnimating:!0},()=>{var o,i,l,c;(i=(o=this.props).onAnimationStart)===null||i===void 0||i.call(o),this.props.motion||((c=(l=this.props).onAnimationEnd)===null||c===void 0||c.call(l,this.state.isAnimating),this.setState({isAnimating:!1}))})}render(){var e;return this.props.motion?this.props.children({animationClassName:(e=this.state.currentClassName)!==null&&e!==void 0?e:"",animationStyle:this.state.extraStyle,animationEventsNeedBind:{onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},isAnimating:this.state.isAnimating}):this.props.children({animationClassName:"",animationStyle:{},animationEventsNeedBind:{},isAnimating:this.state.isAnimating})}}Rr.defaultProps={motion:!0,replayKey:""};var co=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const tn=MS.PREFIX,QS=He.POSITION_SET,eO=He.TRIGGER_SET,tO=["flex","block","table","flow-root","grid"],nO=()=>document.body;class Ie extends ce{constructor(e){super(e),this.isAnimating=!1,this.setContainerEl=t=>this.containerEl={current:t},this.isSpecial=t=>{if(nt(t))return!!t.disabled;if($.isValidElement(t)){if(R(t,"props.disabled"))return He.STATUS_DISABLED;const s=R(t,"props.loading"),o=!Se(t)&&!Se(t.type)&&(R(t,"type.elementType")==="Button"||R(t,"type.elementType")==="IconButton");if(s&&o)return He.STATUS_LOADING}return!1},this.didLeave=()=>{this.props.keepDOM?this.foundation.setDisplayNone(!0):this.foundation.removePortal(),this.foundation.unBindEvent()},this.renderIcon=()=>{const{placement:t}=this.state,{showArrow:n,prefixCls:s,style:o}=this.props;let i=null;const l=E([`${s}-icon-arrow`]),c=R(o,"backgroundColor"),u=t!=null&&t.includes("left")||t!=null&&t.includes("right")?d.createElement(ZS,null):d.createElement(qS,null);return n&&($.isValidElement(n)?i=n:i=d.cloneElement(u,{className:l,style:{color:c,fill:"currentColor"}})),i},this.handlePortalInnerClick=t=>{this.props.clickToHide&&this.foundation.hide(),this.props.stopPropagation&&Qt(t)},this.handlePortalMouseDown=t=>{this.props.stopPropagation&&Qt(t)},this.handlePortalFocus=t=>{this.props.stopPropagation&&Qt(t)},this.handlePortalBlur=t=>{this.props.stopPropagation&&Qt(t)},this.handlePortalInnerKeyDown=t=>{this.foundation.handleContainerKeydown(t)},this.renderContentNode=t=>{const n={initialFocusRef:this.initialFocusRef};return fe(t)?t(n):t},this.renderPortal=()=>{const{containerStyle:t={},visible:n,portalEventSet:s,placement:o,displayNone:i,transitionState:l,id:c,isPositionUpdated:u}=this.state,{prefixCls:h,content:f,showArrow:m,style:y,motion:v,role:p,zIndex:g}=this.props,b=this.renderContentNode(f),{className:_}=this.props,O=this.context.direction,T=E(_,{[`${h}-wrapper`]:!0,[`${h}-wrapper-show`]:n,[`${h}-with-arrow`]:!!m,[`${h}-rtl`]:O==="rtl"}),C=this.renderIcon(),S=Dt(t,v?["transformOrigin"]:void 0),w=R(t,"transformOrigin"),P=R(y,"opacity",null),x=P||1,M=d.createElement(Rr,{fillMode:"forwards",animationState:l,motion:v&&u,startClassName:l==="enter"?`${tn}-animation-show`:`${tn}-animation-hide`,onAnimationStart:()=>this.isAnimating=!0,onAnimationEnd:()=>{var j,B;l==="leave"&&(this.didLeave(),(B=(j=this.props).afterClose)===null||B===void 0||B.call(j)),this.isAnimating=!1}},j=>{let{animationStyle:B,animationClassName:K,animationEventsNeedBind:Z}=j;return d.createElement("div",Object.assign({className:E(T,K),style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},B),i?{display:"none"}:{}),{transformOrigin:w}),y),P?{opacity:u?x:"0"}:{})},s,Z,{role:p,"x-placement":o,id:c}),d.createElement("div",{className:`${tn}-content`},b),C)});return d.createElement(Tn,{getPopupContainer:this.props.getPopupContainer,style:{zIndex:g}},d.createElement("div",{tabIndex:-1,className:`${D}-portal-inner`,style:S,ref:this.setContainerEl,onClick:this.handlePortalInnerClick,onFocus:this.handlePortalFocus,onBlur:this.handlePortalBlur,onMouseDown:this.handlePortalMouseDown,onKeyDown:this.handlePortalInnerKeyDown},M))},this.wrapSpan=t=>{const{wrapperClassName:n}=this.props,s=R(t,"props.style.display"),o=R(t,"props.block"),i=typeof t=="string",l={};return i||(l.display="inline-block"),(o||tO.includes(s))&&(l.width="100%"),d.createElement("span",{className:n,style:l},t)},this.mergeEvents=(t,n)=>{const s={};return wS(n,(o,i)=>{typeof o=="function"&&(s[i]=function(){o(...arguments),t&&typeof t[i]=="function"&&t[i](...arguments)})}),s},this.getPopupId=()=>this.state.id,this.state={visible:!1,transitionState:"",triggerEventSet:{},portalEventSet:{},containerStyle:{},isInsert:!1,placement:e.position||"top",transitionStyle:{},isPositionUpdated:!1,id:e.wrapperId,displayNone:!1},this.foundation=new jS(this.adapter),this.eventManager=new Oi,this.triggerEl=d.createRef(),this.containerEl=d.createRef(),this.initialFocusRef=d.createRef(),this.clickOutsideHandler=null,this.resizeHandler=null,this.isWrapped=!1,this.containerPosition=void 0}get adapter(){var e=this;return Object.assign(Object.assign({},super.adapter),{on:function(){return e.eventManager.on(...arguments)},off:function(){return e.eventManager.off(...arguments)},getAnimatingState:()=>this.isAnimating,insertPortal:(t,n)=>{var{position:s}=n,o=co(n,["position"]);this.setState({isInsert:!0,transitionState:"enter",containerStyle:Object.assign(Object.assign({},this.state.containerStyle),o)},()=>{setTimeout(()=>{this.setState(i=>(i.transitionState==="enter"&&this.eventManager.emit("portalInserted"),{}))},0)})},removePortal:()=>{this.setState({isInsert:!1,isPositionUpdated:!1})},getEventName:()=>({mouseEnter:"onMouseEnter",mouseLeave:"onMouseLeave",mouseOut:"onMouseOut",mouseOver:"onMouseOver",click:"onClick",focus:"onFocus",blur:"onBlur",keydown:"onKeyDown",contextMenu:"onContextMenu"}),registerTriggerEvent:t=>{this.setState({triggerEventSet:t})},registerPortalEvent:t=>{this.setState({portalEventSet:t})},getTriggerBounding:()=>{const t=this.adapter.getTriggerNode();return this.triggerEl.current=t,t&&t.getBoundingClientRect()},getPopupContainerRect:()=>{const t=this.getPopupContainer();let n=null;if(t&&nt(t)){const s=CS(t.getBoundingClientRect());n=Object.assign(Object.assign({},s),{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop})}return n},containerIsBody:()=>this.getPopupContainer()===document.body,containerIsRelative:()=>{const t=this.getPopupContainer();return window.getComputedStyle(t).getPropertyValue("position")==="relative"},containerIsRelativeOrAbsolute:()=>["relative","absolute"].includes(this.containerPosition),getWrapperBounding:()=>{const t=this.containerEl&&this.containerEl.current;return t&&t.getBoundingClientRect()},getDocumentElementBounding:()=>document.documentElement.getBoundingClientRect(),setPosition:t=>{var{position:n}=t,s=co(t,["position"]);this.setState({containerStyle:Object.assign(Object.assign({},this.state.containerStyle),s),placement:n,isPositionUpdated:!0},()=>{this.eventManager.emit("positionUpdated")})},setDisplayNone:(t,n)=>{this.setState({displayNone:t},n)},updatePlacementAttr:t=>{this.setState({placement:t})},togglePortalVisible:(t,n)=>{const s={};s.transitionState=t?"enter":"leave",s.visible=t,this.mounted&&this.setState(s,()=>{n()})},registerClickOutsideHandler:t=>{this.clickOutsideHandler&&this.adapter.unregisterClickOutsideHandler(),this.clickOutsideHandler=n=>{if(!this.mounted)return!1;let s=this.triggerEl&&this.triggerEl.current,o=this.containerEl&&this.containerEl.current;s=Tt.findDOMNode(s),o=Tt.findDOMNode(o);const i=n.target,l=n.composedPath&&n.composedPath()||[i],c=this.props.clickTriggerToHide?s&&s.contains(i)||l.includes(s):!1;(s&&!s.contains(i)&&o&&!o.contains(i)&&!(l.includes(o)||l.includes(s))||c)&&(this.props.onClickOutSide(n),t())},window.addEventListener("mousedown",this.clickOutsideHandler)},unregisterClickOutsideHandler:()=>{this.clickOutsideHandler&&(window.removeEventListener("mousedown",this.clickOutsideHandler),this.clickOutsideHandler=null)},registerResizeHandler:t=>{this.resizeHandler&&this.adapter.unregisterResizeHandler(),this.resizeHandler=Os(n=>{if(!this.mounted)return!1;t(n)},10),window.addEventListener("resize",this.resizeHandler,!1)},unregisterResizeHandler:()=>{this.resizeHandler&&(window.removeEventListener("resize",this.resizeHandler,!1),this.resizeHandler=null)},notifyVisibleChange:t=>{this.props.onVisibleChange(t)},registerScrollHandler:t=>{this.scrollHandler&&this.adapter.unregisterScrollHandler(),this.scrollHandler=Os(n=>{if(!this.mounted)return!1;const s=this.adapter.getTriggerNode();if(n.target.contains(s)){const i={x:n.target.scrollLeft,y:n.target.scrollTop};t(i)}},10),window.addEventListener("scroll",this.scrollHandler,!0)},unregisterScrollHandler:()=>{this.scrollHandler&&(window.removeEventListener("scroll",this.scrollHandler,!0),this.scrollHandler=null)},canMotion:()=>!!this.props.motion,updateContainerPosition:()=>{const t=this.getPopupContainer();if(t&&nt(t)){const s=window.getComputedStyle(t).getPropertyValue("position");this.containerPosition=s}},getContainerPosition:()=>this.containerPosition,getContainer:()=>this.containerEl&&this.containerEl.current,getTriggerNode:()=>{let t=this.triggerEl.current;return nt(this.triggerEl.current)||(t=Tt.findDOMNode(this.triggerEl.current)),t},getFocusableElements:t=>Ti(t),getActiveElement:()=>Ci(),setInitialFocus:()=>{const{preventScroll:t}=this.props,n=R(this,"initialFocusRef.current");n&&"focus"in n&&n.focus({preventScroll:t})},notifyEscKeydown:t=>{this.props.onEscKeyDown(t)},setId:()=>{this.setState({id:ln()})},getTriggerDOM:()=>this.triggerEl.current?Tt.findDOMNode(this.triggerEl.current):null})}componentDidMount(){this.mounted=!0,this.getPopupContainer=this.props.getPopupContainer||this.context.getPopupContainer||nO,this.foundation.init(),Dr(()=>{let e=this.triggerEl.current;e&&(e instanceof HTMLElement||(e=yr.findDOMNode(e))),this.foundation.updateStateIfCursorOnTrigger(e)},1)}componentWillUnmount(){this.mounted=!1,this.foundation.destroy()}focusTrigger(){this.foundation.focusTrigger()}rePosition(){return this.foundation.calcPosition()}componentDidUpdate(e,t){ot(this.props.mouseLeaveDelay<this.props.mouseEnterDelay,"[Semi Tooltip] 'mouseLeaveDelay' cannot be less than 'mouseEnterDelay', which may cause the dropdown layer to not be hidden."),e.visible!==this.props.visible&&(["hover","focus"].includes(this.props.trigger)?this.props.visible?this.foundation.delayShow():this.foundation.delayHide():this.props.visible?this.foundation.show():this.foundation.hide()),de(e.rePosKey,this.props.rePosKey)||this.rePosition()}render(){const{isInsert:e,triggerEventSet:t,visible:n,id:s}=this.state,{wrapWhenSpecial:o,role:i,trigger:l}=this.props;let{children:c}=this.props;const u=Object.assign({},R(c,"props.style")),h={};if(o){const y=this.isSpecial(c);y?(u.pointerEvents="none",y===He.STATUS_DISABLED&&(h.cursor="not-allowed"),c=$.cloneElement(c,{style:u}),l!=="custom"&&(c=this.wrapSpan(c)),this.isWrapped=!0):$.isValidElement(c)||(c=this.wrapSpan(c),this.isWrapped=!0)}let f={};i==="dialog"?(f["aria-expanded"]=n?"true":"false",f["aria-haspopup"]="dialog",f["aria-controls"]=s):f["aria-describedby"]=s;const m=d.cloneElement(c,Object.assign(Object.assign(Object.assign(Object.assign({},f),c.props),this.mergeEvents(c.props,t)),{style:Object.assign(Object.assign({},R(c,"props.style")),h),className:E(R(c,"props.className")),ref:y=>{this.triggerEl.current=y;const{ref:v}=c;typeof v=="function"?v(y):v&&typeof v=="object"&&(v.current=y)},tabIndex:c.props.tabIndex||0,"data-popupid":s}));return d.createElement(d.Fragment,null,e?this.renderPortal():null,m)}}Ie.contextType=kt;Ie.propTypes={children:a.node,motion:a.bool,autoAdjustOverflow:a.bool,position:a.oneOf(QS),getPopupContainer:a.func,mouseEnterDelay:a.number,mouseLeaveDelay:a.number,trigger:a.oneOf(eO).isRequired,className:a.string,wrapperClassName:a.string,clickToHide:a.bool,clickTriggerToHide:a.bool,visible:a.bool,style:a.object,content:a.oneOfType([a.node,a.func]),prefixCls:a.string,onVisibleChange:a.func,onClickOutSide:a.func,spacing:a.oneOfType([a.number,a.object]),margin:a.oneOfType([a.number,a.object]),showArrow:a.oneOfType([a.bool,a.node]),zIndex:a.number,rePosKey:a.oneOfType([a.string,a.number]),arrowBounding:JS,transformFromCenter:a.bool,arrowPointAtCenter:a.bool,stopPropagation:a.bool,role:a.string,wrapWhenSpecial:a.bool,guardFocus:a.bool,returnFocusOnClose:a.bool,preventScroll:a.bool,keepDOM:a.bool};Ie.__SemiComponentName__="Tooltip";Ie.defaultProps=Le(Ie.__SemiComponentName__,{arrowBounding:je.ARROW_BOUNDING,autoAdjustOverflow:!0,arrowPointAtCenter:!0,trigger:"hover",transformFromCenter:!0,position:"top",prefixCls:tn,role:"tooltip",mouseEnterDelay:je.MOUSE_ENTER_DELAY,mouseLeaveDelay:je.MOUSE_LEAVE_DELAY,motion:!0,onVisibleChange:A,onClickOutSide:A,spacing:je.SPACING,margin:je.MARGIN,showArrow:!0,wrapWhenSpecial:!0,zIndex:je.DEFAULT_Z_INDEX,closeOnEsc:!1,guardFocus:!1,returnFocusOnClose:!1,onEscKeyDown:A,disableFocusListener:!1,disableArrowKeyDown:!1,keepDOM:!1});var Lr={exports:{}};const Ei=(r,{target:e=document.body}={})=>{const t=document.createElement("textarea"),n=document.activeElement;t.value=r,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const s=document.getSelection();let o=!1;s.rangeCount>0&&(o=s.getRangeAt(0)),e.append(t),t.select(),t.selectionStart=0,t.selectionEnd=r.length;let i=!1;try{i=document.execCommand("copy")}catch{}return t.remove(),o&&(s.removeAllRanges(),s.addRange(o)),n&&n.focus(),i};Lr.exports=Ei;Lr.exports.default=Ei;var rO=Lr.exports;const sO=Y(rO);function Te(){}const Pi=d.createContext(null);function Ee(r){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.width?String(e.width):r.defaultWidth,n=r.formats[t]||r.formats[r.defaultWidth];return n}}function ie(r){return function(e,t){var n=t!=null&&t.context?String(t.context):"standalone",s;if(n==="formatting"&&r.formattingValues){var o=r.defaultFormattingWidth||r.defaultWidth,i=t!=null&&t.width?String(t.width):o;s=r.formattingValues[i]||r.formattingValues[o]}else{var l=r.defaultWidth,c=t!=null&&t.width?String(t.width):r.defaultWidth;s=r.values[c]||r.values[l]}var u=r.argumentCallback?r.argumentCallback(e):e;return s[u]}}function ae(r){return function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.width,s=n&&r.matchPatterns[n]||r.matchPatterns[r.defaultMatchWidth],o=e.match(s);if(!o)return null;var i=o[0],l=n&&r.parsePatterns[n]||r.parsePatterns[r.defaultParseWidth],c=Array.isArray(l)?iO(l,function(f){return f.test(i)}):oO(l,function(f){return f.test(i)}),u;u=r.valueCallback?r.valueCallback(c):c,u=t.valueCallback?t.valueCallback(u):u;var h=e.slice(i.length);return{value:u,rest:h}}}function oO(r,e){for(var t in r)if(r.hasOwnProperty(t)&&e(r[t]))return t}function iO(r,e){for(var t=0;t<r.length;t++)if(e(r[t]))return t}function kr(r){return function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=e.match(r.matchPattern);if(!n)return null;var s=n[0],o=e.match(r.parsePattern);if(!o)return null;var i=r.valueCallback?r.valueCallback(o[0]):o[0];i=t.valueCallback?t.valueCallback(i):i;var l=e.slice(s.length);return{value:i,rest:l}}}function ur(r){"@babel/helpers - typeof";return ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ur(r)}function aO(r){if(r===null||r===!0||r===!1)return NaN;var e=Number(r);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function Fr(r,e){if(e.length<r)throw new TypeError(r+" argument"+(r>1?"s":"")+" required, but only "+e.length+" present")}function lO(r){Fr(1,arguments);var e=Object.prototype.toString.call(r);return r instanceof Date||ur(r)==="object"&&e==="[object Date]"?new Date(r.getTime()):typeof r=="number"||e==="[object Number]"?new Date(r):((typeof r=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}var cO={};function uO(){return cO}function uo(r,e){var t,n,s,o,i,l,c,u;Fr(1,arguments);var h=uO(),f=aO((t=(n=(s=(o=e==null?void 0:e.weekStartsOn)!==null&&o!==void 0?o:e==null||(i=e.locale)===null||i===void 0||(l=i.options)===null||l===void 0?void 0:l.weekStartsOn)!==null&&s!==void 0?s:h.weekStartsOn)!==null&&n!==void 0?n:(c=h.locale)===null||c===void 0||(u=c.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&t!==void 0?t:0);if(!(f>=0&&f<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var m=lO(r),y=m.getUTCDay(),v=(y<f?7:0)+y-f;return m.setUTCDate(m.getUTCDate()-v),m.setUTCHours(0,0,0,0),m}var dO={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},hO=function(e,t,n){var s,o=dO[e];return typeof o=="string"?s=o:t===1?s=o.one:s=o.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+s:s+" ago":s},pO={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},fO={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},gO={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},mO={date:Ee({formats:pO,defaultWidth:"full"}),time:Ee({formats:fO,defaultWidth:"full"}),dateTime:Ee({formats:gO,defaultWidth:"full"})},yO={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},bO=function(e,t,n,s){return yO[e]},vO={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},_O={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},SO={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},OO={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},wO={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},CO={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},TO=function(e,t){var n=Number(e),s=n%100;if(s>20||s<10)switch(s%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},EO={ordinalNumber:TO,era:ie({values:vO,defaultWidth:"wide"}),quarter:ie({values:_O,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:ie({values:SO,defaultWidth:"wide"}),day:ie({values:OO,defaultWidth:"wide"}),dayPeriod:ie({values:wO,defaultWidth:"wide",formattingValues:CO,defaultFormattingWidth:"wide"})},PO=/^(\d+)(th|st|nd|rd)?/i,$O=/\d+/i,IO={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},xO={any:[/^b/i,/^(a|c)/i]},AO={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},NO={any:[/1/i,/2/i,/3/i,/4/i]},jO={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},MO={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},DO={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},RO={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},LO={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},kO={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},FO={ordinalNumber:kr({matchPattern:PO,parsePattern:$O,valueCallback:function(e){return parseInt(e,10)}}),era:ae({matchPatterns:IO,defaultMatchWidth:"wide",parsePatterns:xO,defaultParseWidth:"any"}),quarter:ae({matchPatterns:AO,defaultMatchWidth:"wide",parsePatterns:NO,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ae({matchPatterns:jO,defaultMatchWidth:"wide",parsePatterns:MO,defaultParseWidth:"any"}),day:ae({matchPatterns:DO,defaultMatchWidth:"wide",parsePatterns:RO,defaultParseWidth:"any"}),dayPeriod:ae({matchPatterns:LO,defaultMatchWidth:"any",parsePatterns:kO,defaultParseWidth:"any"})},q1={code:"en-US",formatDistance:hO,formatLong:mO,formatRelative:bO,localize:EO,match:FO,options:{weekStartsOn:0,firstWeekContainsDate:1}};function ho(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function dr(r,e){return dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},dr(r,e)}function BO(r,e,t){Fr(2,arguments);var n=uo(r,t),s=uo(e,t);return n.getTime()===s.getTime()}var KO={lessThanXSeconds:{one:"1秒未満",other:"{{count}}秒未満",oneWithSuffix:"約1秒",otherWithSuffix:"約{{count}}秒"},xSeconds:{one:"1秒",other:"{{count}}秒"},halfAMinute:"30秒",lessThanXMinutes:{one:"1分未満",other:"{{count}}分未満",oneWithSuffix:"約1分",otherWithSuffix:"約{{count}}分"},xMinutes:{one:"1分",other:"{{count}}分"},aboutXHours:{one:"約1時間",other:"約{{count}}時間"},xHours:{one:"1時間",other:"{{count}}時間"},xDays:{one:"1日",other:"{{count}}日"},aboutXWeeks:{one:"約1週間",other:"約{{count}}週間"},xWeeks:{one:"1週間",other:"{{count}}週間"},aboutXMonths:{one:"約1か月",other:"約{{count}}か月"},xMonths:{one:"1か月",other:"{{count}}か月"},aboutXYears:{one:"約1年",other:"約{{count}}年"},xYears:{one:"1年",other:"{{count}}年"},overXYears:{one:"1年以上",other:"{{count}}年以上"},almostXYears:{one:"1年近く",other:"{{count}}年近く"}},WO=function(e,t,n){n=n||{};var s,o=KO[e];return typeof o=="string"?s=o:t===1?n.addSuffix&&o.oneWithSuffix?s=o.oneWithSuffix:s=o.one:n.addSuffix&&o.otherWithSuffix?s=o.otherWithSuffix.replace("{{count}}",String(t)):s=o.other.replace("{{count}}",String(t)),n.addSuffix?n.comparison&&n.comparison>0?s+"後":s+"前":s},zO={full:"y年M月d日EEEE",long:"y年M月d日",medium:"y/MM/dd",short:"y/MM/dd"},HO={full:"H時mm分ss秒 zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},VO={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},UO={date:Ee({formats:zO,defaultWidth:"full"}),time:Ee({formats:HO,defaultWidth:"full"}),dateTime:Ee({formats:VO,defaultWidth:"full"})},GO={lastWeek:"先週のeeeeのp",yesterday:"昨日のp",today:"今日のp",tomorrow:"明日のp",nextWeek:"翌週のeeeeのp",other:"P"},XO=function(e,t,n,s){return GO[e]},qO={narrow:["BC","AC"],abbreviated:["紀元前","西暦"],wide:["紀元前","西暦"]},YO={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["第1四半期","第2四半期","第3四半期","第4四半期"]},ZO={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]},JO={narrow:["日","月","火","水","木","金","土"],short:["日","月","火","水","木","金","土"],abbreviated:["日","月","火","水","木","金","土"],wide:["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"]},QO={narrow:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},abbreviated:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},wide:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"}},ew={narrow:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},abbreviated:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},wide:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"}},tw=function(e,t){var n=Number(e),s=String(t==null?void 0:t.unit);switch(s){case"year":return"".concat(n,"年");case"quarter":return"第".concat(n,"四半期");case"month":return"".concat(n,"月");case"week":return"第".concat(n,"週");case"date":return"".concat(n,"日");case"hour":return"".concat(n,"時");case"minute":return"".concat(n,"分");case"second":return"".concat(n,"秒");default:return"".concat(n)}},nw={ordinalNumber:tw,era:ie({values:qO,defaultWidth:"wide"}),quarter:ie({values:YO,defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:ie({values:ZO,defaultWidth:"wide"}),day:ie({values:JO,defaultWidth:"wide"}),dayPeriod:ie({values:QO,defaultWidth:"wide",formattingValues:ew,defaultFormattingWidth:"wide"})},rw=/^第?\d+(年|四半期|月|週|日|時|分|秒)?/i,sw=/\d+/i,ow={narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(紀元[前後]|西暦)/i,wide:/^(紀元[前後]|西暦)/i},iw={narrow:[/^B/i,/^A/i],any:[/^(紀元前)/i,/^(西暦|紀元後)/i]},aw={narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^第[1234一二三四１２３４]四半期/i},lw={any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},cw={narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])月/i,wide:/^([123456789]|1[012])月/i},uw={any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},dw={narrow:/^[日月火水木金土]/,short:/^[日月火水木金土]/,abbreviated:/^[日月火水木金土]/,wide:/^[日月火水木金土]曜日/},hw={any:[/^日/,/^月/,/^火/,/^水/,/^木/,/^金/,/^土/]},pw={any:/^(AM|PM|午前|午後|正午|深夜|真夜中|夜|朝)/i},fw={any:{am:/^(A|午前)/i,pm:/^(P|午後)/i,midnight:/^深夜|真夜中/i,noon:/^正午/i,morning:/^朝/i,afternoon:/^午後/i,evening:/^夜/i,night:/^深夜/i}},gw={ordinalNumber:kr({matchPattern:rw,parsePattern:sw,valueCallback:function(e){return parseInt(e,10)}}),era:ae({matchPatterns:ow,defaultMatchWidth:"wide",parsePatterns:iw,defaultParseWidth:"any"}),quarter:ae({matchPatterns:aw,defaultMatchWidth:"wide",parsePatterns:lw,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ae({matchPatterns:cw,defaultMatchWidth:"wide",parsePatterns:uw,defaultParseWidth:"any"}),day:ae({matchPatterns:dw,defaultMatchWidth:"wide",parsePatterns:hw,defaultParseWidth:"any"}),dayPeriod:ae({matchPatterns:pw,defaultMatchWidth:"any",parsePatterns:fw,defaultParseWidth:"any"})},Y1={code:"ja",formatDistance:WO,formatLong:UO,formatRelative:XO,localize:nw,match:gw,options:{weekStartsOn:0,firstWeekContainsDate:1}},mw={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},yw=function(e,t,n){var s,o=mw[e];return typeof o=="string"?s=o:t===1?s=o.one:s=o.other.replace("{{count}}",String(t)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?s+"内":s+"前":s},bw={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},vw={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},_w={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},Sw={date:Ee({formats:bw,defaultWidth:"full"}),time:Ee({formats:vw,defaultWidth:"full"}),dateTime:Ee({formats:_w,defaultWidth:"full"})};function po(r,e,t){var n="eeee p";return BO(r,e,t)?n:r.getTime()>e.getTime()?"'下个'"+n:"'上个'"+n}var Ow={lastWeek:po,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:po,other:"PP p"},ww=function(e,t,n,s){var o=Ow[e];return typeof o=="function"?o(t,n,s):o},Cw={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},Tw={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},Ew={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},Pw={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},$w={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Iw={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},xw=function(e,t){var n=Number(e);switch(t==null?void 0:t.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},Aw={ordinalNumber:xw,era:ie({values:Cw,defaultWidth:"wide"}),quarter:ie({values:Tw,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:ie({values:Ew,defaultWidth:"wide"}),day:ie({values:Pw,defaultWidth:"wide"}),dayPeriod:ie({values:$w,defaultWidth:"wide",formattingValues:Iw,defaultFormattingWidth:"wide"})},Nw=/^(第\s*)?\d+(日|时|分|秒)?/i,jw=/\d+/i,Mw={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},Dw={any:[/^(前)/i,/^(公元)/i]},Rw={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},Lw={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},kw={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},Fw={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},Bw={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},Kw={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},Ww={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},zw={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Hw={ordinalNumber:kr({matchPattern:Nw,parsePattern:jw,valueCallback:function(e){return parseInt(e,10)}}),era:ae({matchPatterns:Mw,defaultMatchWidth:"wide",parsePatterns:Dw,defaultParseWidth:"any"}),quarter:ae({matchPatterns:Rw,defaultMatchWidth:"wide",parsePatterns:Lw,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ae({matchPatterns:kw,defaultMatchWidth:"wide",parsePatterns:Fw,defaultParseWidth:"any"}),day:ae({matchPatterns:Bw,defaultMatchWidth:"wide",parsePatterns:Kw,defaultParseWidth:"any"}),dayPeriod:ae({matchPatterns:Ww,defaultMatchWidth:"any",parsePatterns:zw,defaultParseWidth:"any"})},Vw={code:"zh-CN",formatDistance:yw,formatLong:Sw,formatRelative:ww,localize:Aw,match:Hw,options:{weekStartsOn:1,firstWeekContainsDate:4}};const hr={code:"zh-CN",dateFnsLocale:Vw,Pagination:{pageSize:"每页条数：${pageSize}",total:"总页数：${total}",jumpTo:"跳至",page:"页"},Modal:{confirm:"确定",cancel:"取消"},Tabs:{more:"更多"},TimePicker:{placeholder:{time:"请选择时间",timeRange:"请选择时间范围"},begin:"开始时间",end:"结束时间",hour:"时",minute:"分",second:"秒",AM:"上午",PM:"下午"},DatePicker:{placeholder:{date:"请选择日期",dateTime:"请选择日期及时间",dateRange:["开始日期","结束日期"],dateTimeRange:["开始日期","结束日期"],monthRange:["开始月份","结束月份"]},presets:"快捷选择",footer:{confirm:"确定",cancel:"取消"},selectDate:"返回选择日期",selectTime:"选择时间",year:"年",month:"月",day:"日",monthText:"${year}年 ${month}",months:{1:"1月",2:"2月",3:"3月",4:"4月",5:"5月",6:"6月",7:"7月",8:"8月",9:"9月",10:"10月",11:"11月",12:"12月"},fullMonths:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12"},weeks:{Mon:"一",Tue:"二",Wed:"三",Thu:"四",Fri:"五",Sat:"六",Sun:"日"},localeFormatToken:{FORMAT_SWITCH_DATE:"yyyy-MM-dd"}},Navigation:{collapseText:"收起侧边栏",expandText:"展开侧边栏"},Popconfirm:{confirm:"确定",cancel:"取消"},Table:{emptyText:"暂无数据",pageText:"显示第 ${currentStart} 条-第 ${currentEnd} 条，共 ${total} 条",descend:"点击降序",ascend:"点击升序",cancelSort:"取消排序"},Select:{emptyText:"暂无数据",createText:"创建"},Cascader:{emptyText:"暂无数据"},Tree:{emptyText:"暂无数据",searchPlaceholder:"搜索"},List:{emptyText:"暂无数据"},Calendar:{allDay:"全天",AM:"上午${time}时",PM:"下午${time}时",datestring:"日",remaining:"还有${remained}项"},Upload:{mainText:"点击上传文件或拖拽文件到这里",illegalTips:"不支持此类型文件",legalTips:"松手开始上传",retry:"重试",replace:"替换文件",clear:"清空",selectedFiles:"已选择文件",illegalSize:"文件尺寸不合法",fail:"上传失败"},TreeSelect:{searchPlaceholder:"搜索"},Typography:{copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Transfer:{emptyLeft:"暂无数据",emptySearch:"无搜索结果",emptyRight:"暂无内容，可从左侧勾选",placeholder:"搜索",clear:"清空",selectAll:"全选",clearSelectAll:"取消全选",total:"总个数：${total}",selected:"已选个数：${total}"},Form:{optional:"（可选）"},Image:{preview:"预览",loading:"加载中",loadError:"加载失败",prevTip:"上一张",nextTip:"下一张",zoomInTip:"放大",zoomOutTip:"缩小",rotateTip:"旋转",downloadTip:"下载",adaptiveTip:"适应页面",originTip:"原始尺寸"},Chat:{deleteConfirm:"确认删除该会话吗？",clearContext:"上下文已清除",copySuccess:"复制成功",stop:"停止",copy:"复制",copied:"复制成功",dropAreaText:"将文件放到这里"}};class De extends $.Component{renderChildren(e,t){const{componentName:n}=this.props;let s=e;e!=null&&e.code||(s=hr);const o=R(hr,"dateFnsLocale"),i=R(s,"dateFnsLocale",o);return t(s[n],s.code,i)}render(){const{children:e}=this.props;return d.createElement(kt.Consumer,null,t=>{let{locale:n}=t;return d.createElement(Pi.Consumer,null,s=>this.renderChildren(n||s,e))})}}De.propTypes={componentName:a.string.isRequired,children:a.any};De.defaultProps={componentName:""};const Uw="semi";var Gw=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const $i=d.forwardRef((r,e)=>{const{svg:t,spin:n=!1,rotate:s,style:o,className:i,prefixCls:l=Uw,type:c,size:u="default"}=r,h=Gw(r,["svg","spin","rotate","style","className","prefixCls","type","size"]),f=E(`${l}-icon`,{[`${l}-icon-extra-small`]:u==="extra-small",[`${l}-icon-small`]:u==="small",[`${l}-icon-default`]:u==="default",[`${l}-icon-large`]:u==="large",[`${l}-icon-extra-large`]:u==="extra-large",[`${l}-icon-spinning`]:n===!0,[`${l}-icon-${c}`]:!!c},i),m={};return Number.isSafeInteger(s)&&(m.transform=`rotate(${s}deg)`),Object.assign(m,o),d.createElement("span",Object.assign({role:"img",ref:e,"aria-label":c,className:f,style:m},h),t)});$i.elementType="Icon";const Oe=(r,e)=>{const t=d.forwardRef((n,s)=>d.createElement($i,Object.assign({svg:d.createElement(r),type:e,ref:s},n)));return t.elementType="Icon",t};function Xw(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.08 7.6a1.5 1.5 0 0 1 2.12 0l5.66 5.65 5.66-5.65a1.5 1.5 0 1 1 2.12 2.12l-6.72 6.72a1.5 1.5 0 0 1-2.12 0L4.08 9.72a1.5 1.5 0 0 1 0-2.12Z",fill:"currentColor"}))}const Ii=Oe(Xw,"chevron_down");function qw(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.28 4.24a1.5 1.5 0 0 1 0 2.12l-5.66 5.66 5.66 5.65a1.5 1.5 0 1 1-2.12 2.13l-6.72-6.72a1.5 1.5 0 0 1 0-2.12l6.72-6.72a1.5 1.5 0 0 1 2.12 0Z",fill:"currentColor"}))}const Yw=Oe(qw,"chevron_left");function Zw(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.44 19.8a1.5 1.5 0 0 1 0-2.13l5.66-5.65-5.66-5.66a1.5 1.5 0 1 1 2.12-2.12l6.72 6.72a1.5 1.5 0 0 1 0 2.12L9.56 19.8a1.5 1.5 0 0 1-2.12 0Z",fill:"currentColor"}))}const Jw=Oe(Zw,"chevron_right");function Qw(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 23a11 11 0 1 0 0-22 11 11 0 0 0 0 22Zm5.04-6.14a1.5 1.5 0 0 1-2.13.04l-2.87-2.78L9.26 17A1.5 1.5 0 0 1 7.1 14.9l2.78-2.87L7 9.26A1.5 1.5 0 1 1 9.1 7.1l2.87 2.78L14.74 7A1.5 1.5 0 0 1 16.9 9.1l-2.78 2.87L17 14.74c.6.58.61 1.53.04 2.12Z",fill:"currentColor"}))}const xi=Oe(Qw,"clear");function eC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{d:"M17.66 19.78a1.5 1.5 0 0 0 2.12-2.12L14.12 12l5.66-5.66a1.5 1.5 0 0 0-2.12-2.12L12 9.88 6.34 4.22a1.5 1.5 0 1 0-2.12 2.12L9.88 12l-5.66 5.66a1.5 1.5 0 0 0 2.12 2.12L12 14.12l5.66 5.66Z",fill:"currentColor"}))}const Ai=Oe(eC,"close");function tC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{d:"M7 4c0-1.1.9-2 2-2h11a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2h-1V8c0-2-1-3-3-3H7V4Z",fill:"currentColor"}),$.createElement("path",{d:"M5 7a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h10a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5Z",fill:"currentColor"}))}const nC=Oe(tC,"copy");function rC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.7 3.7a1 1 0 0 0-1.4-1.4L17.3 5.26A11.59 11.59 0 0 0 12 4C5 4 1 10 1 12c0 1.18 1.38 3.73 3.94 5.64L2.3 20.3a1 1 0 1 0 1.42 1.42l18-18ZM7.84 14.77l1.46-1.47a3 3 0 0 1 4-4l1.47-1.46a5 5 0 0 0-6.93 6.93Z",fill:"currentColor"}),$.createElement("path",{d:"M12 20c-1.22 0-2.35-.18-3.38-.5l2.57-2.57a5 5 0 0 0 5.75-5.75l3.56-3.56C22.13 9.27 23 11.07 23 12c0 2-4 8-11 8Z",fill:"currentColor"}))}const sC=Oe(rC,"eye_closed_solid");function oC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 4C5 4 1 10 1 12s4 8 11 8 11-6 11-8-4-8-11-8Zm5 8a5 5 0 1 1-10 0 5 5 0 0 1 10 0Zm-5 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",fill:"currentColor"}))}const iC=Oe(oC,"eye_opened");function aC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.5 2a8.5 8.5 0 1 0 4.86 15.48l4.08 4.08a1.5 1.5 0 1 0 2.12-2.12l-4.08-4.08A8.5 8.5 0 0 0 10.5 2ZM5 10.5a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0Z",fill:"currentColor"}))}const lC=Oe(aC,"search");function cC(r){return $.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",focusable:!1,"aria-hidden":!0},r),$.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.35 4.27c.68.47.86 1.4.38 2.08l-10 14.5a1.5 1.5 0 0 1-2.33.17l-6.5-7a1.5 1.5 0 0 1 2.2-2.04l5.23 5.63 8.94-12.96a1.5 1.5 0 0 1 2.08-.38Z",fill:"currentColor"}))}const cn=Oe(cC,"tick"),Fe={BACKSPACE:8,TAB:9,ENTER:13,ESC:27,UP:38,DOWN:40},Br="Enter";function Kr(r){return R(r,"key")===Br}const Yn=Or.PREFIX;class Wr extends d.PureComponent{constructor(e){super(e),this.copy=t=>{const{content:n,duration:s,onCopy:o}=this.props,i=sO(n);o&&o(t,n,i),this.setCopied(n,s)},this.setCopied=(t,n)=>{this.setState({copied:!0,item:t}),this._timeId=setTimeout(()=>{this.resetCopied()},n*1e3)},this.resetCopied=()=>{this._timeId&&(clearTimeout(this._timeId),this._timeId=null,this.setState({copied:!1,item:""}))},this.renderSuccessTip=()=>{const{successTip:t}=this.props;return typeof t<"u"?t:d.createElement(De,{componentName:"Typography"},n=>d.createElement("span",null,d.createElement(cn,null),n.copied))},this.renderCopyIcon=()=>{const{icon:t}=this.props,n={role:"button",tabIndex:0,onClick:this.copy,onKeyPress:o=>Kr(o)&&this.copy(o)},s=d.createElement("a",{className:`${Yn}-action-copy-icon`},d.createElement(nC,Object.assign({onClick:this.copy},n)));return d.isValidElement(t)?d.cloneElement(t,n):s},this.state={copied:!1,item:""}}componentWillUnmount(){this._timeId&&(clearTimeout(this._timeId),this._timeId=null)}render(){const{style:e,className:t,forwardRef:n,copyTip:s,render:o}=this.props,{copied:i}=this.state,l=E(t,{[`${Yn}-action-copy`]:!i,[`${Yn}-action-copied`]:i});return o?o(i,this.copy,this.props):d.createElement(De,{componentName:"Typography"},c=>d.createElement("span",{style:Object.assign({marginLeft:"4px"},e),className:l,ref:n},i?this.renderSuccessTip():d.createElement(Ie,{content:typeof s<"u"?s:c.copy},this.renderCopyIcon())))}}Wr.propTypes={content:a.string,onCopy:a.func,successTip:a.node,copyTip:a.node,duration:a.number,style:a.object,className:a.string,icon:a.node};Wr.defaultProps={content:"",onCopy:Te,duration:3,style:{},className:""};const Ni={PREFIX:`${D}-popover`,ARROW:`${D}-popover-icon-arrow`},$t={POSITION_SET:["top","topLeft","topRight","left","leftTop","leftBottom","right","rightTop","rightBottom","bottom","bottomLeft","bottomRight","leftTopOver","rightTopOver"],TRIGGER_SET:["hover","focus","click","custom","contextMenu"],DEFAULT_ARROW_STYLE:{borderOpacity:"1",backgroundColor:"var(--semi-color-bg-3)",borderColor:"var(--semi-color-border)"}},Xe={ARROW_BOUNDING:Object.assign(Object.assign({},je.ARROW_BOUNDING),{offsetY:6,offsetX:0,height:8}),SPACING:4,SPACING_WITH_ARROW:10,DEFAULT_Z_INDEX:1030};var uC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const ji=function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{position:e="",className:t,arrowStyle:n,popStyle:s}=r,o=uC(r,["position","className","arrowStyle","popStyle"]),i=e.indexOf("top")===0||e.indexOf("bottom")===0,l=E(t,Ni.ARROW),c=R(n,"borderOpacity",$t.DEFAULT_ARROW_STYLE.borderOpacity),u=R(n,"backgroundColor",R(s,"backgroundColor",$t.DEFAULT_ARROW_STYLE.backgroundColor)),h=R(n,"borderColor",R(s,"borderColor",$t.DEFAULT_ARROW_STYLE.borderColor)),f=Object.assign(Object.assign({},o),{width:Xe.ARROW_BOUNDING.width,height:Xe.ARROW_BOUNDING.height,xmlns:"http://www.w3.org/2000/svg",className:l});return i?d.createElement("svg",Object.assign({},f),d.createElement("path",{d:"M0 0.5L0 1.5C4 1.5, 5.5 3, 7.5 5S10,8 12,8S14.5 7, 16.5 5S20,1.5 24,1.5L24 0.5L0 0.5z",fill:h,opacity:c}),d.createElement("path",{d:"M0 0L0 1C4 1, 5.5 2, 7.5 4S10,7 12,7S14.5  6, 16.5 4S20,1 24,1L24 0L0 0z",fill:u})):d.createElement("svg",Object.assign({},f),d.createElement("path",{d:"M0.5 0L1.5 0C1.5 4, 3 5.5, 5 7.5S8,10 8,12S7 14.5, 5 16.5S1.5,20 1.5,24L0.5 24L0.5 0z",fill:h,opacity:c}),d.createElement("path",{d:"M0 0L1 0C1 4, 2 5.5, 4 7.5S7,10 7,12S6 14.5, 4 16.5S1,20 1,24L0 24L0 0z",fill:u}))};ji.propTypes={position:a.string,className:a.string,arrowStyle:a.object,popStyle:a.object};var dC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const hC=$t.POSITION_SET,pC=$t.TRIGGER_SET;class xe extends d.PureComponent{constructor(e){super(e),this.focusTrigger=()=>{var t;(t=this.tooltipRef.current)===null||t===void 0||t.focusTrigger()},this.renderPopCard=t=>{let{initialFocusRef:n}=t;const{content:s,contentClassName:o,prefixCls:i}=this.props,{direction:l}=this.context,c=E(i,o,{[`${i}-rtl`]:l==="rtl"}),u=this.renderContentNode({initialFocusRef:n,content:s});return d.createElement("div",{className:c},d.createElement("div",{className:`${i}-content`},u))},this.renderContentNode=t=>{const{initialFocusRef:n,content:s}=t,o={initialFocusRef:n};return fe(s)?s(o):s},this.tooltipRef=d.createRef()}render(){const e=this.props,{children:t,prefixCls:n,showArrow:s,arrowStyle:o={},arrowBounding:i,position:l,style:c,trigger:u}=e,h=dC(e,["children","prefixCls","showArrow","arrowStyle","arrowBounding","position","style","trigger"]);let{spacing:f}=this.props;const m={position:l,className:"",popStyle:c,arrowStyle:o},y=s?d.createElement(ji,Object.assign({},m)):!1;Rt(f)&&(f=s?Xe.SPACING_WITH_ARROW:Xe.SPACING);const v=u==="click"||u==="custom"?"dialog":"tooltip";return d.createElement(Ie,Object.assign({guardFocus:!0,ref:this.tooltipRef},h,{trigger:u,position:l,style:c,content:this.renderPopCard,prefixCls:n,spacing:f,showArrow:y,arrowBounding:i,role:v}),t)}}xe.contextType=kt;xe.propTypes={children:a.node,content:a.oneOfType([a.node,a.func]),visible:a.bool,autoAdjustOverflow:a.bool,motion:a.bool,position:a.oneOf(hC),margin:a.oneOfType([a.number,a.object]),mouseEnterDelay:a.number,mouseLeaveDelay:a.number,trigger:a.oneOf(pC).isRequired,contentClassName:a.oneOfType([a.string,a.array]),onVisibleChange:a.func,onClickOutSide:a.func,style:a.object,spacing:a.oneOfType([a.number,a.object]),zIndex:a.number,showArrow:a.bool,arrowStyle:a.shape({borderColor:a.string,backgroundColor:a.string,borderOpacity:a.oneOfType([a.string,a.number])}),arrowPointAtCenter:a.bool,arrowBounding:a.object,prefixCls:a.string,guardFocus:a.bool,disableArrowKeyDown:a.bool};xe.__SemiComponentName__="Popover";xe.defaultProps=Le(xe.__SemiComponentName__,{arrowBounding:Xe.ARROW_BOUNDING,showArrow:!1,autoAdjustOverflow:!0,zIndex:Xe.DEFAULT_Z_INDEX,motion:!0,trigger:"hover",cancelText:"No",okText:"Yes",position:"bottom",prefixCls:Ni.PREFIX,onClickOutSide:A,onEscKeyDown:A,closeOnEsc:!0,returnFocusOnClose:!0,guardFocus:!0,disableFocusListener:!0});let H;function Zn(r){if(!r)return 0;const e=r.match(/^\d*(\.\d*)?/);return e?Number(e[0]):0}function fC(r){return Array.prototype.slice.apply(r).map(t=>`${t}: ${r.getPropertyValue(t)};`).join("")}const gC=function(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",n=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0,i=arguments.length>6?arguments[6]:void 0,l=arguments.length>7?arguments[7]:void 0;if(t.length===0)return"";H||(H=document.createElement("div"),H.setAttribute("aria-hidden","true"),document.body.appendChild(H));const c=window.getComputedStyle(r),u=fC(c),h=Zn(c.lineHeight),f=Math.round(h*(e+1)+Zn(c.paddingTop)+Zn(c.paddingBottom));H.setAttribute("style",u),H.style.position="fixed",H.style.left="0",c.getPropertyValue("width")==="auto"&&r.offsetWidth&&(H.style.width=`${r.offsetWidth}px`),H.style.height="auto",H.style.top="-999999px",H.style.zIndex="-1000",l&&(H.style.fontWeight="600"),H.style.textOverflow="clip",H.style.webkitLineClamp="none",H.innerHTML="";function m(){const O=H.scrollWidth<=H.offsetWidth,T=H.scrollHeight<f;return e===1?O&&T:T}const y=document.createElement("span"),v=document.createTextNode(t);if(y.appendChild(v),o.length>0){const O=document.createTextNode(o);y.appendChild(O)}H.appendChild(y),Object.values(Dt(n,"expand")).map(O=>O&&H.appendChild(O.cloneNode(!0)));function p(){H.innerHTML="",H.appendChild(y),Object.values(n).map(O=>O&&H.appendChild(O.cloneNode(!0)))}function g(O,T){const C=O.length;return T?i==="end"?O.slice(0,T)+s:O.slice(0,T)+s+O.slice(C-T,C):s}function b(O,T){let C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,S=arguments.length>3&&arguments[3]!==void 0?arguments[3]:T.length,w=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;const P=Math.floor((C+S)/2),x=g(T,P);if(O.textContent=x,C>=S-1&&S>0)for(let M=S;M>=C;M-=1){const j=g(T,M);if(O.textContent=j,m())return j}else if(S===0)return s;return m()?b(O,T,P,S,P):b(O,T,C,P,w)}let _=t;return m()||(p(),_=b(v,t,0,i==="middle"?Math.floor(t.length/2):t.length)),H.innerHTML="",_},Mi=d.createContext("normal");var un;(function(r){r.Width="width",r.Height="height",r.All="all"})(un||(un={}));class rt extends ce{constructor(e){var t;super(e),t=this,this.formerPropertyValue=new Map,this.getElement=()=>{try{return yr.findDOMNode(this.childNode||this)}catch{return null}},this.handleResizeEventTriggered=n=>{var s,o,i,l;if(this.props.observerProperty===un.All)(o=(s=this.props).onResize)===null||o===void 0||o.call(s,n);else{const c=[];for(const u of n)this.formerPropertyValue.has(u.target)?u.contentRect[this.props.observerProperty]!==this.formerPropertyValue.get(u.target)&&(this.formerPropertyValue.set(u.target,u.contentRect[this.props.observerProperty]),c.push(u)):(this.formerPropertyValue.set(u.target,u.contentRect[this.props.observerProperty]),c.push(u));c.length>0&&((l=(i=this.props).onResize)===null||l===void 0||l.call(i,c))}},this.observeElement=function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const s=t.getElement();if(t.observer||(t.observer=new ResizeObserver(t.handleResizeEventTriggered)),!(s&&s instanceof Element)){t.observer.disconnect();return}s===t.element&&!n||(t.observer.disconnect(),t.element=s,t.observer.observe(s),t.props.observeParent&&s.parentNode&&s.parentNode.ownerDocument&&s.parentNode.ownerDocument.defaultView&&s.parentNode instanceof s.parentNode.ownerDocument.defaultView.HTMLElement&&(t._parentNode=s.parentNode,t.observer.observe(t._parentNode)))},this.mergeRef=(n,s)=>{this.childNode=s,typeof n=="function"?n(s):typeof n=="object"&&n&&"current"in n&&(n.current=s)},globalThis.ResizeObserver&&(this.observer=new ResizeObserver(this.handleResizeEventTriggered))}componentDidMount(){var e;(e=this.observeElement)===null||e===void 0||e.call(this)}componentDidUpdate(e){var t;(t=this.observeElement)===null||t===void 0||t.call(this,this.props.observeParent!==e.observeParent)}componentWillUnmount(){this.observer&&(this.observer.disconnect(),this.observer=null,this.element=null)}render(){const e=d.Children.only(this.props.children),{ref:t}=e;return d.cloneElement(e,{ref:n=>this.mergeRef(t,n)})}}rt.propTypes={onResize:a.func,observeParent:a.bool,observerProperty:a.string,delayTick:a.number};rt.defaultProps={onResize:()=>{},observeParent:!1,observerProperty:"all",delayTick:0};var Jn=function(r,e,t,n){function s(o){return o instanceof t?o:new t(function(i){i(o)})}return new(t||(t=Promise))(function(o,i){function l(h){try{u(n.next(h))}catch(f){i(f)}}function c(h){try{u(n.throw(h))}catch(f){i(f)}}function u(h){h.done?o(h.value):s(h.value).then(l,c)}u((n=n.apply(r,e||[])).next())})},mC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const Q=Or.PREFIX,yC="...",bC=(r,e)=>{const{mark:t,code:n,underline:s,strong:o,link:i,disabled:l}=r;let c=e;const u=(h,f)=>{let m={};h&&(typeof h=="object"&&(m=Object.assign({},h)),c=d.createElement(f,m,c))};return u(t,"mark"),u(n,"code"),u(s&&!i,"u"),u(o,"strong"),u(r.delete,"del"),u(i,l?"span":"a"),c};class En extends $.Component{constructor(e){super(e),this.observerTakingEffect=!1,this.onResize=t=>Jn(this,void 0,void 0,function*(){return this.rafId&&window.cancelAnimationFrame(this.rafId),new Promise(n=>{this.rafId=window.requestAnimationFrame(()=>Jn(this,void 0,void 0,function*(){yield this.getEllipsisState(),n()}))})}),this.canUseCSSEllipsis=()=>{const{copyable:t}=this.props,{expandable:n,expandText:s,pos:o,suffix:i}=this.getEllipsisOpt();return!n&&Et(s)&&!t&&o==="end"&&!i.length},this.shouldTruncated=t=>!t||t<1?!1:t<=1?this.compareSingleRow():this.wrapperRef.current.scrollHeight>this.wrapperRef.current.offsetHeight,this.compareSingleRow=()=>{if(!(document&&document.createRange))return!1;const t=this.wrapperRef.current,n=t.getBoundingClientRect().width,s=Array.from(t.childNodes),o=document.createRange(),i=s.reduce((l,c)=>{var u;return o.selectNodeContents(c),l+((u=o.getBoundingClientRect().width)!==null&&u!==void 0?u:0)},0);return o.detach(),i>n},this.showTooltip=()=>{var t,n;const{isOverflowed:s,isTruncated:o,expanded:i}=this.state,{showTooltip:l,expandable:c,expandText:u}=this.getEllipsisOpt(),h=this.canUseCSSEllipsis(),f=!i&&(h?s:o),y=!c&&Et(u)&&f&&l;if(!y)return y;const v={type:"tooltip"};return typeof l=="object"?l.type&&l.type.toLowerCase()==="popover"?wg({opts:{showArrow:!0}},l,{opts:{className:E({[`${Q}-ellipsis-popover`]:!0,[(t=l==null?void 0:l.opts)===null||t===void 0?void 0:t.className]:!!(!((n=l==null?void 0:l.opts)===null||n===void 0)&&n.className)})}}):Object.assign(Object.assign({},v),l):v},this.onHover=()=>{if(this.canUseCSSEllipsis()){const{rows:n,suffix:s,pos:o}=this.getEllipsisOpt(),i=this.shouldTruncated(n);this.setState({isOverflowed:i,isTruncated:!1});return}},this.getEllipsisState=()=>Jn(this,void 0,void 0,function*(){const{rows:t,suffix:n,pos:s}=this.getEllipsisOpt(),{children:o,strong:i}=this.props;if(!this.wrapperRef||!this.wrapperRef.current){yield this.onResize();return}const{expanded:l}=this.state;if(this.canUseCSSEllipsis())return;if(Sd(o))return new Promise(m=>{this.setState({isTruncated:!1,isOverflowed:!1},m)});if(ot("children"in this.props&&typeof o!="string","[Semi Typography] Only children with pure text could be used with ellipsis at this moment."),!t||t<0||l)return;const u={expand:this.expandRef.current,copy:this.copyRef&&this.copyRef.current},h=Array.isArray(o)?o.join(""):String(o),f=gC(this.wrapperRef.current,t,h,u,yC,n,s,i);return new Promise(m=>{this.setState({isOverflowed:!1,ellipsisContent:f,isTruncated:h!==f},m)})}),this.toggleOverflow=t=>{const{onExpand:n,expandable:s,collapsible:o}=this.getEllipsisOpt(),{expanded:i}=this.state;n&&n(!i,t),(s&&!i||o&&i)&&this.setState({expanded:!i})},this.getEllipsisOpt=()=>{const{ellipsis:t}=this.props;return t?Object.assign({rows:1,expandable:!1,pos:"end",suffix:"",showTooltip:!1,collapsible:!1,expandText:t.expandable?this.expandStr:void 0,collapseText:t.collapsible?this.collapseStr:void 0},typeof t=="object"?t:null):{}},this.renderExpandable=()=>{const{expanded:t,isTruncated:n}=this.state;if(!n)return null;const{expandText:s,expandable:o,collapseText:i,collapsible:l}=this.getEllipsisOpt(),c=!o&&Et(s),u=!l&&Et(i);let h;return!t&&!c?h=s:t&&!u&&(h=i),!c||!u?d.createElement("a",{role:"button",tabIndex:0,className:`${Q}-ellipsis-expand`,key:"expand",ref:this.expandRef,"aria-label":h,onClick:this.toggleOverflow,onKeyPress:f=>Kr(f)&&this.toggleOverflow(f)},h):null},this.getEllipsisStyle=()=>{const{ellipsis:t,component:n}=this.props;if(!t)return{ellipsisCls:"",ellipsisStyle:{}};const{rows:s}=this.getEllipsisOpt(),{expanded:o}=this.state,i=!o&&this.canUseCSSEllipsis(),l=E({[`${Q}-ellipsis`]:!0,[`${Q}-ellipsis-single-line`]:s===1,[`${Q}-ellipsis-multiple-line`]:s>1,[`${Q}-ellipsis-multiple-line-text`]:s>1&&n==="span",[`${Q}-ellipsis-overflow-ellipsis`]:s===1&&i,[`${Q}-ellipsis-overflow-ellipsis-text`]:s===1&&i&&n==="span"}),c=i&&s>1?{WebkitLineClamp:s}:{};return{ellipsisCls:l,ellipsisStyle:c}},this.renderEllipsisText=t=>{const{suffix:n}=t,{children:s}=this.props,{isTruncated:o,expanded:i,ellipsisContent:l}=this.state;return i||!o?d.createElement("span",{onMouseEnter:this.onHover},s,n&&n.length?n:null):d.createElement("span",{onMouseEnter:this.onHover},l,n)},this.state={editable:!1,copied:!1,isOverflowed:!1,ellipsisContent:e.children,expanded:!1,isTruncated:!1,prevChildren:null},this.wrapperRef=d.createRef(),this.expandRef=d.createRef(),this.copyRef=d.createRef()}componentDidMount(){this.props.ellipsis&&this.onResize().then(()=>Dr(()=>this.observerTakingEffect=!0,1))}static getDerivedStateFromProps(e,t){const{prevChildren:n}=t,s={};return s.prevChildren=e.children,e.ellipsis&&n!==e.children&&(s.isOverflowed=!1,s.ellipsisContent=e.children,s.expanded=!1,s.isTruncated=!0),s}componentDidUpdate(e){this.props.children!==e.children&&(this.forceUpdate(),this.props.ellipsis&&this.onResize())}componentWillUnmount(){this.rafId&&window.cancelAnimationFrame(this.rafId)}renderOperations(){return d.createElement(d.Fragment,null,this.renderExpandable(),this.renderCopy())}renderCopy(){var e;const{copyable:t,children:n}=this.props;if(!t)return null;const s=(e=t==null?void 0:t.content)!==null&&e!==void 0?e:n;let o,i=!1;Array.isArray(s)?(o="",s.forEach(c=>{typeof c=="object"&&(i=!0),o+=String(c)})):(typeof s!="object"||(i=!0),o=String(s)),ot(i,"Content to be copied in Typography is a object, it will case a [object Object] mistake when copy to clipboard.");const l=Object.assign({content:o,duration:3},typeof t=="object"?t:null);return d.createElement(Wr,Object.assign({},l,{forwardRef:this.copyRef}))}renderIcon(){const{icon:e,size:t}=this.props,n=t==="inherit"?this.context:t;if(!e)return null;const s=n==="small"?"small":"default";return d.createElement("span",{className:`${Q}-icon`,"x-semi-prop":"icon"},Me(e)?d.cloneElement(e,{size:s}):e)}renderContent(){const e=this.props,{component:t,children:n,className:s,type:o,spacing:i,disabled:l,style:c,ellipsis:u,icon:h,size:f,link:m,heading:y,weight:v}=e,p=mC(e,["component","children","className","type","spacing","disabled","style","ellipsis","icon","size","link","heading","weight"]),g=Dt(p,["strong","editable","mark","copyable","underline","code","delete"]),b=f==="inherit"?this.context:f,_=this.renderIcon(),O=this.getEllipsisOpt(),{ellipsisCls:T,ellipsisStyle:C}=this.getEllipsisStyle();let S=u?this.renderEllipsisText(O):n;const w=E({[`${Q}-link-text`]:m,[`${Q}-link-underline`]:this.props.underline&&m});S=bC(this.props,d.createElement(d.Fragment,null,_,this.props.link?d.createElement("span",{className:w},S):S));const P=/^h[1-6]$/,x=ee(y)&&P.test(y),M=E(s,T,{[`${Q}-${o}`]:o&&!m,[`${Q}-${b}`]:b,[`${Q}-link`]:m,[`${Q}-disabled`]:l,[`${Q}-${i}`]:i,[`${Q}-${y}`]:x,[`${Q}-${y}-weight-${v}`]:x&&v&&isNaN(Number(v))}),j=Object.assign(Object.assign({},isNaN(Number(v))?{}:{fontWeight:v}),c);return d.createElement(wr,Object.assign({className:M,style:Object.assign(Object.assign({},j),C),component:t,forwardRef:this.wrapperRef},g),S,this.renderOperations())}renderTipWrapper(){const{children:e}=this.props,t=this.showTooltip(),n=this.renderContent();if(t){const{type:s,opts:o,renderTooltip:i}=t;return fe(i)?i(e,n):s.toLowerCase()==="popover"?d.createElement(xe,Object.assign({content:e,position:"top"},o),n):d.createElement(Ie,Object.assign({content:e,position:"top"},o),n)}else return n}render(){var e=this;const{size:t}=this.props,n=t==="inherit"?this.context:t,s=d.createElement(Mi.Provider,{value:n},d.createElement(De,{componentName:"Typography"},o=>(this.expandStr=o.expand,this.collapseStr=o.collapse,this.renderTipWrapper())));return this.props.ellipsis?d.createElement(rt,{onResize:function(){e.observerTakingEffect&&e.onResize(...arguments)},observeParent:!0,observerProperty:un.Width},s):s}}En.propTypes={children:a.node,copyable:a.oneOfType([a.shape({text:a.string,onCopy:a.func,successTip:a.node,copyTip:a.node}),a.bool]),delete:a.bool,disabled:a.bool,ellipsis:a.oneOfType([a.shape({rows:a.number,expandable:a.bool,expandText:a.string,onExpand:a.func,suffix:a.string,showTooltip:a.oneOfType([a.shape({type:a.string,opts:a.object}),a.bool]),collapsible:a.bool,collapseText:a.string,pos:a.oneOf(["end","middle"])}),a.bool]),mark:a.bool,underline:a.bool,link:a.oneOfType([a.object,a.bool]),spacing:a.oneOf(Pt.SPACING),strong:a.bool,size:a.oneOf(Pt.SIZE),type:a.oneOf(Pt.TYPE),style:a.object,className:a.string,icon:a.oneOfType([a.node,a.string]),heading:a.string,component:a.string};En.defaultProps={children:null,copyable:!1,delete:!1,disabled:!1,ellipsis:!1,icon:"",mark:!1,underline:!1,strong:!1,link:!1,type:"primary",spacing:"normal",size:"normal",style:{},className:""};En.contextType=Mi;class zr extends $.PureComponent{render(){return d.createElement(En,Object.assign({component:"span"},this.props))}}zr.propTypes={copyable:a.oneOfType([a.object,a.bool]),delete:a.bool,disabled:a.bool,icon:a.oneOfType([a.node,a.string]),ellipsis:a.oneOfType([a.object,a.bool]),mark:a.bool,underline:a.bool,link:a.oneOfType([a.object,a.bool]),strong:a.bool,type:a.oneOf(Pt.TYPE),size:a.oneOf(Pt.SIZE),style:a.object,className:a.string,code:a.bool,component:a.string,weight:a.number};zr.defaultProps={copyable:!1,delete:!1,disabled:!1,icon:"",ellipsis:!1,mark:!1,underline:!1,strong:!1,link:!1,type:"primary",style:{},size:"normal",className:""};var vC=Re,_C=_e,SC="[object Number]";function OC(r){return typeof r=="number"||_C(r)&&vC(r)==SC}var wC=OC;const Ve=Y(wC),CC=`${D}-spin`,TC={PREFIX:CC},EC={SIZE:["small","middle","large"]};class Hr extends ye{static get spinDefaultAdapter(){return{getProp:()=>{},setLoading:e=>{}}}constructor(e){super(Object.assign(Object.assign({},Hr.spinDefaultAdapter),e))}updateLoadingIfNeedDelay(){const{spinning:e,delay:t}=this._adapter.getProps(),{delay:n}=this._adapter.getStates();if(n){const s=this;this._timer=setTimeout(()=>{s._adapter.setState({loading:e,delay:0})},t)}}destroy(){this._timer&&(clearTimeout(this._timer),this._timer=null)}}var PC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};let fo=-1;function Di(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{id:e,className:t}=r,n=PC(r,["id","className"]);let s=e;Rt(s)&&(fo++,s=fo);const o=`linearGradient-${s}`;return d.createElement("svg",Object.assign({},n,{className:t,width:"48",height:"48",viewBox:"0 0 36 36",version:"1.1",xmlns:"http://www.w3.org/2000/svg","aria-hidden":!0,"data-icon":"spin"}),d.createElement("defs",null,d.createElement("linearGradient",{x1:"0%",y1:"100%",x2:"100%",y2:"100%",id:o},d.createElement("stop",{stopColor:"currentColor",stopOpacity:"0",offset:"0%"}),d.createElement("stop",{stopColor:"currentColor",stopOpacity:"0.50",offset:"39.9430698%"}),d.createElement("stop",{stopColor:"currentColor",offset:"100%"}))),d.createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},d.createElement("rect",{fillOpacity:"0.01",fill:"none",x:"0",y:"0",width:"36",height:"36"}),d.createElement("path",{d:"M34,18 C34,9.163444 26.836556,2 18,2 C11.6597233,2 6.18078805,5.68784135 3.59122325,11.0354951",stroke:`url(#${o})`,strokeWidth:"4",strokeLinecap:"round"})))}var $C=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const Be=TC.PREFIX;class Vr extends ce{constructor(e){super(e),this.foundation=new Hr(this.adapter),this.state={delay:e.delay,loading:!0}}static getDerivedStateFromProps(e){return e.delay?e.spinning===!1?{delay:0,loading:!1}:{delay:e.delay}:{loading:e.spinning}}get adapter(){return Object.assign(Object.assign({},super.adapter),{setLoading:e=>{this.setState({loading:e})}})}componentWillUnmount(){this.foundation.destroy()}renderSpin(){const{indicator:e,tip:t}=this.props,{loading:n}=this.state;return n?d.createElement("div",{className:`${Be}-wrapper`},e?d.createElement("div",{className:`${Be}-animate`,"x-semi-prop":"indicator"},e):d.createElement(Di,null),t?d.createElement("div",{"x-semi-prop":"tip"},t):null):null}render(){this.foundation.updateLoadingIfNeedDelay();const e=this.props,{children:t,style:n,wrapperClassName:s,childStyle:o,size:i}=e,l=$C(e,["children","style","wrapperClassName","childStyle","size"]),{loading:c}=this.state,u=E(Be,s,{[`${Be}-${i}`]:i,[`${Be}-block`]:t,[`${Be}-hidden`]:!c});return d.createElement("div",Object.assign({className:u,style:n},this.getDataAttr(l)),this.renderSpin(),d.createElement("div",{className:`${Be}-children`,style:o,"x-semi-prop":"children"},t))}}Vr.propTypes={size:a.oneOf(EC.SIZE),spinning:a.bool,children:a.node,indicator:a.node,delay:a.number,tip:a.node,wrapperClassName:a.string,childStyle:a.object,style:a.object};Vr.defaultProps={size:"middle",spinning:!0,children:null,indicator:null,delay:0};const IC={PREFIX:`${D}-input`},Pn={SIZE:["small","large","default"],STATUS:["default","error","warning","success"],CLEARBTN_CLICKED_EVENT_FLAG:"__fromClearBtn",MODE:["password"]};function xC(r){const{value:e,maxLength:t,getValueLength:n}=r;if(fe(n)){let s=0,o=e.length;for(;s<o;){const i=s+Math.floor((o-s)/2),l=e.slice(0,i+1);n(l)>t?o=i:s=i+1}return e.slice(0,s)}else return e.slice(0,t)}class Ur extends ye{static get inputDefaultAdapter(){return{notifyChange:A,setValue:A}}constructor(e){super(Object.assign(Object.assign({},Ur.inputDefaultAdapter),e))}destroy(){this._timer&&(clearTimeout(this._timer),this._timer=null)}setDisable(){}setValue(e){this._adapter.setValue(e)}handleChange(e,t){const{maxLength:n,minLength:s,getValueLength:o}=this._adapter.getProps();let i=e;n&&fe(o)&&(i=this.handleVisibleMaxLength(e)),s&&fe(o)&&this.handleVisibleMinLength(i),this._isControlledComponent()?this._adapter.notifyChange(i,t):(this._adapter.setValue(i),this._adapter.notifyChange(i,t))}handleVisibleMinLength(e){const{minLength:t,getValueLength:n}=this._adapter.getProps(),{minLength:s}=this._adapter.getStates();if(Ve(t)&&t>=0&&fe(n)&&ee(e)){const o=n(e);if(o<t){const i=e.length+(t-o);i!==s&&this._adapter.setMinLength(i)}else s!==t&&this._adapter.setMinLength(t)}}handleVisibleMaxLength(e){const{maxLength:t,getValueLength:n}=this._adapter.getProps();return Ve(t)&&t>=0&&fe(n)&&ee(e)&&n(e)>t?(console.warn("[Semi Input] The input character is truncated because the input length exceeds the maximum length limit"),this.handleTruncateValue(e,t)):e}handleTruncateValue(e,t){const{getValueLength:n}=this._adapter.getProps();return xC({value:e,maxLength:t,getValueLength:n})}handleClear(e){let t=e;const n="";this._isControlledComponent("value")?this._adapter.setState({isFocus:!1}):this._adapter.setState({value:"",isFocus:!1}),(!t||typeof t!="object")&&(t={}),VS(t,Pn.CLEARBTN_CLICKED_EVENT_FLAG,!0),this._adapter.notifyChange(n,t),this._adapter.notifyClear(t),t&&this.stopPropagation(t)}handleClick(e){const{disabled:t}=this._adapter.getProps(),{isFocus:n}=this._adapter.getStates();t||n||this._adapter.isEventTarget(e)&&(this._adapter.focusInput(),this._adapter.toggleFocusing(!0))}handleModeChange(e){e==="password"?this._adapter.setEyeClosed(!0):this._adapter.setEyeClosed(!1)}handleClickEye(e){const t=this._adapter.getState("eyeClosed");this._adapter.focusInput(),this._adapter.toggleFocusing(!0),this._adapter.setEyeClosed(!t)}handleInputType(e){const t=this._adapter.getProp("mode"),n=this._adapter.getState("eyeClosed");return t==="password"?n?"password":"text":e}handleMouseDown(e){e.preventDefault()}handleMouseUp(e){e.preventDefault()}handleBlur(e){const{value:t}=this.getStates();this._adapter.toggleFocusing(!1),this._adapter.notifyBlur(t,e)}handleFocus(e){const{value:t}=this.getStates();this._adapter.toggleFocusing(!0),this._adapter.notifyFocus(t,e)}handleInput(e){this._adapter.notifyInput(e)}handleKeyDown(e){this._adapter.notifyKeyDown(e)}handleKeyUp(e){this._adapter.notifyKeyUp(e)}handleKeyPress(e){this._adapter.notifyKeyPress(e),e.key===Br&&this._adapter.notifyEnterPress(e)}isAllowClear(){const{value:e,isFocus:t,isHovering:n}=this._adapter.getStates(),{showClear:s,disabled:o,showClearIgnoreDisabled:i}=this._adapter.getProps();return e&&s&&(!o||i)&&(t||n)}handleClickPrefixOrSuffix(e){const{disabled:t}=this._adapter.getProps(),{isFocus:n}=this._adapter.getStates();!t&&!n&&(this._adapter.focusInput(),this._adapter.toggleFocusing(!0))}handlePreventMouseDown(e){e&&fe(e.preventDefault)&&e.preventDefault()}handleModeEnterPress(e){["Enter"," "].includes(e==null?void 0:e.key)&&(this.handlePreventMouseDown(e),this.handleClickEye(e))}}var AC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const k=IC.PREFIX,NC=Pn.SIZE,jC=Pn.STATUS,MC=Pn.MODE;class Gr extends ce{constructor(e){super(e),this.handleClear=n=>{this.foundation.handleClear(n)},this.handleClick=n=>{this.foundation.handleClick(n)},this.handleMouseOver=n=>{this.setState({isHovering:!0})},this.handleMouseLeave=n=>{this.setState({isHovering:!1})},this.handleModeChange=n=>{this.foundation.handleModeChange(n)},this.handleClickEye=n=>{this.foundation.handleClickEye(n)},this.handleMouseDown=n=>{this.foundation.handleMouseDown(n)},this.handleMouseUp=n=>{this.foundation.handleMouseUp(n)},this.handleModeEnterPress=n=>{this.foundation.handleModeEnterPress(n)},this.handleClickPrefixOrSuffix=n=>{this.foundation.handleClickPrefixOrSuffix(n)},this.handlePreventMouseDown=n=>{this.foundation.handlePreventMouseDown(n)};const t="value"in e?e.value:e.defaultValue;this.state={value:t,cachedValue:e.value,disabled:!1,props:{},isFocus:!1,isHovering:!1,eyeClosed:e.mode==="password",minLength:e.minLength},this.inputRef=d.createRef(),this.prefixRef=d.createRef(),this.suffixRef=d.createRef(),this.foundation=new Ur(this.adapter)}get adapter(){return Object.assign(Object.assign({},super.adapter),{setValue:e=>this.setState({value:e}),setEyeClosed:e=>this.setState({eyeClosed:e}),toggleFocusing:e=>{this.setState({isFocus:e})},focusInput:()=>{const{preventScroll:e}=this.props,t=this.inputRef&&this.inputRef.current;t&&t.focus({preventScroll:e})},toggleHovering:e=>this.setState({isHovering:e}),getIfFocusing:()=>this.state.isFocus,notifyChange:(e,t)=>this.props.onChange(e,t),notifyBlur:(e,t)=>this.props.onBlur(t),notifyFocus:(e,t)=>this.props.onFocus(t),notifyInput:e=>this.props.onInput(e),notifyKeyPress:e=>this.props.onKeyPress(e),notifyKeyDown:e=>this.props.onKeyDown(e),notifyKeyUp:e=>this.props.onKeyUp(e),notifyEnterPress:e=>this.props.onEnterPress(e),notifyClear:e=>this.props.onClear(e),setMinLength:e=>this.setState({minLength:e}),isEventTarget:e=>e&&e.target===e.currentTarget})}static getDerivedStateFromProps(e,t){const n={};return e.value!==t.cachedValue&&(n.value=e.value,n.cachedValue=e.value),n}componentDidUpdate(e){const{mode:t}=this.props;e.mode!==t&&this.handleModeChange(t)}componentDidMount(){const{disabled:e,autoFocus:t,preventScroll:n}=this.props;!e&&(t||this.props.autofocus)&&this.inputRef.current.focus({preventScroll:n})}renderPrepend(){const{addonBefore:e}=this.props;if(e){const t=E({[`${k}-prepend`]:!0,[`${k}-prepend-text`]:e&&ee(e),[`${k}-prepend-icon`]:Me(e)});return d.createElement("div",{className:t,"x-semi-prop":"addonBefore"},e)}return null}renderAppend(){const{addonAfter:e}=this.props;if(e){const t=E({[`${k}-append`]:!0,[`${k}-append-text`]:e&&ee(e),[`${k}-append-icon`]:Me(e)});return d.createElement("div",{className:t,"x-semi-prop":"addonAfter"},e)}return null}renderClearBtn(){const e=E(`${k}-clearbtn`),{clearIcon:t}=this.props;return this.foundation.isAllowClear()?d.createElement("div",{className:e,onMouseDown:this.handleClear},t||d.createElement(xi,null)):null}renderModeBtn(){const{eyeClosed:e}=this.state,{mode:t,disabled:n}=this.props,s=E(`${k}-modebtn`),o=e?d.createElement(sC,null):d.createElement(iC,null),i=t==="password"&&!n,l=e?"Show password":"Hidden password";return i?d.createElement("div",{role:"button",tabIndex:0,"aria-label":l,className:s,onClick:this.handleClickEye,onMouseDown:this.handleMouseDown,onMouseUp:this.handleMouseUp,onKeyPress:this.handleModeEnterPress},o):null}renderPrefix(){const{prefix:e,insetLabel:t,insetLabelId:n}=this.props,s=e||t;if(!s)return null;const o=E({[`${k}-prefix`]:!0,[`${k}-inset-label`]:t,[`${k}-prefix-text`]:s&&ee(s),[`${k}-prefix-icon`]:Me(s)});return d.createElement("div",{className:o,onMouseDown:this.handlePreventMouseDown,onClick:this.handleClickPrefixOrSuffix,id:n,"x-semi-prop":"prefix,insetLabel"},s)}renderSuffix(e){const{suffix:t,hideSuffix:n}=this.props;if(!t)return null;const s=E({[`${k}-suffix`]:!0,[`${k}-suffix-text`]:t&&ee(t),[`${k}-suffix-icon`]:Me(t),[`${k}-suffix-hidden`]:e&&!!n});return d.createElement("div",{className:s,onMouseDown:this.handlePreventMouseDown,onClick:this.handleClickPrefixOrSuffix,"x-semi-prop":"suffix"},t)}getInputRef(){const{forwardRef:e}=this.props;if(!Et(e)){if(typeof e=="function")return t=>{e(t),this.inputRef={current:t}};if(Object.prototype.toString.call(e)==="[object Object]")return this.inputRef=e,e}return this.inputRef}render(){const e=this.props,{addonAfter:t,addonBefore:n,autoFocus:s,clearIcon:o,className:i,disabled:l,defaultValue:c,placeholder:u,prefix:h,mode:f,insetLabel:m,insetLabelId:y,validateStatus:v,type:p,readonly:g,size:b,suffix:_,style:O,showClear:T,onEnterPress:C,onClear:S,hideSuffix:w,inputStyle:P,forwardRef:x,maxLength:M,getValueLength:j,preventScroll:B,borderless:K,showClearIgnoreDisabled:Z,onlyBorder:q}=e,se=AC(e,["addonAfter","addonBefore","autoFocus","clearIcon","className","disabled","defaultValue","placeholder","prefix","mode","insetLabel","insetLabelId","validateStatus","type","readonly","size","suffix","style","showClear","onEnterPress","onClear","hideSuffix","inputStyle","forwardRef","maxLength","getValueLength","preventScroll","borderless","showClearIgnoreDisabled","onlyBorder"]),{value:J,isFocus:V,minLength:U}=this.state,G=this.foundation.isAllowClear(),we=Me(_),Ce=this.getInputRef(),X=`${k}-wrapper`,oe=E(X,i,{[`${k}-wrapper__with-prefix`]:h||m,[`${k}-wrapper__with-suffix`]:_,[`${k}-wrapper__with-suffix-hidden`]:G&&!!w,[`${k}-wrapper__with-suffix-icon`]:we,[`${k}-wrapper__with-append`]:n,[`${k}-wrapper__with-prepend`]:t,[`${k}-wrapper__with-append-only`]:n&&!t,[`${k}-wrapper__with-prepend-only`]:!n&&t,[`${X}-readonly`]:g,[`${X}-disabled`]:l,[`${X}-warning`]:v==="warning",[`${X}-error`]:v==="error",[`${X}-focus`]:V,[`${X}-clearable`]:T,[`${X}-modebtn`]:f==="password",[`${X}-hidden`]:p==="hidden",[`${X}-${b}`]:b,[`${k}-borderless`]:K,[`${k}-only_border`]:q!=null}),ue=E(k,{[`${k}-${b}`]:b,[`${k}-disabled`]:l,[`${k}-sibling-clearbtn`]:this.foundation.isAllowClear(),[`${k}-sibling-modebtn`]:f==="password"}),he=J??"",ne=Object.assign(Object.assign({},se),{style:P,className:ue,disabled:l,readOnly:g,type:this.foundation.handleInputType(p),placeholder:u,onInput:L=>this.foundation.handleInput(L),onChange:L=>this.foundation.handleChange(L.target.value,L),onFocus:L=>this.foundation.handleFocus(L),onBlur:L=>this.foundation.handleBlur(L),onKeyUp:L=>this.foundation.handleKeyUp(L),onKeyDown:L=>this.foundation.handleKeyDown(L),onKeyPress:L=>this.foundation.handleKeyPress(L),value:he});fe(j)||(ne.maxLength=M),U&&(ne.minLength=U),v==="error"&&(ne["aria-invalid"]="true");let pe=Object.assign({},O);return q!==void 0&&(pe=Object.assign({borderWidth:q},O)),d.createElement("div",{className:oe,style:pe,onMouseEnter:L=>this.handleMouseOver(L),onMouseLeave:L=>this.handleMouseLeave(L),onClick:L=>this.handleClick(L)},this.renderPrepend(),this.renderPrefix(),d.createElement("input",Object.assign({},ne,{ref:Ce})),this.renderClearBtn(),this.renderSuffix(G),this.renderModeBtn(),this.renderAppend())}}Gr.propTypes={"aria-label":a.string,"aria-labelledby":a.string,"aria-invalid":a.bool,"aria-errormessage":a.string,"aria-describedby":a.string,"aria-required":a.bool,addonBefore:a.node,addonAfter:a.node,clearIcon:a.node,prefix:a.node,suffix:a.node,mode:a.oneOf(MC),value:a.any,defaultValue:a.any,disabled:a.bool,readonly:a.bool,autoFocus:a.bool,type:a.string,showClear:a.bool,hideSuffix:a.bool,placeholder:a.any,size:a.oneOf(NC),className:a.string,style:a.object,validateStatus:a.oneOf(jC),onClear:a.func,onChange:a.func,onBlur:a.func,onFocus:a.func,onInput:a.func,onKeyDown:a.func,onKeyUp:a.func,onKeyPress:a.func,onEnterPress:a.func,insetLabel:a.node,insetLabelId:a.string,inputStyle:a.object,getValueLength:a.func,preventScroll:a.bool,borderless:a.bool};Gr.defaultProps={addonBefore:"",addonAfter:"",prefix:"",suffix:"",readonly:!1,type:"text",showClear:!1,hideSuffix:!1,placeholder:"",size:"default",className:"",onClear:A,onChange:A,onBlur:A,onFocus:A,onInput:A,onKeyDown:A,onKeyUp:A,onKeyPress:A,onEnterPress:A,validateStatus:"default",borderless:!1};const go=d.forwardRef((r,e)=>d.createElement(Gr,Object.assign({},r,{forwardRef:e})));var DC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class Ri extends d.PureComponent{render(){const e=this.props,{triggerRender:t,componentName:n}=e,s=DC(e,["triggerRender","componentName"]);return t(Object.assign({},s))}}Ri.propTypes={triggerRender:a.func.isRequired,componentName:a.string,componentProps:a.object,value:a.any,inputValue:a.string,placeholder:a.oneOfType([a.string,a.array]),className:a.string,style:a.object};const RC={PREFIX:`${D}-highlight`},LC=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");class kC extends ye{constructor(e){super(Object.assign({},e)),this.findAll=t=>{let{autoEscape:n=!0,caseSensitive:s=!1,searchWords:o,sourceString:i}=t;ee(o)&&(o=[o]);const l=this.findChunks({autoEscape:n,caseSensitive:s,searchWords:o,sourceString:i}),c=this.combineChunks({chunks:l});return this.fillInChunks({chunksToHighlight:c,totalLength:i?i.length:0})},this.findChunks=t=>{let{autoEscape:n,caseSensitive:s,searchWords:o,sourceString:i}=t;return o.map(l=>typeof l=="string"?{text:l}:l).filter(l=>l.text).reduce((l,c)=>{let u=c.text;n&&(u=LC(u));const h=new RegExp(u,s?"g":"gi");let f;for(;f=h.exec(i);){const m=f.index,y=h.lastIndex;y>m&&l.push({highlight:!0,start:m,end:y,className:c.className,style:c.style}),f.index===h.lastIndex&&h.lastIndex++}return l},[])},this.combineChunks=t=>{let{chunks:n}=t;return n.sort((s,o)=>s.start-o.start).reduce((s,o)=>{if(s.length===0)return[o];{const i=s.pop();if(o.start<=i.end){const l=Math.max(i.end,o.end);s.push({highlight:!0,start:i.start,end:l,className:i.className||o.className,style:Object.assign(Object.assign({},i.style),o.style)})}else s.push(i,o);return s}},[])},this.fillInChunks=t=>{let{chunksToHighlight:n,totalLength:s}=t;const o=[],i=(l,c,u,h,f)=>{c-l>0&&o.push({start:l,end:c,highlight:u,className:h,style:f})};if(n.length===0)i(0,s,!1);else{let l=0;n.forEach(c=>{i(l,c.start,!1),i(c.start,c.end,!0,c.className,c.style),l=c.end}),i(l,s,!1)}return o}}}const FC=RC.PREFIX;class Xr extends $.PureComponent{constructor(){super(...arguments),this.getHighLightTextHTML=e=>{let{sourceString:t="",searchWords:n=[],option:s={autoEscape:!0,caseSensitive:!1}}=e;const o=new kC().findAll(Object.assign({sourceString:t,searchWords:n},s)),i=s.highlightTag||"mark",l=s.highlightClassName||"",c=s.highlightStyle||{};return o.map((u,h)=>{const{end:f,start:m,highlight:y,style:v,className:p}=u,g=t.substr(m,f-m);return y?d.createElement(i,{style:Object.assign(Object.assign({},c),v),className:`${l} ${p||""}`.trim(),key:g+h},g):g})}}render(){const{searchWords:e,sourceString:t,component:n,highlightClassName:s,highlightStyle:o,caseSensitive:i,autoEscape:l}=this.props,c=E({[`${FC}-tag`]:!0},s),u={highlightTag:n,highlightClassName:c,highlightStyle:o,caseSensitive:i,autoEscape:l};return this.getHighLightTextHTML({sourceString:t,searchWords:e,option:u})}}Xr.propTypes={style:a.object,className:a.string,autoEscape:a.bool,caseSensitive:a.bool,sourceString:a.string,searchWords:a.arrayOf(a.string),highlightStyle:a.object,highlightClassName:a.string,component:a.string};Xr.defaultProps={component:"mark",autoEscape:!0,caseSensitive:!1,sourceString:""};const BC={PREFIX:`${D}-avatar`},$n={SHAPE:["circle","square"],SIZE:["extra-extra-small","extra-small","small","default","medium","large","extra-large"],COLOR:["grey","red","pink","purple","violet","indigo","blue","light-blue","cyan","teal","green","light-green","lime","yellow","amber","orange","white"]};class KC extends ye{constructor(e){super(Object.assign({},e)),this.handleFocusVisible=t=>{const{target:n}=t;try{n.matches(":focus-visible")&&this._adapter.setFocusVisible(!0)}catch{ot(!0,"Warning: [Semi Avatar] The current browser does not support the focus-visible")}},this.handleBlur=()=>{this._adapter.setFocusVisible(!1)},this.changeScale=()=>{const{gap:t}=this.getProps(),n=this._adapter.getAvatarNode(),s=n==null?void 0:n.firstChild,[o,i]=[(n==null?void 0:n.offsetWidth)||0,(s==null?void 0:s.offsetWidth)||0];if(o!==0&&i!==0&&t*2<o){const l=o-t*2>i?1:(o-t*2)/i;this._adapter.setScale(l)}}}init(){const{children:e}=this.getProps();typeof e=="string"&&this.changeScale()}destroy(){}handleImgLoadError(){const{onError:e}=this.getProps();(e?e():void 0)!==!1&&this._adapter.notifyImgState(!1)}handleEnter(e){this._adapter.notifyEnter(e)}handleLeave(e){this._adapter.notifyLeave(e)}}const WC=r=>{let{gradientStart:e,gradientEnd:t}=r;const n=ln();return d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"51",height:"52",viewBox:"0 0 51 52",fill:"none"},d.createElement("g",{filter:"url(#filter0_d_6_2)"},d.createElement("path",{d:"M40.4918 46.5592C44.6795 43.176 46.261 34.1333 47.5301 25.6141C49.5854 11.8168 39.6662 1 25.8097 1C11.2857 1 3 11.4279 3 25.3518C3 33.7866 6.29361 43.8947 10.4602 46.5592C12.5868 47.9192 12.5868 47.9051 25.8097 47.9192C38.3651 47.9282 38.5352 48.14 40.4918 46.5592Z",fill:`url(#${n})`})),d.createElement("defs",null,d.createElement("filter",{id:"filter0_d_6_2",x:"0.789215",y:"0.447304",width:"49.2216",height:"51.3549",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},d.createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),d.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),d.createElement("feOffset",{dy:"1.65809"}),d.createElement("feGaussianBlur",{stdDeviation:"1.10539"}),d.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"}),d.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_6_2"}),d.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_6_2",result:"shape"})),d.createElement("linearGradient",{id:n,x1:"17.671",y1:"31.7392",x2:"17.671",y2:"47.9333",gradientUnits:"userSpaceOnUse"},d.createElement("stop",{stopColor:e}),d.createElement("stop",{offset:"1",stopColor:t}))))};var zC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const HC=$n.SIZE,VC=$n.SHAPE,UC=$n.COLOR,F=BC.PREFIX;class it extends ce{constructor(e){super(e),this.handleFocusVisible=t=>{this.foundation.handleFocusVisible(t)},this.handleBlur=t=>{this.foundation.handleBlur()},this.getContent=()=>{const{children:t,onClick:n,imgAttr:s,src:o,srcSet:i,alt:l}=this.props,{isImgExist:c}=this.state;let u=t;const h=n!==Te,f=o&&c,m={tabIndex:0,onKeyDown:this.handleKeyDown,onFocus:this.handleFocusVisible,onBlur:this.handleBlur};if(f){const y=h?`clickable Avatar: ${l}`:l,v=Object.assign(Object.assign({src:o,srcSet:i,onError:this.handleError},s),{className:E({[`${F}-no-focus-visible`]:h})}),p=h?Object.assign(Object.assign({},v),m):v;u=d.createElement("img",Object.assign({alt:y},p))}else if(typeof t=="string"){const y=l??t,p={role:"img","aria-label":h?`clickable Avatar: ${y}`:y,className:E(`${F}-label`,{[`${F}-no-focus-visible`]:h})},g=h?Object.assign(Object.assign({},p),m):p,b={transform:`scale(${this.state.scale})`};u=d.createElement("span",{className:`${F}-content`,style:b},d.createElement("span",Object.assign({},g,{"x-semi-prop":"children"}),t))}return u},this.renderBottomSlot=()=>{var t,n;if(!this.props.bottomSlot)return null;if(this.props.bottomSlot.render)return this.props.bottomSlot.render();const s=(t=this.props.bottomSlot.render)!==null&&t!==void 0?t:()=>{var o;const i={};return this.props.bottomSlot.bgColor&&(i.backgroundColor=this.props.bottomSlot.bgColor),this.props.bottomSlot.textColor&&(i.color=this.props.bottomSlot.textColor),d.createElement("span",{style:i,className:E(`${F}-bottom_slot-shape_${this.props.bottomSlot.shape}`,`${F}-bottom_slot-shape_${this.props.bottomSlot.shape}-${this.props.size}`,(o=this.props.bottomSlot.className)!==null&&o!==void 0?o:"")},this.props.bottomSlot.text)};return d.createElement("div",{className:E([`${F}-bottom_slot`]),style:(n=this.props.bottomSlot.style)!==null&&n!==void 0?n:{}},s())},this.renderTopSlot=()=>{var t,n,s,o;if(!this.props.topSlot)return null;if(this.props.topSlot.render)return this.props.topSlot.render();const i={};return this.props.topSlot.textColor&&(i.color=this.props.topSlot.textColor),d.createElement("div",{style:(t=this.props.topSlot.style)!==null&&t!==void 0?t:{},className:E([`${F}-top_slot-wrapper`,(n=this.props.topSlot.className)!==null&&n!==void 0?n:"",{[`${F}-animated`]:this.props.contentMotion}])},d.createElement("div",{className:E([`${F}-top_slot-bg`,`${F}-top_slot-bg-${this.props.size}`])},d.createElement("div",{className:E([`${F}-top_slot-bg-svg`,`${F}-top_slot-bg-svg-${this.props.size}`])},d.createElement(WC,{gradientStart:(s=this.props.topSlot.gradientStart)!==null&&s!==void 0?s:"var(--semi-color-primary)",gradientEnd:(o=this.props.topSlot.gradientEnd)!==null&&o!==void 0?o:"var(--semi-color-primary)"}))),d.createElement("div",{className:E([`${F}-top_slot`])},d.createElement("div",{style:i,className:E([`${F}-top_slot-content`,`${F}-top_slot-content-${this.props.size}`])},this.props.topSlot.text)))},this.state={isImgExist:!0,hoverContent:"",focusVisible:!1,scale:1},this.onEnter=this.onEnter.bind(this),this.onLeave=this.onLeave.bind(this),this.handleError=this.handleError.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.getContent=this.getContent.bind(this),this.avatarRef=d.createRef()}get adapter(){return Object.assign(Object.assign({},super.adapter),{notifyImgState:e=>{this.setState({isImgExist:e})},notifyEnter:e=>{const{hoverMask:t}=this.props,n=t;this.setState({hoverContent:n},()=>{const{onMouseEnter:s}=this.props;s&&s(e)})},notifyLeave:e=>{this.setState({hoverContent:""},()=>{const{onMouseLeave:t}=this.props;t&&t(e)})},setFocusVisible:e=>{this.setState({focusVisible:e})},setScale:e=>{this.setState({scale:e})},getAvatarNode:()=>{var e;return(e=this.avatarRef)===null||e===void 0?void 0:e.current}})}componentDidMount(){this.foundation=new KC(this.adapter),this.foundation.init()}componentDidUpdate(e){if(this.props.src&&this.props.src!==e.src){const t=new Image(0,0);t.src=this.props.src,t.onload=()=>{this.setState({isImgExist:!0})},t.onerror=()=>{this.setState({isImgExist:!1})},t.onabort=()=>{this.setState({isImgExist:!1})}}typeof this.props.children=="string"&&this.props.children!==e.children&&this.foundation.changeScale()}componentWillUnmount(){this.foundation.destroy()}onEnter(e){this.foundation.handleEnter(e)}onLeave(e){this.foundation.handleLeave(e)}handleError(){this.foundation.handleImgLoadError()}handleKeyDown(e){const{onClick:t}=this.props;switch(e.key){case"Enter":t(e),ge(e);break;case"Escape":e.target.blur();break}}render(){var e;const t=this.props,{shape:n,children:s,size:o,color:i,className:l,hoverMask:c,onClick:u,imgAttr:h,src:f,srcSet:m,style:y,alt:v,gap:p,bottomSlot:g,topSlot:b,border:_,contentMotion:O}=t,T=zC(t,["shape","children","size","color","className","hoverMask","onClick","imgAttr","src","srcSet","style","alt","gap","bottomSlot","topSlot","border","contentMotion"]),{isImgExist:C,hoverContent:S,focusVisible:w}=this.state;let P={};$n.SIZE.includes(o)||(P={width:o,height:o}),P=Object.assign(Object.assign({},P),y);const x=g||b||_,M={onClick:u,onMouseEnter:this.onEnter,onMouseLeave:this.onLeave},j=f&&C,B=E(F,{[`${F}-${n}`]:n,[`${F}-${o}`]:o,[`${F}-${i}`]:i&&!j,[`${F}-img`]:j,[`${F}-focus`]:w,[`${F}-animated`]:O},l),K=S?d.createElement("div",{className:`${F}-hover`,"x-semi-prop":"hoverContent"},S):null;let Z=d.createElement("span",Object.assign({},T,{style:x?{}:P,className:B},x?{}:M,{role:"listitem",ref:this.avatarRef}),this.getContent(),K);if(_){const q={};typeof _=="object"&&(_!=null&&_.color)&&(q.borderColor=_==null?void 0:_.color),Z=d.createElement("div",{style:Object.assign({position:"relative"},P)},Z,d.createElement("span",{style:q,className:E([`${F}-additionalBorder`,`${F}-additionalBorder-${o}`,{[`${F}-${n}`]:n}])}),typeof this.props.border=="object"&&this.props.border.motion&&d.createElement("span",{style:q,className:E([`${F}-additionalBorder`,`${F}-additionalBorder-${o}`,{[`${F}-${n}`]:n,[`${F}-additionalBorder-animated`]:typeof this.props.border=="object"&&((e=this.props.border)===null||e===void 0?void 0:e.motion)}])}))}return x?d.createElement("span",Object.assign({className:E([`${F}-wrapper`]),style:P},M),Z,b&&["extra-small","small","default","medium","large","extra-large"].includes(o)&&n==="circle"&&this.renderTopSlot(),g&&["extra-small","small","default","medium","large","extra-large"].includes(o)&&this.renderBottomSlot()):Z}}it.__SemiComponentName__="Avatar";it.defaultProps=Le(it.__SemiComponentName__,{size:"medium",color:"grey",shape:"circle",gap:3,onClick:Te,onMouseEnter:Te,onMouseLeave:Te});it.propTypes={children:a.node,color:a.oneOf(UC),shape:a.oneOf(VC),size:a.oneOf(HC),hoverMask:a.node,className:a.string,style:a.object,gap:a.number,imgAttr:a.object,src:a.string,srcSet:a.string,alt:a.string,onError:a.func,onClick:a.func,onMouseEnter:a.func,onMouseLeave:a.func,bottomSlot:a.shape({render:a.func,shape:a.oneOf(["circle","square"]),text:a.node,bgColor:a.string,textColor:a.string,className:a.string,style:a.object}),topSlot:a.shape({render:a.func,gradientStart:a.string,gradientEnd:a.string,text:a.node,textColor:a.string,className:a.string,style:a.object}),border:a.oneOfType([a.shape({color:a.string,motion:a.bool}),a.bool]),contentMotion:a.bool};it.elementType="Avatar";const Li={PREFIX:`${D}-button`},Ft={sizes:["default","small","large"],iconPositions:["left","right"],htmlTypes:["button","reset","submit"],btnTypes:["primary","secondary","tertiary","warning","danger"],themes:["solid","borderless","light","outline"],DEFAULT_ICON_POSITION:"left"},GC={SIZE:["extra-small","small","default","large","extra-large","custom"]};var XC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const qC=Ft.sizes,{htmlTypes:YC,btnTypes:ZC}=Ft;let Bt=class extends $.PureComponent{render(){const e=this.props,{children:t,block:n,htmlType:s,loading:o,circle:i,className:l,style:c,disabled:u,size:h,theme:f,type:m,prefixCls:y,iconPosition:v}=e,p=XC(e,["children","block","htmlType","loading","circle","className","style","disabled","size","theme","type","prefixCls","iconPosition"]),g=Object.assign(Object.assign({disabled:u},Dt(p,["x-semi-children-alias"])),{className:E(y,{[`${y}-${m}`]:!u&&m,[`${y}-disabled`]:u,[`${y}-size-large`]:h==="large",[`${y}-size-small`]:h==="small",[`${y}-light`]:f==="light",[`${y}-block`]:n,[`${y}-circle`]:i,[`${y}-borderless`]:f==="borderless",[`${y}-outline`]:f==="outline",[`${y}-${m}-disabled`]:u&&m},l),type:s,"aria-disabled":u}),b={};return l&&l.includes("-with-icon")||(b["x-semi-prop"]=this.props["x-semi-children-alias"]||"children"),d.createElement("button",Object.assign({},g,{onClick:this.props.onClick,onMouseDown:this.props.onMouseDown,style:c}),d.createElement("span",Object.assign({className:E(`${y}-content`,this.props.contentClassName),onClick:_=>u&&_.stopPropagation()},b),t))}};Bt.defaultProps={disabled:!1,size:"default",type:"primary",theme:"light",block:!1,htmlType:"button",onMouseDown:Te,onClick:Te,onMouseEnter:Te,onMouseLeave:Te,prefixCls:Li.PREFIX};Bt.propTypes={children:a.node,disabled:a.bool,prefixCls:a.string,style:a.object,size:a.oneOf(qC),type:a.oneOf(ZC),block:a.bool,onClick:a.func,onMouseDown:a.func,circle:a.bool,loading:a.bool,htmlType:a.oneOf(YC),theme:a.oneOf(Ft.themes),className:a.string,onMouseEnter:a.func,onMouseLeave:a.func,"aria-label":a.string,contentClassName:a.string};var JC=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const QC=GC.SIZE;class Kt extends $.PureComponent{render(){const e=this.props,{children:t,iconPosition:n,iconSize:s,iconStyle:o,style:i,icon:l,noHorizontalPadding:c,theme:u,className:h,prefixCls:f,loading:m}=e,y=JC(e,["children","iconPosition","iconSize","iconStyle","style","icon","noHorizontalPadding","theme","className","prefixCls","loading"]),v=Object.assign({},i);Array.isArray(c)?(c.includes("left")&&(v.paddingLeft=0),c.includes("right")&&(v.paddingRight=0)):c===!0?(v.paddingLeft=0,v.paddingRight=0):typeof c=="string"&&(c==="left"&&(v.paddingLeft=0),c==="right"&&(v.paddingRight=0));let p=null,g=null;m&&!y.disabled?g=d.createElement(Di,null):d.isValidElement(l)&&(g=l);const b=E({[`${f}-content-left`]:n==="right",[`${f}-content-right`]:n==="left"}),_=this.props["x-semi-children-alias"]||"children",O=t!=null?d.createElement("span",{className:b,"x-semi-prop":_},t):null;n==="left"?p=d.createElement(d.Fragment,null,g,O):p=d.createElement(d.Fragment,null,O,g);const T=E(h,`${f}-with-icon`,{[`${f}-with-icon-only`]:O==null||O==="",[`${f}-loading`]:m});return d.createElement(Bt,Object.assign({},y,{className:T,theme:u,style:v}),p)}}Kt.defaultProps={iconPosition:Ft.DEFAULT_ICON_POSITION,prefixCls:Li.PREFIX,loading:!1,noHorizontalPadding:!1,onMouseEnter:A,onMouseLeave:A};Kt.elementType="IconButton";Kt.propTypes={iconStyle:a.object,style:a.object,loading:a.bool,prefixCls:a.string,icon:a.oneOfType([a.object,a.string,a.node]),iconSize:a.oneOf(QC),noHorizontalPadding:a.oneOfType([a.bool,a.string,a.array]),children:a.node,theme:a.string,iconPosition:a.oneOf(Ft.iconPositions),className:a.string,onMouseEnter:a.func,onMouseLeave:a.func};class qe extends d.PureComponent{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(e)}render(){const e=Object.assign({},this.props),t=!!e.icon,n=!!e.loading,s=!!e.disabled;return t||n&&!s?d.createElement(Kt,Object.assign({},e)):d.createElement(Bt,Object.assign({},e))}}qe.__SemiComponentName__="Button";qe.propTypes=Object.assign(Object.assign({},Bt.propTypes),Kt.propTypes);qe.defaultProps=Le(qe.__SemiComponentName__);qe.elementType="Button";const eT={PREFIX:`${D}-space`},ze={ALIGN_SET:["start","end","center","baseline"],SPACING_LOOSE:"loose",SPACING_MEDIUM:"medium",SPACING_TIGHT:"tight"},tT="Symbol(react.fragment)",pr=r=>{let e=[];return d.Children.forEach(r,t=>{t!=null&&(Array.isArray(t)?e=e.concat(pr(t)):$.isValidElement(t)&&t.type&&t.type.toString()===tT&&t.props?e=e.concat(pr(t.props.children)):e.push(t))}),e},be=eT.PREFIX;class qr extends $.PureComponent{render(){const{children:e=null,style:t,className:n,spacing:s,wrap:o,align:i,vertical:l}=this.props,c=o&&l?!1:o,u=Object.assign({},t);let h="",f="";ee(s)?(h=s,f=s):Ve(s)?(u.rowGap=s,u.columnGap=s):_a(s)&&(ee(s[0])?h=s[0]:Ve(s[0])&&(u.columnGap=`${s[0]}px`),ee(s[1])?f=s[1]:Ve(s[1])&&(u.rowGap=`${s[1]}px`));const m=E(be,n,{[`${be}-align-${i}`]:i,[`${be}-vertical`]:l,[`${be}-horizontal`]:!l,[`${be}-wrap`]:c,[`${be}-tight-horizontal`]:h===ze.SPACING_TIGHT,[`${be}-tight-vertical`]:f===ze.SPACING_TIGHT,[`${be}-medium-horizontal`]:h===ze.SPACING_MEDIUM,[`${be}-medium-vertical`]:f===ze.SPACING_MEDIUM,[`${be}-loose-horizontal`]:h===ze.SPACING_LOOSE,[`${be}-loose-vertical`]:f===ze.SPACING_LOOSE}),y=pr(e),v=xt(this.props);return d.createElement("div",Object.assign({},v,{className:m,style:u,"x-semi-prop":"children"}),y)}}qr.propTypes={wrap:a.bool,align:a.oneOf(ze.ALIGN_SET),vertical:a.bool,spacing:a.oneOfType([a.string,a.number,a.array]),children:a.node,style:a.object,className:a.string};qr.defaultProps={vertical:!1,wrap:!1,spacing:"tight",align:"center"};var nT=Sr,rT=wi,sT=ft;function oT(r,e,t){for(var n=-1,s=e.length,o={};++n<s;){var i=e[n],l=nT(r,i);t(l,i)&&rT(o,sT(i,r),l)}return o}var iT=oT;function aT(r,e){return r!=null&&e in Object(r)}var lT=aT,cT=ft,uT=At,dT=le,hT=wn,pT=$r,fT=_n;function gT(r,e,t){e=cT(e,r);for(var n=-1,s=e.length,o=!1;++n<s;){var i=fT(e[n]);if(!(o=r!=null&&t(r,i)))break;r=r[i]}return o||++n!=s?o:(s=r==null?0:r.length,!!s&&pT(s)&&hT(i,s)&&(dT(r)||uT(r)))}var mT=gT,yT=lT,bT=mT;function vT(r,e){return r!=null&&bT(r,e,yT)}var _T=vT,ST=iT,OT=_T;function wT(r,e){return ST(r,e,function(t,n){return OT(r,n)})}var CT=wT,TT=CT,ET=vi,PT=ET(function(r,e){return r==null?{}:TT(r,e)}),$T=PT;const fr=Y($T);function gr(){return gr=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)({}).hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r},gr.apply(null,arguments)}function IT(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r,dr(r,e)}var mo=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function xT(r,e){return!!(r===e||mo(r)&&mo(e))}function AT(r,e){if(r.length!==e.length)return!1;for(var t=0;t<r.length;t++)if(!xT(r[t],e[t]))return!1;return!0}function Qn(r,e){e===void 0&&(e=AT);var t,n=[],s,o=!1;function i(){for(var l=[],c=0;c<arguments.length;c++)l[c]=arguments[c];return o&&t===this&&e(l,n)||(s=r.apply(this,l),o=!0,t=this,n=l),s}return i}var NT=typeof performance=="object"&&typeof performance.now=="function",yo=NT?function(){return performance.now()}:function(){return Date.now()};function bo(r){cancelAnimationFrame(r.id)}function jT(r,e){var t=yo();function n(){yo()-t>=e?r.call(null):s.id=requestAnimationFrame(n)}var s={id:requestAnimationFrame(n)};return s}var er=-1;function vo(r){if(r===void 0&&(r=!1),er===-1||r){var e=document.createElement("div"),t=e.style;t.width="50px",t.height="50px",t.overflow="scroll",document.body.appendChild(e),er=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return er}var tt=null;function _o(r){if(r===void 0&&(r=!1),tt===null||r){var e=document.createElement("div"),t=e.style;t.width="50px",t.height="50px",t.overflow="scroll",t.direction="rtl";var n=document.createElement("div"),s=n.style;return s.width="100px",s.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?tt="positive-descending":(e.scrollLeft=1,e.scrollLeft===0?tt="negative":tt="positive-ascending"),document.body.removeChild(e),tt}return tt}var MT=150,DT=function(e,t){return e};function RT(r){var e,t=r.getItemOffset,n=r.getEstimatedTotalSize,s=r.getItemSize,o=r.getOffsetForIndexAndAlignment,i=r.getStartIndexForOffset,l=r.getStopIndexForStartIndex,c=r.initInstanceProps,u=r.shouldResetStyleCacheOnItemSizeChange,h=r.validateProps;return e=function(f){IT(m,f);function m(v){var p;return p=f.call(this,v)||this,p._instanceProps=c(p.props,ho(p)),p._outerRef=void 0,p._resetIsScrollingTimeoutId=null,p.state={instance:ho(p),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof p.props.initialScrollOffset=="number"?p.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},p._callOnItemsRendered=void 0,p._callOnItemsRendered=Qn(function(g,b,_,O){return p.props.onItemsRendered({overscanStartIndex:g,overscanStopIndex:b,visibleStartIndex:_,visibleStopIndex:O})}),p._callOnScroll=void 0,p._callOnScroll=Qn(function(g,b,_){return p.props.onScroll({scrollDirection:g,scrollOffset:b,scrollUpdateWasRequested:_})}),p._getItemStyle=void 0,p._getItemStyle=function(g){var b=p.props,_=b.direction,O=b.itemSize,T=b.layout,C=p._getItemStyleCache(u&&O,u&&T,u&&_),S;if(C.hasOwnProperty(g))S=C[g];else{var w=t(p.props,g,p._instanceProps),P=s(p.props,g,p._instanceProps),x=_==="horizontal"||T==="horizontal",M=_==="rtl",j=x?w:0;C[g]=S={position:"absolute",left:M?void 0:j,right:M?j:void 0,top:x?0:w,height:x?"100%":P,width:x?P:"100%"}}return S},p._getItemStyleCache=void 0,p._getItemStyleCache=Qn(function(g,b,_){return{}}),p._onScrollHorizontal=function(g){var b=g.currentTarget,_=b.clientWidth,O=b.scrollLeft,T=b.scrollWidth;p.setState(function(C){if(C.scrollOffset===O)return null;var S=p.props.direction,w=O;if(S==="rtl")switch(_o()){case"negative":w=-O;break;case"positive-descending":w=T-_-O;break}return w=Math.max(0,Math.min(w,T-_)),{isScrolling:!0,scrollDirection:C.scrollOffset<w?"forward":"backward",scrollOffset:w,scrollUpdateWasRequested:!1}},p._resetIsScrollingDebounced)},p._onScrollVertical=function(g){var b=g.currentTarget,_=b.clientHeight,O=b.scrollHeight,T=b.scrollTop;p.setState(function(C){if(C.scrollOffset===T)return null;var S=Math.max(0,Math.min(T,O-_));return{isScrolling:!0,scrollDirection:C.scrollOffset<S?"forward":"backward",scrollOffset:S,scrollUpdateWasRequested:!1}},p._resetIsScrollingDebounced)},p._outerRefSetter=function(g){var b=p.props.outerRef;p._outerRef=g,typeof b=="function"?b(g):b!=null&&typeof b=="object"&&b.hasOwnProperty("current")&&(b.current=g)},p._resetIsScrollingDebounced=function(){p._resetIsScrollingTimeoutId!==null&&bo(p._resetIsScrollingTimeoutId),p._resetIsScrollingTimeoutId=jT(p._resetIsScrolling,MT)},p._resetIsScrolling=function(){p._resetIsScrollingTimeoutId=null,p.setState({isScrolling:!1},function(){p._getItemStyleCache(-1,null)})},p}m.getDerivedStateFromProps=function(p,g){return LT(p,g),h(p),null};var y=m.prototype;return y.scrollTo=function(p){p=Math.max(0,p),this.setState(function(g){return g.scrollOffset===p?null:{scrollDirection:g.scrollOffset<p?"forward":"backward",scrollOffset:p,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},y.scrollToItem=function(p,g){g===void 0&&(g="auto");var b=this.props,_=b.itemCount,O=b.layout,T=this.state.scrollOffset;p=Math.max(0,Math.min(p,_-1));var C=0;if(this._outerRef){var S=this._outerRef;O==="vertical"?C=S.scrollWidth>S.clientWidth?vo():0:C=S.scrollHeight>S.clientHeight?vo():0}this.scrollTo(o(this.props,p,g,T,this._instanceProps,C))},y.componentDidMount=function(){var p=this.props,g=p.direction,b=p.initialScrollOffset,_=p.layout;if(typeof b=="number"&&this._outerRef!=null){var O=this._outerRef;g==="horizontal"||_==="horizontal"?O.scrollLeft=b:O.scrollTop=b}this._callPropsCallbacks()},y.componentDidUpdate=function(){var p=this.props,g=p.direction,b=p.layout,_=this.state,O=_.scrollOffset,T=_.scrollUpdateWasRequested;if(T&&this._outerRef!=null){var C=this._outerRef;if(g==="horizontal"||b==="horizontal")if(g==="rtl")switch(_o()){case"negative":C.scrollLeft=-O;break;case"positive-ascending":C.scrollLeft=O;break;default:var S=C.clientWidth,w=C.scrollWidth;C.scrollLeft=w-S-O;break}else C.scrollLeft=O;else C.scrollTop=O}this._callPropsCallbacks()},y.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&bo(this._resetIsScrollingTimeoutId)},y.render=function(){var p=this.props,g=p.children,b=p.className,_=p.direction,O=p.height,T=p.innerRef,C=p.innerElementType,S=p.innerTagName,w=p.itemCount,P=p.itemData,x=p.itemKey,M=x===void 0?DT:x,j=p.layout,B=p.outerElementType,K=p.outerTagName,Z=p.style,q=p.useIsScrolling,se=p.width,J=this.state.isScrolling,V=_==="horizontal"||j==="horizontal",U=V?this._onScrollHorizontal:this._onScrollVertical,G=this._getRangeToRender(),we=G[0],Ce=G[1],X=[];if(w>0)for(var oe=we;oe<=Ce;oe++)X.push($.createElement(g,{data:P,key:M(oe,P),index:oe,isScrolling:q?J:void 0,style:this._getItemStyle(oe)}));var ue=n(this.props,this._instanceProps);return $.createElement(B||K||"div",{className:b,onScroll:U,ref:this._outerRefSetter,style:gr({position:"relative",height:O,width:se,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:_},Z)},$.createElement(C||S||"div",{children:X,ref:T,style:{height:V?"100%":ue,pointerEvents:J?"none":void 0,width:V?ue:"100%"}}))},y._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var p=this.props.itemCount;if(p>0){var g=this._getRangeToRender(),b=g[0],_=g[1],O=g[2],T=g[3];this._callOnItemsRendered(b,_,O,T)}}if(typeof this.props.onScroll=="function"){var C=this.state,S=C.scrollDirection,w=C.scrollOffset,P=C.scrollUpdateWasRequested;this._callOnScroll(S,w,P)}},y._getRangeToRender=function(){var p=this.props,g=p.itemCount,b=p.overscanCount,_=this.state,O=_.isScrolling,T=_.scrollDirection,C=_.scrollOffset;if(g===0)return[0,0,0,0];var S=i(this.props,C,this._instanceProps),w=l(this.props,S,C,this._instanceProps),P=!O||T==="backward"?Math.max(1,b):1,x=!O||T==="forward"?Math.max(1,b):1;return[Math.max(0,S-P),Math.max(0,Math.min(g-1,w+x)),S,w]},m}($.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},e}var LT=function(e,t){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,t.instance},kT=RT({getItemOffset:function(e,t){var n=e.itemSize;return t*n},getItemSize:function(e,t){var n=e.itemSize;return n},getEstimatedTotalSize:function(e){var t=e.itemCount,n=e.itemSize;return n*t},getOffsetForIndexAndAlignment:function(e,t,n,s,o,i){var l=e.direction,c=e.height,u=e.itemCount,h=e.itemSize,f=e.layout,m=e.width,y=l==="horizontal"||f==="horizontal",v=y?m:c,p=Math.max(0,u*h-v),g=Math.min(p,t*h),b=Math.max(0,t*h-v+h+i);switch(n==="smart"&&(s>=b-v&&s<=g+v?n="auto":n="center"),n){case"start":return g;case"end":return b;case"center":{var _=Math.round(b+(g-b)/2);return _<Math.ceil(v/2)?0:_>p+Math.floor(v/2)?p:_}case"auto":default:return s>=b&&s<=g?s:s<b?b:g}},getStartIndexForOffset:function(e,t){var n=e.itemCount,s=e.itemSize;return Math.max(0,Math.min(n-1,Math.floor(t/s)))},getStopIndexForStartIndex:function(e,t,n){var s=e.direction,o=e.height,i=e.itemCount,l=e.itemSize,c=e.layout,u=e.width,h=s==="horizontal"||c==="horizontal",f=t*l,m=h?u:o,y=Math.ceil((m+n-f)/l);return Math.max(0,Math.min(i-1,t+y-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){e.itemSize}});const ki={PREFIX:`${D}-tag`},vt={TAG_SIZE:["default","small","large"],TAG_COLOR:["grey","red","pink","purple","violet","indigo","blue","light-blue","cyan","teal","green","light-green","lime","yellow","amber","orange","white"],TAG_TYPE:["light","solid","ghost"],AVATAR_SHAPE:["square","circle"]};var FT=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const re=ki.PREFIX,Fi=vt.TAG_COLOR,Bi=vt.TAG_SIZE,Ki=vt.TAG_TYPE,BT=vt.AVATAR_SHAPE;class Pe extends $.Component{constructor(e){super(e),this.state={visible:!0},this.close=this.close.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this)}static getDerivedStateFromProps(e){return"visible"in e?{visible:e.visible}:null}setVisible(e){"visible"in this.props||this.setState({visible:e})}close(e,t,n){const{onClose:s}=this.props;e.stopPropagation(),e.nativeEvent.stopImmediatePropagation(),s&&s(t,e,n),!e.defaultPrevented&&this.setVisible(!1)}handleKeyDown(e){const{closable:t,onClick:n,onKeyDown:s}=this.props;switch(e.key){case"Backspace":case"Delete":t&&this.close(e,this.props.children,this.props.tagKey),ge(e);break;case"Enter":n(e),ge(e);break;case"Escape":e.target.blur();break}s&&s(e)}renderAvatar(){const{avatarShape:e,avatarSrc:t}=this.props;return d.createElement(it,{src:t,shape:e})}render(){const e=this.props,{tagKey:t,children:n,size:s,color:o,closable:i,visible:l,onClose:c,onClick:u,className:h,type:f,shape:m,avatarSrc:y,avatarShape:v,tabIndex:p,prefixIcon:g,suffixIcon:b}=e,_=FT(e,["tagKey","children","size","color","closable","visible","onClose","onClick","className","type","shape","avatarSrc","avatarShape","tabIndex","prefixIcon","suffixIcon"]),{visible:O}=this.state,T=u!==Pe.defaultProps.onClick||i,C={role:"button",tabIndex:p||0,onKeyDown:this.handleKeyDown},S=Object.assign(Object.assign({},_),{onClick:u,tabIndex:p,className:E(re,{[`${re}-default`]:s==="default",[`${re}-small`]:s==="small",[`${re}-large`]:s==="large",[`${re}-square`]:m==="square",[`${re}-circle`]:m==="circle",[`${re}-${f}`]:f,[`${re}-${o}-${f}`]:o&&f,[`${re}-closable`]:i,[`${re}-invisible`]:!O,[`${re}-avatar-${v}`]:y},h)}),w=T?Object.assign(Object.assign({},S),C):S,P=i?d.createElement("div",{className:`${re}-close`,onClick:j=>this.close(j,n,t)},d.createElement(Ai,{size:"small"})):null,x=ee(n),M=E(`${re}-content`,`${re}-content-${x?"ellipsis":"center"}`);return d.createElement("div",Object.assign({"aria-label":this.props["aria-label"]||x?`${i?"Closable ":""}Tag: ${n}`:""},w),g?d.createElement("div",{className:`${re}-prefix-icon`},g):null,y?this.renderAvatar():null,d.createElement("div",{className:M},n),b?d.createElement("div",{className:`${re}-suffix-icon`},b):null,P)}}Pe.defaultProps={size:Bi[0],color:Fi[0],closable:!1,type:Ki[0],onClose:()=>{},onClick:()=>{},onMouseEnter:()=>{},style:{},className:"",shape:"square",avatarShape:"square",prefixIcon:null,suffixIcon:null};Pe.propTypes={children:a.node,tagKey:a.oneOfType([a.string,a.number]),size:a.oneOf(Bi),color:a.oneOf(Fi),type:a.oneOf(Ki),closable:a.bool,visible:a.bool,onClose:a.func,onClick:a.func,prefixIcon:a.node,suffixIcon:a.node,style:a.object,className:a.string,avatarSrc:a.string,avatarShape:a.oneOf(BT),"aria-label":a.string};var KT=Function.prototype.toString,tr=Object.create,WT=Object.prototype.toString,zT=function(){function r(){this._keys=[],this._values=[]}return r.prototype.has=function(e){return!!~this._keys.indexOf(e)},r.prototype.get=function(e){return this._values[this._keys.indexOf(e)]},r.prototype.set=function(e,t){this._keys.push(e),this._values.push(t)},r}();function HT(){return new zT}function VT(){return new WeakMap}var UT=typeof WeakMap<"u"?VT:HT;function Yr(r){if(!r)return tr(null);var e=r.constructor;if(e===Object)return r===Object.prototype?{}:tr(r);if(e&&~KT.call(e).indexOf("[native code]"))try{return new e}catch{}return tr(r)}function GT(r){var e="";return r.global&&(e+="g"),r.ignoreCase&&(e+="i"),r.multiline&&(e+="m"),r.unicode&&(e+="u"),r.sticky&&(e+="y"),e}function XT(r){return r.flags}var qT=/test/g.flags==="g"?XT:GT;function Wi(r){var e=WT.call(r);return e.substring(8,e.length-1)}function YT(r){return r[Symbol.toStringTag]||Wi(r)}var ZT=typeof Symbol<"u"?YT:Wi,JT=Object.defineProperty,QT=Object.getOwnPropertyDescriptor,zi=Object.getOwnPropertyNames,Zr=Object.getOwnPropertySymbols,Hi=Object.prototype,Vi=Hi.hasOwnProperty,e1=Hi.propertyIsEnumerable,Ui=typeof Zr=="function";function t1(r){return zi(r).concat(Zr(r))}var n1=Ui?t1:zi;function In(r,e,t){for(var n=n1(r),s=0,o=n.length,i=void 0,l=void 0;s<o;++s)if(i=n[s],!(i==="callee"||i==="caller")){if(l=QT(r,i),!l){e[i]=t.copier(r[i],t);continue}!l.get&&!l.set&&(l.value=t.copier(l.value,t));try{JT(e,i,l)}catch{e[i]=l.value}}return e}function r1(r,e){var t=new e.Constructor;e.cache.set(r,t);for(var n=0,s=r.length;n<s;++n)t[n]=e.copier(r[n],e);return t}function s1(r,e){var t=new e.Constructor;return e.cache.set(r,t),In(r,t,e)}function Gi(r,e){return r.slice(0)}function o1(r,e){return r.slice(0,r.size,r.type)}function i1(r,e){return new e.Constructor(Gi(r.buffer))}function a1(r,e){return new e.Constructor(r.getTime())}function Xi(r,e){var t=new e.Constructor;return e.cache.set(r,t),r.forEach(function(n,s){t.set(s,e.copier(n,e))}),t}function l1(r,e){return In(r,Xi(r,e),e)}function c1(r,e){var t=Yr(e.prototype);e.cache.set(r,t);for(var n in r)Vi.call(r,n)&&(t[n]=e.copier(r[n],e));return t}function u1(r,e){var t=Yr(e.prototype);e.cache.set(r,t);for(var n in r)Vi.call(r,n)&&(t[n]=e.copier(r[n],e));for(var s=Zr(r),o=0,i=s.length,l=void 0;o<i;++o)l=s[o],e1.call(r,l)&&(t[l]=e.copier(r[l],e));return t}var d1=Ui?u1:c1;function h1(r,e){var t=Yr(e.prototype);return e.cache.set(r,t),In(r,t,e)}function nr(r,e){return new e.Constructor(r.valueOf())}function p1(r,e){var t=new e.Constructor(r.source,qT(r));return t.lastIndex=r.lastIndex,t}function nn(r,e){return r}function qi(r,e){var t=new e.Constructor;return e.cache.set(r,t),r.forEach(function(n){t.add(e.copier(n,e))}),t}function f1(r,e){return In(r,qi(r,e),e)}var g1=Array.isArray,Jr=Object.assign,m1=Object.getPrototypeOf||function(r){return r.__proto__},Yi={array:r1,arrayBuffer:Gi,blob:o1,dataView:i1,date:a1,error:nn,map:Xi,object:d1,regExp:p1,set:qi},y1=Jr({},Yi,{array:s1,map:l1,object:h1,set:f1});function b1(r){return{Arguments:r.object,Array:r.array,ArrayBuffer:r.arrayBuffer,Blob:r.blob,Boolean:nr,DataView:r.dataView,Date:r.date,Error:r.error,Float32Array:r.arrayBuffer,Float64Array:r.arrayBuffer,Int8Array:r.arrayBuffer,Int16Array:r.arrayBuffer,Int32Array:r.arrayBuffer,Map:r.map,Number:nr,Object:r.object,Promise:nn,RegExp:r.regExp,Set:r.set,String:nr,WeakMap:nn,WeakSet:nn,Uint8Array:r.arrayBuffer,Uint8ClampedArray:r.arrayBuffer,Uint16Array:r.arrayBuffer,Uint32Array:r.arrayBuffer,Uint64Array:r.arrayBuffer}}function Zi(r){var e=Jr({},Yi,r),t=b1(e),n=t.Array,s=t.Object;function o(i,l){if(l.prototype=l.Constructor=void 0,!i||typeof i!="object")return i;if(l.cache.has(i))return l.cache.get(i);if(l.prototype=m1(i),l.Constructor=l.prototype&&l.prototype.constructor,!l.Constructor||l.Constructor===Object)return s(i,l);if(g1(i))return n(i,l);var c=t[ZT(i)];return c?c(i,l):typeof i.then=="function"?i:s(i,l)}return function(l){return o(l,{Constructor:void 0,cache:UT(),copier:o,prototype:void 0})}}function v1(r){return Zi(Jr({},y1,r))}v1({});var dn=Zi({});const Wt={PREFIX:`${D}-dropdown`},xn={POSITION_SET:He.POSITION_SET,TRIGGER_SET:["hover","focus","click","custom","contextMenu"],DEFAULT_LEAVE_DELAY:100,ITEM_TYPE:["primary","secondary","tertiary","warning","danger"]},So={SPACING:4,NESTED_SPACING:2};class _1 extends ye{handleVisibleChange(e){this._adapter.setPopVisible(e),this._adapter.notifyVisibleChange(e);const{trigger:t}=this.getProps();if(e&&t==="click"){const n=this._adapter.getPopupId();this.setFocusToFirstMenuItem(n)}}getMenuItemNodes(e){const t=document.getElementById(e);return t?Array.from(t.getElementsByTagName("li")).filter(n=>n.ariaDisabled==="false"):null}setFocusToFirstMenuItem(e){const t=this.getMenuItemNodes(e);t&&ES(t)}setFocusToLastMenuItem(e){const t=this.getMenuItemNodes(e);t&&PS(t)}handleKeyDown(e){var t,n;const s=(n=(t=e.target)===null||t===void 0?void 0:t.attributes["data-popupid"])===null||n===void 0?void 0:n.value,{visible:o}=this._adapter.getStates();switch(e.key){case" ":case"Enter":e.target.click();break;case"ArrowDown":this.setFocusToFirstMenuItem(s),o&&ge(e);break;case"ArrowUp":this.setFocusToLastMenuItem(s),o&&ge(e);break}}}class S1 extends ye{constructor(){super(...arguments),this.menuItemNodes=null,this.firstChars=[]}handleEscape(e){this._adapter.getContext("trigger")==="custom"&&(e&&NS(document.querySelectorAll("[data-popupid]"),e.id)).focus()}setFocusByFirstCharacter(e,t){const n=xS(this.menuItemNodes,e,this.firstChars,t);n>=0&&Lt(this.menuItemNodes,this.menuItemNodes[n])}onMenuKeydown(e){const t=AS(e.target,"tooltip");this.menuItemNodes||(this.menuItemNodes=[...e.target.parentNode.getElementsByTagName("li")].filter(s=>s.ariaDisabled!=="true")),this.firstChars.length===0&&this.menuItemNodes.forEach(s=>{var o;this.firstChars.push((o=s.textContent.trim()[0])===null||o===void 0?void 0:o.toLowerCase())});const n=this.menuItemNodes.find(s=>s.tabIndex===0);switch(e.key){case" ":case"Enter":e.target.click();break;case"Escape":this.handleEscape(t);break;case"ArrowUp":$S(this.menuItemNodes,n),ge(e);break;case"ArrowDown":IS(this.menuItemNodes,n),ge(e);break;default:TS(e.key)&&this.setFocusByFirstCharacter(n,e.key);break}}}const at=d.createContext({level:0});var O1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const w1=Wt.PREFIX;class Qr extends ce{constructor(e){super(e),this.foundation=new S1(this.adapter)}get adapter(){return Object.assign({},super.adapter)}render(){const e=this.props,{children:t,className:n,style:s}=e,o=O1(e,["children","className","style"]);return d.createElement("ul",Object.assign({role:"menu","aria-orientation":"vertical"},o,{className:E(`${w1}-menu`,n),style:s,onKeyDown:i=>this.foundation.onMenuKeydown(i)}),t)}}Qr.propTypes={children:a.node,className:a.string,style:a.object};Qr.contextType=at;const Ke=Wt.PREFIX;class zt extends ce{render(){const{children:e,disabled:t,className:n,forwardRef:s,style:o,type:i,active:l,icon:c,onKeyDown:u,showTick:h,hover:f}=this.props,{showTick:m}=this.context,y=m??h,v=E(n,{[`${Ke}-item`]:!0,[`${Ke}-item-disabled`]:t,[`${Ke}-item-hover`]:f,[`${Ke}-item-withTick`]:y,[`${Ke}-item-${i}`]:i,[`${Ke}-item-active`]:l}),p={};t||["onClick","onMouseEnter","onMouseLeave","onContextMenu"].forEach(_=>{this.context.level!==1&&_==="onClick"?p.onMouseDown=T=>{var C,S;T.button===0&&((S=(C=this.props)[_])===null||S===void 0||S.call(C,T))}:p[_]=this.props[_]});let g=null;switch(!0){case(y&&l):g=d.createElement(cn,null);break;case(y&&!l):g=d.createElement(cn,{style:{color:"transparent"}});break;default:g=null;break}let b=null;return c&&(b=d.createElement("div",{className:`${Ke}-item-icon`},c)),d.createElement("li",Object.assign({role:"menuitem",tabIndex:-1,"aria-disabled":t},p,{onKeyDown:u,ref:_=>s(_),className:v,style:o},this.getDataAttr(this.props)),g,b,e)}}zt.propTypes={children:a.oneOfType([a.string,a.node]),name:a.string,disabled:a.bool,selected:a.bool,onClick:a.func,onMouseEnter:a.func,onMouseLeave:a.func,onContextMenu:a.func,className:a.string,style:a.object,forwardRef:a.func,type:a.oneOf(xn.ITEM_TYPE),active:a.bool,icon:a.node};zt.contextType=at;zt.defaultProps={disabled:!1,divided:!1,selected:!1,onMouseEnter:A,onMouseLeave:A,forwardRef:A};zt.elementType="Dropdown.Item";const C1=Wt.PREFIX,Ji=function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{style:e,className:t}=r;return d.createElement("div",{className:E(`${C1}-divider`,t),style:e})};Ji.propTypes={style:a.object,className:a.string};const Oo=Wt.PREFIX;class es extends $.PureComponent{render(){const{className:e,style:t,children:n}=this.props,{showTick:s}=this.context,o=E({[`${Oo}-title`]:!0,[`${Oo}-title-withTick`]:s},e);return d.createElement("div",{className:o,style:t},n)}}es.propTypes={children:a.node,className:a.string,style:a.object};es.contextType=at;var rr=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const T1=xn.POSITION_SET,E1=xn.TRIGGER_SET;class te extends ce{constructor(e){super(e),this.handleVisibleChange=t=>this.foundation.handleVisibleChange(t),this.state={popVisible:e.visible},this.foundation=new _1(this.adapter),this.tooltipRef=d.createRef()}get adapter(){return Object.assign(Object.assign({},super.adapter),{setPopVisible:e=>this.setState({popVisible:e}),notifyVisibleChange:e=>{var t,n;return(n=(t=this.props).onVisibleChange)===null||n===void 0?void 0:n.call(t,e)},getPopupId:()=>this.tooltipRef.current.getPopupId()})}renderContent(){const{render:e,menu:t,contentClassName:n,style:s,showTick:o,prefixCls:i,trigger:l}=this.props,c=E(i,n),{level:u=0}=this.context,h={showTick:o,level:u+1,trigger:l};let f=null;return d.isValidElement(e)?f=e:Array.isArray(t)&&(f=this.renderMenu()),d.createElement(at.Provider,{value:h},d.createElement("div",{className:c,style:s},d.createElement("div",{className:`${i}-content`,"x-semi-prop":"render"},f)))}renderMenu(){const{menu:e}=this.props,t=e.map((n,s)=>{switch(n.node){case"title":{const{name:o,node:i}=n,l=rr(n,["name","node"]);return d.createElement(te.Title,Object.assign({},l,{key:i+o+s}),o)}case"item":{const{node:o,name:i}=n,l=rr(n,["node","name"]);return d.createElement(te.Item,Object.assign({},l,{key:o+i+s}),i)}case"divider":return d.createElement(te.Divider,{key:n.node+s});default:return null}});return d.createElement(te.Menu,null,t)}renderPopCard(){const{render:e,contentClassName:t,style:n,showTick:s,prefixCls:o}=this.props,i=E(o,t),{level:l=0}=this.context,c={showTick:s,level:l+1};return d.createElement(at.Provider,{value:c},d.createElement("div",{className:i,style:n},d.createElement("div",{className:`${o}-content`},e)))}render(){const e=this.props,{children:t,position:n,trigger:s,onVisibleChange:o,zIndex:i,className:l,motion:c,margin:u,style:h,prefixCls:f}=e,m=rr(e,["children","position","trigger","onVisibleChange","zIndex","className","motion","margin","style","prefixCls"]);let{spacing:y}=this.props;const{level:v}=this.context,{popVisible:p}=this.state,g=this.renderContent();return v>0?y=typeof y=="number"?y:So.NESTED_SPACING:(y===null||typeof y>"u")&&(y=So.SPACING),d.createElement(Ie,Object.assign({zIndex:i,motion:c,margin:u,content:g,className:l,prefixCls:f,spacing:y,position:n,trigger:s,onVisibleChange:this.handleVisibleChange,showArrow:!1,returnFocusOnClose:!0,ref:this.tooltipRef},m),d.isValidElement(t)?d.cloneElement(t,{className:E(R(t,"props.className"),{[`${f}-showing`]:p}),"aria-haspopup":!0,"aria-expanded":p,onKeyDown:b=>{this.foundation.handleKeyDown(b);const _=R(t,"props.onKeyDown");_&&_(b)}}):t)}}te.Menu=Qr;te.Item=zt;te.Divider=Ji;te.Title=es;te.contextType=at;te.propTypes={children:a.node,contentClassName:a.oneOfType([a.string,a.array]),className:a.string,getPopupContainer:a.func,margin:a.oneOfType([a.number,a.object]),mouseEnterDelay:a.number,mouseLeaveDelay:a.number,menu:a.array,motion:a.oneOfType([a.bool,a.func,a.object]),onVisibleChange:a.func,prefixCls:a.string,position:a.oneOf(T1),rePosKey:a.oneOfType([a.string,a.number]),render:a.node,spacing:a.oneOfType([a.number,a.object]),showTick:a.bool,style:a.object,trigger:a.oneOf(E1),visible:a.bool,zIndex:a.number};te.__SemiComponentName__="Dropdown";te.defaultProps=Le(te.__SemiComponentName__,{onVisibleChange:A,prefixCls:Wt.PREFIX,zIndex:je.DEFAULT_Z_INDEX,motion:!0,trigger:"hover",position:"bottom",mouseLeaveDelay:xn.DEFAULT_LEAVE_DELAY,showTick:!1,closeOnEsc:!0,onEscKeyDown:A});const P1={PREFIX:D+"-overflow-list"},wo={COLLAPSE:"collapse",SCROLL:"scroll"},Co={START:"start",END:"end"},$1={GROW:1},Ue={BOUNDARY_SET:Object.values(Co),POSITION_SET:["vertical","horizontal"],MODE_SET:Object.values(wo),MODE_MAP:wo,BOUNDARY_MAP:Co,OVERFLOW_DIR:$1},To={MINIMUM_HTML_ELEMENT_WIDTH:4};class ts extends d.PureComponent{componentDidMount(){const{items:e}=this.props;this.cachedKeys=Object.keys(e);const{root:t,threshold:n,rootMargin:s,option:o,onIntersect:i}=this.props;this.observer=new IntersectionObserver(i,Object.assign({root:t,threshold:n,rootMargin:s},o)),this.observeElement()}componentDidUpdate(){const{items:e}=this.props,t=Object.keys(e);de(this.cachedKeys,t)||(this.observeElement(!0),this.cachedKeys=t)}componentWillUnmount(){this.observer&&(this.observer.disconnect(),this.observer=null)}observeElement(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const{items:t}=this.props;if(Se(t)){this.observer.disconnect();return}e&&this.observer.disconnect(),Object.keys(t).forEach(n=>{const s=t[n];s&&nt(s)&&this.observer.observe(s)})}render(){const{children:e}=this.props;return e}}ts.propTypes={onIntersect:a.func,option:a.object,root:a.any,threshold:a.number,rootMargin:a.string,items:a.object};ts.defaultProps={onIntersect:()=>{},threshold:.75,rootMargin:"0px",option:{},items:{}};const I1=Ue.BOUNDARY_MAP;class x1 extends ye{constructor(e){super(Object.assign({},e)),this.previousY=void 0,this.isScrollMode=()=>{const{renderMode:t}=this.getProps();return t==="scroll"},this.getReversedItems=()=>{const{items:t}=this.getProps();return dn(t).reverse()}}getOverflowItem(){const{items:e}=this.getProps(),{visibleState:t,overflow:n}=this.getStates();if(!this.isScrollMode())return n;const s=e.map(c=>{let{key:u}=c;return!!t.get(u)}),o=s.indexOf(!0),i=s.lastIndexOf(!0),l=[];return l[0]=o>=0?e.slice(0,o):[],l[1]=i>=0?e.slice(i+1,e.length):e.slice(),l}handleIntersect(e){const t=dn(this.getState("visibleState")),n={};e.forEach(c=>{const u=R(c,"target.dataset.scrollkey"),h=c.isIntersecting;n[u]=c,t.set(u,h)});let s=!1;for(const c of t.values())if(c){s=!0;break}const o=s,[i]=e,l=i.boundingClientRect.y;if(!o&&this.previousY!==void 0&&l!==this.previousY){this.previousY=l;return}this.previousY=l,this._adapter.updateVisibleState(t),this._adapter.notifyIntersect(n)}handleCollapseOverflow(){const{minVisibleItems:e,collapseFrom:t}=this.getProps(),{overflowWidth:n,containerWidth:s,pivot:o,overflowStatus:i}=this.getStates(),{items:l,onOverflow:c}=this.getProps();let u=n,h=0,f=!1;for(const m of this._adapter.getItemSizeMap().values()){if(u+=m,u>s){f=!0;break}if(h===l.length-1){this._adapter.updateStates({overflowStatus:"normal",pivot:l.length-1,visible:l,overflow:[]});break}h++}if(f){const m=Math.max(e,h),y=t===I1.START,v=y?this.getReversedItems().slice(0,m).reverse():l.slice(0,m),p=y?this.getReversedItems().slice(m).reverse():l.slice(m);this._adapter.updateStates({overflowStatus:"overflowed",pivot:m,visible:v,overflow:p}),o!==m&&c(p);return}}}const Ot=P1.PREFIX,sr=Ue.BOUNDARY_MAP,Eo=Ue.OVERFLOW_DIR,wt=Ue.MODE_MAP;class lt extends ce{constructor(e){var t;super(e),t=this,this.scroller=null,this.spacer=null,this.isScrollMode=()=>{const{renderMode:n}=this.props;return n===wt.SCROLL},this.resize=function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];var s;const o=(s=n[0])===null||s===void 0?void 0:s.target.clientWidth;t.setState({containerWidth:o,overflowStatus:"calculating"})},this.reintersect=n=>{this.foundation.handleIntersect(n)},this.mergeRef=(n,s,o)=>{this.itemRefs[o]=s,typeof n=="function"?n(s):typeof n=="object"&&n&&"current"in n&&(n.current=s)},this.renderOverflow=()=>{const n=this.foundation.getOverflowItem();return this.props.overflowRenderer(n)},this.getItemKey=(n,s)=>{const{itemKey:o}=this.props;return fe(o)?o(n):R(n,o||"key",s)},this.renderItemList=()=>{const{className:n,wrapperClassName:s,wrapperStyle:o,style:i,visibleItemRenderer:l,renderMode:c,collapseFrom:u}=this.props,{visible:h,overflowStatus:f}=this.state;let m=this.renderOverflow();if(!this.isScrollMode()&&(Array.isArray(m)&&(m=d.createElement(d.Fragment,null,m)),d.isValidElement(m))){const p=d.cloneElement(m);m=d.createElement(rt,{onResize:g=>{let[b]=g;this.setState({overflowWidth:b.target.clientWidth,overflowStatus:"calculating"})}},d.createElement("div",{className:`${Ot}-overflow`},p))}const y=c===wt.SCROLL?(()=>{const p=[d.createElement("div",{className:E(s,`${Ot}-scroll-wrapper`),ref:g=>{this.scroller=g},style:Object.assign({},o),key:`${Ot}-scroll-wrapper`},h.map(l).map(g=>{const{forwardRef:b,key:_}=g;return d.cloneElement(g,{ref:O=>this.mergeRef(b,O,_),"data-scrollkey":`${_}`,key:_})}))];return this.props.overflowRenderDirection==="both"?(p.unshift(m[0]),p.push(m[1])):this.props.overflowRenderDirection==="start"?(p.unshift(m[1]),p.unshift(m[0])):(p.push(m[0]),p.push(m[1])),p})():[u===sr.START?m:null,h.map((p,g)=>{const{key:b}=p,_=l(p,g),O=d.cloneElement(_);return d.createElement(rt,{key:b??g,onResize:T=>{let[C]=T;return this.onItemResize(C,p,g)}},d.createElement("div",{key:b??g,className:`${Ot}-item`},O))}),u===sr.END?m:null];return d.createElement("div",{className:E(`${Ot}`,n),style:Object.assign(Object.assign({},i),c===wt.COLLAPSE?{maxWidth:"100%",visibility:f==="calculating"?"hidden":"visible"}:null)},...y)},this.onItemResize=(n,s,o)=>{const i=this.getItemKey(s,o),l=this.itemSizeMap.get(i);l?l!==n.target.clientWidth&&(this.itemSizeMap.set(i,n.target.clientWidth),this.setState({overflowStatus:"calculating"})):this.itemSizeMap.set(i,n.target.clientWidth);const{maxCount:c}=this.state;this.itemSizeMap.size===c&&this.setState({overflowStatus:"calculating"})},this.state={direction:Eo.GROW,lastOverflowCount:0,overflow:[],visible:[],containerWidth:0,visibleState:new Map,itemSizeMap:new Map,overflowStatus:"calculating",pivot:-1,overflowWidth:0,maxCount:0},this.foundation=new x1(this.adapter),this.previousWidths=new Map,this.itemRefs={},this.itemSizeMap=new Map}static getDerivedStateFromProps(e,t){const{prevProps:n}=t,s={};s.prevProps=e;const o=i=>!n&&i in e||n&&!de(n[i],e[i]);if(o("items")||o("style")){if(s.direction=Eo.GROW,s.lastOverflowCount=0,s.maxCount=0,e.renderMode===wt.SCROLL)s.visible=e.items,s.overflow=[];else{let i=e.items.length;Math.floor(t.containerWidth/To.MINIMUM_HTML_ELEMENT_WIDTH)!==0&&(i=Math.min(i,Math.floor(t.containerWidth/To.MINIMUM_HTML_ELEMENT_WIDTH)));const l=e.collapseFrom===sr.START,c=l?dn(e.items).reverse().slice(0,i):e.items.slice(0,i),u=l?dn(e.items).reverse().slice(i):e.items.slice(i);s.visible=c,s.overflow=u,s.maxCount=i}s.pivot=-1,s.overflowStatus="calculating"}return s}get adapter(){return Object.assign(Object.assign({},super.adapter),{updateVisibleState:e=>{this.setState({visibleState:e},()=>{var t,n;(n=(t=this.props).onVisibleStateChange)===null||n===void 0||n.call(t,e)})},updateStates:e=>{this.setState(Object.assign({},e))},notifyIntersect:e=>{this.props.onIntersect&&this.props.onIntersect(e)},getItemSizeMap:()=>this.itemSizeMap})}componentDidUpdate(e,t){const n=e.items.map(u=>u.key),s=this.props.items.map(u=>u.key);de(n,s)||(this.itemRefs={},this.setState({visibleState:new Map}));const{overflow:o,containerWidth:i,visible:l,overflowStatus:c}=this.state;this.isScrollMode()||c!=="calculating"||this.foundation.handleCollapseOverflow()}render(){const e=this.renderItemList(),{renderMode:t}=this.props;return t===wt.SCROLL?d.createElement(ts,{onIntersect:this.reintersect,root:this.scroller,threshold:this.props.threshold,items:this.itemRefs},e):d.createElement(rt,{onResize:this.resize},e)}}lt.__SemiComponentName__="OverflowList";lt.defaultProps=Le(lt.__SemiComponentName__,{collapseFrom:"end",minVisibleItems:0,overflowRenderer:()=>null,renderMode:"collapse",threshold:.75,visibleItemRenderer:()=>null,onOverflow:()=>null,overflowRenderDirection:"both"});lt.propTypes={className:a.string,collapseFrom:a.oneOf(Ue.BOUNDARY_SET),direction:a.oneOf(Ue.POSITION_SET),items:a.array,minVisibleItems:a.number,onIntersect:a.func,onOverflow:a.func,overflowRenderer:a.func,renderMode:a.oneOf(Ue.MODE_SET),style:a.object,threshold:a.number,visibleItemRenderer:a.func,wrapperClassName:a.string,wrapperStyle:a.object,collapseMask:a.object,overflowRenderDirection:a.string};const ns={PREFIX:`${D}-select`,PREFIX_OPTION:`${D}-select-option`,PREFIX_GROUP:`${D}-select-group`},$e={SIZE_SET:["small","large","default"],POSITION_SET:He.POSITION_SET,STATUS:md,SEARCH_POSITION_TRIGGER:"trigger",SEARCH_POSITION_DROPDOWN:"dropdown"},Qi={LIST_HEIGHT:270};var Po=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class A1 extends ye{constructor(e){super(Object.assign({},e)),this._keydownHandler=null}init(){this._setDropdownWidth();const e=this.getProp("defaultOpen"),t=this.getProp("open"),n=this._collectOptions();this._setDefaultSelection(n),(e||t)&&this.open(void 0,n),this.getProp("autoFocus")&&this.focus()}focus(){const e=this._isFilterable(),t=this._isMultiple();this._adapter.updateFocusState(!0),this._adapter.setIsFocusInContainer(!1),e&&t?this.focusInput():e&&!t?this.toggle2SearchInput(!0):this._focusTrigger()}_focusTrigger(){this._adapter.focusTrigger()}destroy(){this._adapter.unregisterClickOutsideHandler()}_setDropdownWidth(){const{style:e,dropdownMatchSelectWidth:t}=this.getProps();let n;t&&(e&&Ve(e.width)||e&&ee(e.width)&&!e.width.includes("%")?n=e.width:n=this._adapter.getTriggerWidth(),this._adapter.setOptionWrapperWidth(n))}_collectOptions(){const e=this._adapter.getOptionsFromChildren();return this._adapter.updateOptions(e),this._adapter.rePositionDropdown(),e}_setDefaultSelection(e){let{value:t}=this.getProps();const{defaultValue:n}=this.getProps();this._isControlledComponent()||(t=n),this._update(t,e)}handleOptionListChange(){const e=this._collectOptions(),{selections:t}=this.getStates();this.updateOptionsActiveStatus(t,e);const{defaultActiveFirstOption:n}=this.getProps();n&&this._adapter.updateFocusIndex(0)}handleOptionListChangeHadDefaultValue(){const e=this.getState("selections");let t;const{onChangeWithObject:n}=this.getProps(),s=this._isMultiple();switch(!0){case(s&&!!e.size):try{t=[...e].map(i=>n?i[1]:i[1].value)}catch{t=[]}break;case(s&&!e.size):t=[];break;case(!s&&!!e.size):try{t=n?[...e][0][1]:[...e][0][1].value}catch{}break;case(!s&&!e.size):break}const o=this._adapter.getOptionsFromChildren();this._update(t,o)}handleValueChange(e){const{allowCreate:t,autoClearSearchValue:n,remote:s}=this.getProps(),{inputValue:o}=this.getStates();let i;t&&this._isControlledComponent()?(i=this.getState("options"),i.forEach(l=>l._show=!0)):i=this._adapter.getOptionsFromChildren(),this._adapter.rePositionDropdown(),this._isFilterable()&&!n&&o&&!s&&(i=this._filterOption(i,o)),this._update(e,i)}_update(e,t){let n;this._isMultiple()?(n=this._updateMultiple(e,t),this.updateOverflowItemCount(n.size)):n=this._updateSingle(e,t),this._adapter.updateSelection(n),this.updateOptionsActiveStatus(n,t)}_updateSingle(e,t){const n=new Map,{onChangeWithObject:s}=this.getProps(),o=s&&typeof e<"u"?e.value:e,i=t.filter(c=>c.value===o),l=!i.length&&typeof o<"u"&&o!==null;if(i.length){const c=i[0],u=Object.assign({},c);n.set(u.label,u)}else if(l){let c={value:e,label:e,_notExist:!0,_scrollIndex:-1};s&&(c=Object.assign(Object.assign({},e),{_notExist:!0,_scrollIndex:-1})),n.set(c.label,c)}return n}_updateMultiple(e,t){const n=this.getState("selections");let s=[];const o=Array.isArray(e);this.checkMultipleProps(),n.size&&(s=[...n].map(u=>u[1]));const i=new Map;let l=e;const{onChangeWithObject:c}=this.getProps();return c&&o&&(l=e.map(u=>u.value)),o&&l.length&&l.forEach((u,h)=>{const f=t.findIndex(m=>m.value===u);if(f!==-1)i.set(t[f].label,t[f]);else{const m=s.findIndex(y=>y.value===u);if(m!==-1){const y=s[m];if(c){const v=Object.assign({},e[h]);de(v,y)?i.set(y.label,y):i.set(v.label,v)}else i.set(y.label,y)}else{let y={value:u,label:u,_notExist:!0};c&&(y=Object.assign(Object.assign({},e[h]),{_notExist:!0})),i.set(y.label,Object.assign(Object.assign({},y),{_scrollIndex:-1}))}}}),i}_isMultiple(){return this.getProp("multiple")}_isDisabled(){return this.getProp("disabled")}_isFilterable(){return!!this.getProp("filter")}handleClick(e){const{clickToHide:t}=this.getProps(),{isOpen:n}=this.getStates();this._isDisabled()||(n?n&&t?this.close({event:e}):n&&!t&&this.focusInput():(this.open(),this._notifyFocus(e)))}open(e,t){const n=this._isFilterable(),s=t||this.getState("options");if(n){const i=this._filterOption(s,"").filter(l=>!l._inputCreateOnly);this._adapter.updateOptions(i),this.toggle2SearchInput(!0)}else this._adapter.updateFocusState(!0);this._adapter.openMenu(()=>{const{searchPosition:o,autoFocus:i}=this.getProps();i&&o===$e.SEARCH_POSITION_DROPDOWN&&this._adapter.focusDropdownInput()}),this._setDropdownWidth(),this._adapter.notifyDropdownVisibleChange(!0),this.bindKeyBoardEvent(),this._adapter.registerClickOutsideHandler(o=>{this.close({event:o}),this._notifyBlur(o),this._adapter.updateFocusState(!1)})}toggle2SearchInput(e){e?this._adapter.toggleInputShow(e,()=>this.focusInput()):this._adapter.toggleInputShow(e,()=>{})}close(e){const{event:t,closeCb:n,notToggleInput:s}=e||{},{isFocus:o}=this.getStates();this._adapter.closeMenu(),this._adapter.notifyDropdownVisibleChange(!1),this._adapter.setIsFocusInContainer(!1),o&&this._focusTrigger(),this._adapter.unregisterClickOutsideHandler();const i=this._isFilterable();i&&!s&&this.toggle2SearchInput(!1),this._adapter.once("popoverClose",()=>{i&&this.clearInput(t),n&&n()})}onSelect(e,t,n){if(this._isDisabled())return;e._inputCreateOnly&&this._adapter.notifyCreate(e),this._isMultiple()?this._handleMultipleSelect(e,n):this._handleSingleSelect(e,n),this._adapter.updateFocusIndex(t)}_handleSingleSelect(e,t){var{value:n,label:s}=e,o=Po(e,["value","label"]);const i=new Map().set(s,Object.assign({value:n,label:s},o));this._notifySelect(n,Object.assign({value:n,label:s},o));const l=this._isFilterable();this._isControlledComponent()?this.close({event:t,notToggleInput:!0,closeCb:()=>{this._notifyChange(i),l&&this.toggle2SearchInput(!1)}}):(this._adapter.updateSelection(i),this._notifyChange(i),this.close({event:t,closeCb:()=>{this.updateOptionsActiveStatus(i)}}))}_handleMultipleSelect(e,t){var{value:n,label:s}=e,o=Po(e,["value","label"]);const i=this._adapter.getMaxLimit(),l=this._adapter.getSelections(),{autoClearSearchValue:c}=this.getProps();if(l.has(s))this._notifyDeselect(n,Object.assign({value:n,label:s},o)),l.delete(s);else if(i&&l.size===i){this._adapter.notifyMaxLimit(Object.assign({value:n,label:s},Dt(o,"_scrollIndex")));return}else this._notifySelect(n,Object.assign({value:n,label:s},o)),l.set(s,Object.assign({value:n,label:s},o));if(this._isControlledComponent())this._notifyChange(l),this._isFilterable()&&(c&&this.clearInput(t),this.focusInput());else{this._adapter.updateSelection(l),this.updateOverflowItemCount(l.size),this._adapter.rePositionDropdown();let{options:u}=this.getStates();this._isFilterable()&&(c&&(this.clearInput(t),u=this._filterOption(u,"")),this.focusInput()),this.updateOptionsActiveStatus(l,u),this._notifyChange(l)}}clearSelected(){const e=new Map;this._isControlledComponent()?(this._notifyChange(e),this._adapter.notifyClear()):(this._adapter.updateSelection(e),this.updateOptionsActiveStatus(e),this._notifyChange(e),this._adapter.notifyClear());const{isOpen:t}=this.getStates();t&&this._adapter.rePositionDropdown()}updateOptionsActiveStatus(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getState("options");const{allowCreate:n}=this.getProps(),s=t.map(o=>(e.has(o.label)?(o._selected=!0,n&&delete o._inputCreateOnly):(o._inputCreateOnly&&(o._show=!1),o._selected=!1),o));this._adapter.updateOptions(s)}removeTag(e){const t=this._adapter.getSelections();t.delete(e.label),this._isControlledComponent()?(this._notifyDeselect(e.value,e),this._notifyChange(t)):(this._notifyDeselect(e.value,e),this._adapter.updateSelection(t),this.updateOverflowItemCount(t.size),this.updateOptionsActiveStatus(t),this._adapter.rePositionDropdown(),this._notifyChange(t))}clearInput(e){const{inputValue:t}=this.getStates();if(t!==""){this._adapter.updateInputValue(""),this._adapter.notifySearch("",e);const{options:n}=this.getStates(),{remote:s}=this.getProps();let o=n;s||(o=this._filterOption(n,"")),this._adapter.updateOptions(o)}}focusInput(){this._adapter.focusInput(),this._adapter.updateFocusState(!0),this._adapter.setIsFocusInContainer(!1)}handleInputChange(e,t){this._adapter.updateInputValue(e);const{options:n,isOpen:s}=this.getStates(),{allowCreate:o,remote:i}=this.getProps();let l=n;i||(l=this._filterOption(n,e)),l=this._createOptionByInput(o,l,e),this._adapter.updateOptions(l),this._adapter.notifySearch(e,t),this._isMultiple()&&this._adapter.rePositionDropdown()}_filterOption(e,t){const n=this.getProp("filter");if(n){if(typeof n=="boolean"&&n){const s=t.toLowerCase();return e.map(o=>{const i=o.label.toString().toLowerCase(),l=o._parentGroup&&o._parentGroup.label,c=i.includes(s),u=ee(l)&&l.toLowerCase().includes(s);return c||u?o._show=!0:o._show=!1,o})}else if(typeof n=="function")return e.map(s=>(n(t,s)?s._show=!0:s._show=!1,s))}else return e}_createOptionByInput(e,t,n){if(e)if(n){const s={_show:!0,_selected:!1,value:n,label:n,_inputCreateOnly:!0};let o=-1,i=-1;t.forEach((l,c)=>{!l._show&&!l._inputCreateOnly||(l.label===n&&(i=c),l._inputCreateOnly&&(o=c,l.value=n,l.label=n,l._show=!0))}),o===-1&&i===-1&&t.push(s),i!==-1&&(t=t.filter(l=>!l._inputCreateOnly))}else t=t.filter(s=>!s._inputCreateOnly);return t}bindKeyBoardEvent(){this._keydownHandler=e=>{this._handleKeyDown(e)},this._adapter.registerKeyDown(this._keydownHandler)}unBindKeyBoardEvent(){this._keydownHandler&&this._adapter.unregisterKeyDown()}_handleKeyDown(e){const t=e.keyCode,{loading:n,filter:s,multiple:o,disabled:i}=this.getProps(),{isOpen:l}=this.getStates();if(!(n||i))switch(t){case Fe.UP:e.preventDefault(),this._handleArrowKeyDown(-1);break;case Fe.DOWN:e.preventDefault(),this._handleArrowKeyDown(1);break;case Fe.BACKSPACE:this._handleBackspaceKeyDown();break;case Fe.ENTER:ge(e),this._handleEnterKeyDown(e);break;case Fe.ESC:l&&this.close({event:e}),s&&!o&&this._focusTrigger();break;case Fe.TAB:this._handleTabKeyDown(e);break}}handleContainerKeyDown(e){const t=e.keyCode,{isOpen:n}=this.getStates();switch(t){case Fe.TAB:n&&this._handleTabKeyDown(e);break}}_getEnableFocusIndex(e){const{focusIndex:t,options:n}=this.getStates(),s=n.filter(l=>l._show),o=s.length;let i=t+e;if(i<0&&(i=o-1),i>=o&&(i=0),e>0){let l=-1;for(let c=0;c<s.length&&(!s[c].disabled&&(l=c),!(l>=i));c++);i=l}else{let l=s.length;for(let c=o-1;c>=0&&(!s[c].disabled&&(l=c),!(l<=i));c--);i=l}this._adapter.updateFocusIndex(i),this._adapter.updateScrollTop(i)}_handleArrowKeyDown(e){const{isOpen:t}=this.getStates();t?this._getEnableFocusIndex(e):this.open()}_handleTabKeyDown(e){const{isOpen:t}=this.getStates();if(this._adapter.updateFocusState(!1),t){const n=this._adapter.getContainer(),s=this._adapter.getFocusableElements(n);s.length>0?e.shiftKey?this._handlePanelOpenShiftTabKeyDown(s,e):this._handlePanelOpenTabKeyDown(s,e):(this.close({event:e}),this._notifyBlur(e))}else this._notifyBlur(e)}_handlePanelOpenTabKeyDown(e,t){const n=this._adapter.getActiveElement();this._adapter.getIsFocusInContainer()?n===e[e.length-1]&&(this._focusTrigger(),this.close({event:t}),ge(t)):(e[0].focus(),this._adapter.setIsFocusInContainer(!0),ge(t))}_handlePanelOpenShiftTabKeyDown(e,t){const n=this._adapter.getActiveElement();this._adapter.getIsFocusInContainer()?n===e[0]&&(this._focusTrigger(),this._adapter.setIsFocusInContainer(!1),ge(t)):(this.close({event:t}),this._notifyBlur(t))}_handleEnterKeyDown(e){const{isOpen:t,options:n,focusIndex:s}=this.getStates();if(!t)this.open();else if(s!==-1){const o=n.filter(l=>l._show),{length:i}=o;if(i<=s)return;if(o&&i){const l=o[s];if(l.disabled)return;this.onSelect(l,s,e)}}else this.close({event:e})}_handleBackspaceKeyDown(){if(this._isMultiple()){const e=this._adapter.getSelections(),{inputValue:t}=this.getStates(),n=e.size;if(n&&!t){const s=[...e.keys()];let o=n-1,i=s[o],l=e.get(i),c=!1;if(l.disabled&&o===0)return;for(;l.disabled&&o!==0;)o=o-1,i=s[o],l=e.get(i),o==0&&l.disabled&&(c=!0);c||this.removeTag(l)}}}_notifyChange(e){const{onChangeWithObject:t}=this.getProps(),n=this.getState("selections");let s;const o=[...e.values()],i=this._isMultiple();if(this._diffSelections(e,n,i))switch(!0){case t:this._notifyChangeWithObject(e);break;case(!t&&!i):s=o.length?o[0].value:void 0,this._adapter.notifyChange(s);break;case(!t&&i):s=o.length?o.map(c=>c.value):[],this._adapter.notifyChange(s);break}}_removeInternalKey(e){let t=Object.assign({},e);return delete t._parentGroup,delete t._show,delete t._selected,delete t._scrollIndex,delete t._keyInJsx,"_keyInOptionList"in t&&(t.key=t._keyInOptionList,delete t._keyInOptionList),t}_notifySelect(e,t){const n=this._removeInternalKey(t);this._adapter.notifySelect(e,n)}_notifyDeselect(e,t){const n=this._removeInternalKey(t);this._adapter.notifyDeselect(e,n)}_diffSelections(e,t,n){let s=!0,o=!0;if(!n){const i=[...e.values()],l=[...t.values()],c=i[0]?i[0].value:i[0],u=l[0]?l[0].value:l[0];o=!de(c,u);const h=i[0]?i[0].label:i[0],f=l[0]?l[0].label:l[0];s=!de(h,f)}return o||s}_notifyChangeWithObject(e){this.getState("selections");const t=[];for(const n of e.entries()){let s=Object.assign({label:n[0]},n[1]);s=this._removeInternalKey(s),t.push(s)}this._isMultiple()?this._adapter.notifyChange(t):this._adapter.notifyChange(t[0])}_notifyBlur(e){this._adapter.notifyBlur(e)}_notifyFocus(e){this._adapter.notifyFocus(e)}handleMouseEnter(e){this._adapter.updateHovering(!0),this._adapter.notifyMouseEnter(e)}handleMouseLeave(e){this._adapter.updateHovering(!1),this._adapter.notifyMouseLeave(e)}handleClearClick(e){const{filter:t,searchPosition:n}=this.getProps();t&&n===$e.SEARCH_POSITION_TRIGGER&&this.clearInput(e),this.focus(),this.clearSelected(),e.stopPropagation()}handleKeyPress(e){e&&e.key===Br&&this.handleClick(e)}handleClearBtnEnterPress(e){Kr(e)&&this.handleClearClick(e)}handleOptionMouseEnter(e){this._adapter.updateFocusIndex(e)}handleListScroll(e){this._adapter.notifyListScroll(e)}handleTriggerFocus(e){this.bindKeyBoardEvent(),this._adapter.setIsFocusInContainer(!1)}handleTriggerBlur(e){const{filter:t,autoFocus:n}=this.getProps(),{isOpen:s,isFocus:o}=this.getStates();o&&!s&&(this._notifyBlur(e),this._adapter.updateFocusState(!1))}handleInputBlur(e){const{filter:t,autoFocus:n}=this.getProps(),{showInput:s,isOpen:o}=this.getStates(),i=this._isMultiple();t&&!i&&(s||n)&&!o&&this.toggle2SearchInput(!1)}selectAll(){const{options:e}=this.getStates(),{onChangeWithObject:t}=this.getProps();let n=[];if(!this._isMultiple()){console.warn(`[Semi Select]: It seems that you have called the selectAll method in the single-selection Select.
                Please note that this is not a legal way to use it`);return}t?n=e:n=e.map(o=>o.value),this.handleValueChange(n),this._adapter.notifyChange(n)}checkMultipleProps(e){if(this._isMultiple()){const t=e||this.getProps(),{defaultValue:n,value:s}=t,o=s||n;!Rt(o)&&!Array.isArray(o)&&ot(!0,"[Semi Select] defaultValue/value should be array type in multiple mode")}}updateScrollTop(){this._adapter.updateScrollTop()}updateOverflowItemCount(e,t){const{maxTagCount:n,ellipsisTrigger:s}=this.getProps();s&&(t?this._adapter.updateOverflowItemCount(t):typeof n=="number"&&(e-n>0?this._adapter.updateOverflowItemCount(e-n):this._adapter.updateOverflowItemCount(0)))}updateIsFullTags(){const{isFullTags:e}=this.getStates();e||this._adapter.setState({isFullTags:!0})}handlePopoverClose(){this._adapter.emit("popoverClose")}handleSlotMouseEnter(){this._adapter.updateFocusIndex(-1)}}const Ct=ki.PREFIX,ea=vt.TAG_SIZE,N1=vt.AVATAR_SHAPE;class hn extends $.PureComponent{renderNTag(e,t){const{size:n,showPopover:s,popoverProps:o,onPlusNMouseEnter:i}=this.props;let l=d.createElement(Pe,{closable:!1,size:n,color:"grey",style:{backgroundColor:"transparent"},key:"_+n",onMouseEnter:i},"+",e);return s&&(l=d.createElement(xe,Object.assign({showArrow:!0,content:t,trigger:"hover",position:"top",autoAdjustOverflow:!0,className:`${Ct}-rest-group-popover`},o,{key:"_+n_Popover"}),l)),l}renderMergeTags(e){const{maxTagCount:t,tagList:n,restCount:s}=this.props,o=s||n.length-t;let i=e;const l=e.slice(0,t),c=e.slice(t);let u=null;return o>0&&(u=this.renderNTag(o,c),l.push(u),i=l),i}renderAllTags(){const{tagList:e,size:t,mode:n,avatarShape:s,onTagClose:o}=this.props;return e.map(l=>{if(n==="custom")return l;const c=Object.assign({},l);return c.size||(c.size=t),c.avatarShape||(c.avatarShape=s),c.tagKey||(typeof c.children=="string"||typeof c.children=="number"?c.tagKey=c.children:c.tagKey=Math.random()),d.createElement(Pe,Object.assign({},c,{key:c.tagKey,onClose:(u,h,f)=>{c.onClose&&c.onClose(u,h,f),o&&o(u,h,f)}}))})}render(){const{style:e,className:t,maxTagCount:n,size:s}=this.props,o=E({[`${Ct}-group`]:!0,[`${Ct}-group-max`]:n,[`${Ct}-group-small`]:s==="small",[`${Ct}-group-large`]:s==="large"},t),i=this.renderAllTags(),l=typeof n>"u"?i:this.renderMergeTags(i);return d.createElement("div",{style:e,className:o},l)}}hn.defaultProps={style:{},className:"",size:ea[0],avatarShape:"square",onTagClose:()=>{},onPlusNMouseEnter:()=>{}};hn.propTypes={children:a.node,style:a.object,className:a.string,maxTagCount:a.number,restCount:a.number,tagList:a.array,size:a.oneOf(ea),mode:a.string,onTagClose:a.func,showPopover:a.bool,popoverProps:a.object,avatarShape:a.oneOf(N1)};var j1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const $o=(r,e,t,n)=>{const s=r.props;if(!r||!s)return null;const o=Object.assign(Object.assign({value:s.value,label:s.label||s.children||s.value,_show:!0,_selected:!1,_scrollIndex:t},s),{_parentGroup:e});return o._keyInJsx=n||r.key,o},M1=r=>{let e=[],t=[];const n={label:"",children:[],_show:!1};let s=d.Children.toArray(r);s=s.filter(l=>l&&l.props);let o="",i=-1;return s.forEach(l=>{if(l.type.isSelectOption){o="option",i++;const c=$o(l,void 0,i);n.children.push(c),t.push(c)}else if(l.type.isSelectOptionGroup){o="group";let c=l.props,{children:u}=c,h=j1(c,["children"]),f=[];Array.isArray(u)?f=u.map(v=>v.key):f.push(u.key),u=d.Children.toArray(u);const m=u.map((v,p)=>{let g=v.key;return f[p]===null&&(g=l.key+""+v.key),i++,$o(v,h,i,g)}),y=Object.assign(Object.assign({},l.props),{children:m,key:l.key});e.push(y),t=t.concat(m)}else ot(!0,"[Semi Select] The children of `Select` should be `Select.Option` or `Select.OptionGroup`")}),o==="option"&&(e=[n]),{optionGroups:e,options:t}},D1=r=>{let{index:e,data:t,style:n}=r;const{visibleOptions:s,renderOption:o}=t,i=s[e];return o(i,e,n)};var Io=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class Ge extends $.PureComponent{onClick(e,t){var{value:n,label:s,children:o}=e,i=Io(e,["value","label","children"]);const{props:l}=this;l.disabled||l.onSelect(Object.assign(Object.assign({},i),{value:n,label:s||o}),t)}renderOptionContent(e){let{config:t,children:n,inputValue:s,prefixCls:o}=e;return ee(n)&&s?d.createElement(Xr,{searchWords:t.searchWords,sourceString:t.sourceString,highlightClassName:t.highlightClassName}):n}render(){const e=this.props,{children:t,disabled:n,value:s,selected:o,label:i,empty:l,emptyContent:c,onSelect:u,focused:h,showTick:f,className:m,style:y,onMouseEnter:v,prefixCls:p,renderOptionItem:g,inputValue:b,semiOptionId:_}=e,O=Io(e,["children","disabled","value","selected","label","empty","emptyContent","onSelect","focused","showTick","className","style","onMouseEnter","prefixCls","renderOptionItem","inputValue","semiOptionId"]),T=E(p,{[`${p}-disabled`]:n,[`${p}-selected`]:o,[`${p}-focused`]:h,[`${p}-empty`]:l,[m]:m}),C=E([`${p}-icon`]);if(l)return c===null?null:d.createElement(De,{componentName:"Select"},w=>d.createElement("div",{className:T,"x-semi-prop":"emptyContent"},c||w.emptyText));if(typeof g=="function"){const w=E(m,{[`${p}-custom`]:!0,[`${p}-custom-selected`]:o});return g(Object.assign({disabled:n,focused:h,selected:o,style:y,label:i,value:s,inputValue:b,onMouseEnter:P=>v(P),onClick:P=>this.onClick(Object.assign({value:s,label:i,children:t},O),P),className:w},O))}const S={searchWords:[b],sourceString:t,highlightClassName:`${p}-keyword`};return d.createElement("div",Object.assign({className:T,onClick:w=>{this.onClick(Object.assign({value:s,label:i,children:t},O),w)},onMouseEnter:w=>v&&v(w),role:"option",id:_,"aria-selected":o?"true":"false","aria-disabled":n?"true":"false",style:y},xt(O)),f?d.createElement("div",{className:C},d.createElement(cn,null)):null,ee(t)?d.createElement("div",{className:`${p}-text`},this.renderOptionContent({children:t,config:S,inputValue:b,prefixCls:p})):t)}}Ge.isSelectOption=!0;Ge.propTypes={children:a.node,disabled:a.bool,value:a.oneOfType([a.string,a.number]),selected:a.bool,label:a.node,empty:a.bool,emptyContent:a.node,onSelect:a.func,focused:a.bool,showTick:a.bool,className:a.string,style:a.object,onMouseEnter:a.func,prefixCls:a.string,renderOptionItem:a.func,inputValue:a.string};Ge.defaultProps={prefixCls:ns.PREFIX_OPTION};var R1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const L1=ns.PREFIX_GROUP;class An extends ce{render(){const e=this.props,{label:t,className:n,style:s}=e,o=R1(e,["label","className","style"]),i=E(n,{[L1]:!0});return!t&&typeof t!="number"?null:d.createElement("div",Object.assign({className:i,style:s},this.getDataAttr(o)),t)}}An.isSelectOptionGroup=!0;An.propTypes={children:a.oneOfType([a.array,a.node]),label:a.node,className:a.string,style:a.object};var k1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const I=ns.PREFIX,F1=0;class Ye extends ce{constructor(e){super(e),this.setOptionContainerEl=t=>this.optionContainerEl={current:t},this.handleInputChange=(t,n)=>this.foundation.handleInputChange(t,n),this.getTagItem=(t,n,s)=>{const{size:o,disabled:i}=this.props,l=t[0],{value:c}=t[1],u=t[1].disabled||i,h=(v,p)=>{p&&typeof p.preventDefault=="function"&&p.preventDefault(),this.foundation.removeTag({label:l,value:c})},{content:f,isRenderInTag:m}=s(t[1],{index:n,disabled:u,onClose:h}),y={disabled:u,closable:!u,onClose:h};return m?d.createElement(Pe,Object.assign({},y,{color:"white",size:o||"large",key:c,tabIndex:-1}),f):d.createElement($.Fragment,{key:c},f)},this.state={isOpen:!1,isFocus:!1,options:[],selections:new Map,dropdownMinWidth:null,optionKey:F1,inputValue:"",showInput:!1,focusIndex:e.defaultActiveFirstOption?0:-1,keyboardEventSet:{},optionGroups:[],isHovering:!1,isFocusInContainer:!1,isFullTags:!1,overflowItemCount:0},this.selectOptionListID="",this.selectID="",this.virtualizeListRef=d.createRef(),this.inputRef=d.createRef(),this.dropdownInputRef=d.createRef(),this.triggerRef=d.createRef(),this.optionsRef=d.createRef(),this.optionContainerEl=d.createRef(),this.clickOutsideHandler=null,this.onSelect=this.onSelect.bind(this),this.onClear=this.onClear.bind(this),this.onMouseEnter=this.onMouseEnter.bind(this),this.onMouseLeave=this.onMouseLeave.bind(this),this.renderOption=this.renderOption.bind(this),this.onKeyPress=this.onKeyPress.bind(this),this.eventManager=new Oi,this.foundation=new A1(this.adapter)}get adapter(){var e=this;const t={registerKeyDown:o=>{const i={onKeyDown:o};this.setState({keyboardEventSet:i})},unregisterKeyDown:()=>{this.setState({keyboardEventSet:{}})},updateFocusIndex:o=>{this.setState({focusIndex:o})},scrollToFocusOption:()=>{}},n={updateInputValue:o=>{this.setState({inputValue:o})},toggleInputShow:(o,i)=>{this.setState({showInput:o},()=>{i()})},focusInput:()=>{const{preventScroll:o}=this.props;this.inputRef&&this.inputRef.current&&this.inputRef.current.focus({preventScroll:o})},focusDropdownInput:()=>{const{preventScroll:o}=this.props;this.dropdownInputRef&&this.dropdownInputRef.current&&this.dropdownInputRef.current.focus({preventScroll:o})}},s={notifyMaxLimit:o=>this.props.onExceed(o),getMaxLimit:()=>this.props.max,registerClickOutsideHandler:o=>{const i=l=>{const c=this.optionsRef&&this.optionsRef.current,u=this.triggerRef&&this.triggerRef.current,h=Tt.findDOMNode(c),f=l.target,m=l.composedPath&&l.composedPath()||[f];!(h&&h.contains(f))&&!(u&&u.contains(f))&&!(m.includes(u)||m.includes(h))&&o(l)};this.clickOutsideHandler=i,document.addEventListener("mousedown",i,!1)},unregisterClickOutsideHandler:()=>{this.clickOutsideHandler&&(document.removeEventListener("mousedown",this.clickOutsideHandler,!1),this.clickOutsideHandler=null)},rePositionDropdown:()=>{let{optionKey:o}=this.state;o=o+1,this.setState({optionKey:o})},notifyDeselect:(o,i)=>{delete i._parentGroup,this.props.onDeselect(o,i)}};return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},super.adapter),t),n),s),{on:(o,i)=>this.eventManager.on(o,i),off:o=>this.eventManager.off(o),once:(o,i)=>this.eventManager.once(o,i),emit:o=>this.eventManager.emit(o),getOptionsFromChildren:function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.props.children,i=[],l=[];const{optionList:c}=e.props;if(c&&c.length)l=c.map((u,h)=>Object.assign({_show:!0,_selected:!1,_scrollIndex:h},u)),i[0]={children:l,label:""};else{const u=M1(o);i=u.optionGroups,l=u.options}return e.setState({optionGroups:i}),l},updateOptions:o=>{this.setState({options:o})},openMenu:o=>{this.setState({isOpen:!0},()=>{o==null||o()})},closeMenu:()=>{this.setState({isOpen:!1})},getTriggerWidth:()=>{const o=this.triggerRef.current;return o&&o.getBoundingClientRect().width},setOptionWrapperWidth:o=>{this.setState({dropdownMinWidth:o})},updateSelection:o=>{this.setState({selections:o})},getSelections:()=>new Map(this.state.selections),notifyChange:o=>{this.props.onChange(o)},notifySelect:(o,i)=>{delete i._parentGroup,this.props.onSelect(o,i)},notifyDropdownVisibleChange:o=>{this.props.onDropdownVisibleChange(o)},notifySearch:(o,i)=>{this.props.onSearch(o,i)},notifyCreate:o=>{this.props.onCreate(o)},notifyMouseEnter:o=>{this.props.onMouseEnter(o)},notifyMouseLeave:o=>{this.props.onMouseLeave(o)},notifyFocus:o=>{this.props.onFocus(o)},notifyBlur:o=>{this.props.onBlur(o)},notifyClear:()=>{this.props.onClear()},notifyListScroll:o=>{this.props.onListScroll(o)},updateHovering:o=>{this.setState({isHovering:o})},updateFocusState:o=>{this.setState({isFocus:o})},updateOverflowItemCount:o=>{this.setState({overflowItemCount:o})},focusTrigger:()=>{try{const{preventScroll:o}=this.props;this.triggerRef.current.focus({preventScroll:o})}catch{}},getContainer:()=>this.optionContainerEl&&this.optionContainerEl.current,getFocusableElements:o=>Ti(o),getActiveElement:()=>Ci(),setIsFocusInContainer:o=>{this.setState({isFocusInContainer:o})},getIsFocusInContainer:()=>this.state.isFocusInContainer,updateScrollTop:o=>{let i;"renderOptionItem"in this.props?(i=`.${I}-option-custom-selected`,o!==void 0&&(i=`.${I}-option-custom:nth-child(${o+1})`)):(i=`.${I}-option-selected`,o!==void 0&&(i=`.${I}-option:nth-child(${o+1})`));let l=document.querySelector(`#${I}-${this.selectOptionListID} ${i}`);if(Array.isArray(l)&&(l=l[0]),l){const c=l.parentNode;c.scrollTop=l.offsetTop-c.offsetTop-c.clientHeight/2+l.clientHeight/2}}})}componentDidMount(){this.foundation.init(),this.selectOptionListID=ln(),this.selectID=this.props.id||ln()}componentWillUnmount(){this.foundation.destroy()}componentDidUpdate(e,t){const n=d.Children.toArray(e.children).map(i=>i.key),s=d.Children.toArray(this.props.children).map(i=>i.key);let o=!1;(!de(n,s)||!de(e.optionList,this.props.optionList))&&(o=!0,this.foundation.handleOptionListChange()),(!de(this.props.value,e.value)||o)&&("value"in this.props?this.foundation.handleValueChange(this.props.value):this.foundation.handleOptionListChangeHadDefaultValue())}renderTriggerInput(){const{size:e,multiple:t,disabled:n,inputProps:s,filter:o}=this.props,i=R(s,"className"),l=E(`${I}-input`,{[`${I}-input-single`]:!t,[`${I}-input-multiple`]:t},i),{inputValue:c,focusIndex:u}=this.state,h=Object.assign({value:c,disabled:n,className:l,onChange:this.handleInputChange},s);let f={};return t&&(f={width:c?`${c.length*16}px`:"2px"},h.style=f),d.createElement(go,Object.assign({ref:this.inputRef,size:e,"aria-activedescendant":u!==-1?`${this.selectID}-option-${u}`:"",onFocus:m=>{t&&o&&this.setState({isFocus:!0}),m.stopPropagation()},onBlur:m=>this.foundation.handleInputBlur(m)},h))}renderDropdownInput(){const{size:e,multiple:t,disabled:n,inputProps:s,filter:o,searchPosition:i,searchPlaceholder:l}=this.props,{inputValue:c,focusIndex:u}=this.state,h=E(`${I}-dropdown-search-wrapper`,{}),f=R(s,"className"),m=E(`${I}-dropdown-input`,{[`${I}-dropdown-input-single`]:!t,[`${I}-dropdown-input-multiple`]:t},f),y=Object.assign(Object.assign({value:c,disabled:n,className:m,onChange:this.handleInputChange,placeholder:l,showClear:!0},s),{onKeyDown:v=>this.foundation._handleKeyDown(v)});return d.createElement("div",{className:h},d.createElement(go,Object.assign({ref:this.dropdownInputRef,prefix:d.createElement(lC,null),"aria-activedescendant":u!==-1?`${this.selectID}-option-${u}`:""},y)))}close(){this.foundation.close()}open(){this.foundation.open()}clearInput(){this.foundation.clearInput()}selectAll(){this.foundation.selectAll()}deselectAll(){this.foundation.clearSelected()}focus(){this.foundation.focus()}onSelect(e,t,n){this.foundation.onSelect(e,t,n)}onClear(e){e.nativeEvent.stopImmediatePropagation(),this.foundation.handleClearClick(e)}search(e,t){this.handleInputChange(e,t)}renderEmpty(){return d.createElement(Ge,{empty:!0,emptyContent:this.props.emptyContent})}renderLoading(){const e=`${I}-loading-wrapper`;return d.createElement("div",{className:e},d.createElement(Vr,null))}renderOption(e,t,n){const{focusIndex:s,inputValue:o}=this.state,{renderOptionItem:i}=this.props;let l;const c=t===s;let u=n||{};return e.style&&(u=Object.assign(Object.assign({},u),e.style)),e._inputCreateOnly?l=this.renderCreateOption(e,c,t,n):("key"in e&&(e._keyInOptionList=e.key),l=d.createElement(Ge,Object.assign({showTick:!0},e,{selected:e._selected,onSelect:(h,f)=>this.onSelect(h,t,f),focused:c,onMouseEnter:()=>this.onOptionHover(t),style:u,key:e._keyInOptionList||e._keyInJsx||e.label+e.value+t,renderOptionItem:i,inputValue:o,semiOptionId:`${this.selectID}-option-${t}`}),e.label)),l}renderCreateOption(e,t,n,s){const{renderCreateItem:o}=this.props;if(typeof o>"u")return d.createElement(Ge,Object.assign({key:e.key||e.label+e.value,onSelect:(c,u)=>this.onSelect(c,n,u),onMouseEnter:()=>this.onOptionHover(n),showTick:!0},e,{focused:t,style:s}),d.createElement(De,{componentName:"Select"},c=>d.createElement(d.Fragment,null,d.createElement("span",{className:`${I}-create-tips`},c.createText),e.value)));const i=o(e.value,t,s);return d.createElement("div",{role:"button","aria-label":"Use the input box to create an optional item",onClick:l=>this.onSelect(e,n,l),key:e.key||e.label},i)}onOptionHover(e){this.foundation.handleOptionMouseEnter(e)}renderWithGroup(e){const t=[],n=new Map;return e.forEach((s,o)=>{const i=s._parentGroup,l=this.renderOption(s,o);if(i&&!n.has(i.label)){const c=d.createElement(An,Object.assign({},i,{key:i.label}));n.set(i.label,!0),t.push(c)}t.push(l)}),t}renderVirtualizeList(e){const{virtualize:t}=this.props,{direction:n}=this.context,{height:s,width:o,itemSize:i}=t;return d.createElement(kT,{ref:this.virtualizeListRef,height:s||Qi.LIST_HEIGHT,itemCount:e.length,itemSize:i,itemData:{visibleOptions:e,renderOption:this.renderOption},width:o||"100%",style:{direction:n}},D1)}renderOptions(e){const{dropdownMinWidth:t,options:n,selections:s}=this.state,{maxHeight:o,dropdownClassName:i,dropdownStyle:l,outerTopSlot:c,innerTopSlot:u,outerBottomSlot:h,innerBottomSlot:f,loading:m,virtualize:y,multiple:v,emptyContent:p,searchPosition:g,filter:b}=this.props,_=n.filter(w=>w._show);let O=this.renderWithGroup(_);y&&(O=this.renderVirtualizeList(_));const T=Object.assign({minWidth:t},l),C=E({[`${I}-option-list`]:!0,[`${I}-option-list-chosen`]:s.size}),S=!n.length||!n.some(w=>w._show);return d.createElement("div",{id:`${I}-${this.selectOptionListID}`,className:E({[`${I}-option-list-wrapper`]:!(S&&p===null)},i),style:T,ref:this.setOptionContainerEl,onKeyDown:w=>this.foundation.handleContainerKeyDown(w)},c?d.createElement("div",{className:`${I}-option-list-outer-top-slot`,onMouseEnter:()=>this.foundation.handleSlotMouseEnter()},c):null,g===$e.SEARCH_POSITION_DROPDOWN&&b?this.renderDropdownInput():null,d.createElement("div",{style:{maxHeight:`${o}px`},className:C,role:"listbox","aria-multiselectable":v,onScroll:w=>this.foundation.handleListScroll(w)},u?d.createElement("div",{className:`${I}-option-list-inner-top-slot`,onMouseEnter:()=>this.foundation.handleSlotMouseEnter()},u):null,m?this.renderLoading():S?this.renderEmpty():O,f?d.createElement("div",{className:`${I}-option-list-inner-bottom-slot`,onMouseEnter:()=>this.foundation.handleSlotMouseEnter()},f):null),h?d.createElement("div",{className:`${I}-option-list-outer-bottom-slot`,onMouseEnter:()=>this.foundation.handleSlotMouseEnter()},h):null)}renderSingleSelection(e,t){let{renderSelectedItem:n,searchPosition:s}=this.props;const{placeholder:o}=this.props,{showInput:i,inputValue:l}=this.state;let c="";const u=[...e];if(typeof n>"u"&&(n=y=>y.label),u.length){const y=u[0][1];c=n(y)}const h=s===$e.SEARCH_POSITION_TRIGGER,f=E({[`${I}-selection-text`]:!0,[`${I}-selection-placeholder`]:!c&&c!==0,[`${I}-selection-text-hide`]:l&&i&&h,[`${I}-selection-text-inactive`]:!l&&i&&h}),m=`${I}-content-wrapper`;return d.createElement(d.Fragment,null,d.createElement("div",{className:m},d.createElement("span",{className:f,"x-semi-prop":"placeholder"},c||c===0?c:o),t&&i&&h?this.renderTriggerInput():null))}renderTag(e,t,n){const{size:s,disabled:o}=this.props;let{renderSelectedItem:i}=this.props;const l=e[0],{value:c}=e[1],u=e[1].disabled||o,h=(p,g)=>{g&&typeof g.preventDefault=="function"&&g.preventDefault(),this.foundation.removeTag({label:l,value:c})};typeof i>"u"&&(i=p=>({isRenderInTag:!0,content:p.label}));const{content:f,isRenderInTag:m}=i(e[1],{index:t,disabled:u,onClose:h}),y={disabled:u,closable:!u,onClose:h},v=n&&!fe(this.props.renderSelectedItem)?d.createElement(zr,{size:"small",ellipsis:{rows:1,showTooltip:{type:"popover",opts:{style:{width:"auto",fontSize:12}}}}},f):f;return m?d.createElement(Pe,Object.assign({},y,{color:"white",size:s||"large",key:c,style:{maxWidth:"100%"}}),v):d.createElement($.Fragment,{key:c},v)}renderNTag(e,t){const{size:n,showRestTagsPopover:s,restTagsPopoverProps:o}=this.props;let i=d.createElement(Pe,{closable:!1,size:n||"large",color:"grey",className:`${I}-content-wrapper-collapse-tag`,key:`_+${e}`,style:{marginRight:0,flexShrink:0}},"+",e);return s&&(i=d.createElement(xe,Object.assign({showArrow:!0,content:d.createElement(qr,{spacing:2,wrap:!0,style:{maxWidth:"400px"}},t.map((l,c)=>this.renderTag(l,c))),trigger:"hover",position:"top",autoAdjustOverflow:!0},o,{key:`_+${e}_Popover`}),i)),i}renderOverflow(e,t){return e.length&&e[0]?this.renderTag(e[0],t,!0):null}handleOverflow(e){const{overflowItemCount:t,selections:n}=this.state,{maxTagCount:s}=this.props,o=n.size-s>0?n.size-s+e.length-1:e.length-1;t!==o&&this.foundation.updateOverflowItemCount(n.size,o)}renderCollapsedTags(e,t){const{overflowItemCount:n}=this.state,s=typeof t=="number"?e.slice(0,t):e;return d.createElement("div",{className:`${I}-content-wrapper-collapse`},d.createElement(lt,{items:s,key:String(e.length),overflowRenderer:o=>this.renderOverflow(o,t-1),onOverflow:o=>this.handleOverflow(o),visibleItemRenderer:(o,i)=>this.renderTag(o,i)}),n>0&&this.renderNTag(n,e.slice(e.length-n)))}renderOneLineTags(e,t){let{renderSelectedItem:n}=this.props;const{showRestTagsPopover:s,restTagsPopoverProps:o,maxTagCount:i}=this.props,{isFullTags:l}=this.state;let c;if(typeof n>"u"&&(n=u=>({isRenderInTag:!0,content:u.label})),s){const h=(l?e:e.slice(0,i)).map((f,m)=>this.getTagItem(f,m,n));c=d.createElement(hn,{tagList:h,maxTagCount:t,restCount:l?void 0:e.length-i,size:"large",mode:"custom",showPopover:s,popoverProps:o,onPlusNMouseEnter:()=>{this.foundation.updateIsFullTags()}})}else{const h=e.slice(0,i).map((f,m)=>this.getTagItem(f,m,n));c=d.createElement(hn,{tagList:h,maxTagCount:t,restCount:e.length-i,size:"large",mode:"custom"})}return c}renderMultipleSelection(e,t){let{renderSelectedItem:n,searchPosition:s}=this.props;const{placeholder:o,maxTagCount:i,expandRestTagsOnClick:l,ellipsisTrigger:c}=this.props,{inputValue:u,isOpen:h}=this.state,f=[...e];typeof n>"u"&&(n=T=>({isRenderInTag:!0,content:T.label}));const m=E({[`${I}-content-wrapper`]:!0,[`${I}-content-wrapper-one-line`]:i&&!h,[`${I}-content-wrapper-empty`]:!f.length}),y=E({[`${I}-selection-text`]:!0,[`${I}-selection-placeholder`]:!f.length,[`${I}-selection-text-hide`]:f&&f.length}),v=o&&!u?d.createElement("span",{className:y},o):null,p=f.length>i?i:void 0,g=!i,b=c?this.renderCollapsedTags(f,p):this.renderOneLineTags(f,p),_=g||l&&h?f.map((T,C)=>this.renderTag(T,C)):b,O=t&&s===$e.SEARCH_POSITION_TRIGGER;return d.createElement(d.Fragment,null,d.createElement("div",{className:m},f&&f.length?_:v,O?this.renderTriggerInput():null))}onMouseEnter(e){this.foundation.handleMouseEnter(e)}onMouseLeave(e){this.foundation.handleMouseLeave(e)}onKeyPress(e){this.foundation.handleKeyPress(e)}handlePopoverVisibleChange(e){const{virtualize:t}=this.props,{selections:n}=this.state;if(e)if(t){let s=-1;if(n.forEach(o=>{const i=R(o,"_scrollIndex");Ve(i)&&i>=0&&(s=s!==-1&&s<i?s:i)}),s!==-1)try{this.virtualizeListRef.current.scrollToItem(s,"center")}catch{}}else this.foundation.updateScrollTop()}renderSuffix(){const{suffix:e}=this.props,t=E({[`${I}-suffix`]:!0,[`${I}-suffix-text`]:e&&ee(e),[`${I}-suffix-icon`]:Me(e)});return d.createElement("div",{className:t,"x-semi-prop":"suffix"},e)}renderPrefix(){const{prefix:e,insetLabel:t,insetLabelId:n}=this.props,s=e||t,o=E({[`${I}-prefix`]:!0,[`${I}-inset-label`]:t,[`${I}-prefix-text`]:s&&ee(s),[`${I}-prefix-icon`]:Me(s)});return d.createElement("div",{className:o,id:n,"x-semi-prop":"prefix,insetLabel"},s)}renderSelection(){const e=this.props,{disabled:t,multiple:n,filter:s,style:o,id:i,size:l,className:c,validateStatus:u,showArrow:h,suffix:f,prefix:m,insetLabel:y,placeholder:v,triggerRender:p,arrowIcon:g,clearIcon:b,borderless:_}=e,O=k1(e,["disabled","multiple","filter","style","id","size","className","validateStatus","showArrow","suffix","prefix","insetLabel","placeholder","triggerRender","arrowIcon","clearIcon","borderless"]),{selections:T,isOpen:C,keyboardEventSet:S,inputValue:w,isHovering:P,isFocus:x,showInput:M,focusIndex:j}=this.state,B=typeof p=="function",K=!!s,Z=B?E(c):E(I,c,{[`${I}-borderless`]:_,[`${I}-open`]:C,[`${I}-focus`]:x,[`${I}-disabled`]:t,[`${I}-single`]:!n,[`${I}-multiple`]:n,[`${I}-filterable`]:K,[`${I}-small`]:l==="small",[`${I}-large`]:l==="large",[`${I}-error`]:u==="error",[`${I}-warning`]:u==="warning",[`${I}-no-arrow`]:!h,[`${I}-with-prefix`]:m||y,[`${I}-with-suffix`]:f}),q=this.props.showClear&&(T.size||w)&&!t&&(P||C),se=h?d.createElement("div",{className:`${I}-arrow`,"x-semi-prop":"arrowIcon"},g):d.createElement("div",{className:`${I}-arrow-empty`}),J=b||d.createElement(xi,null),V=B?d.createElement(Ri,{value:Array.from(T.values()),inputValue:w,onChange:this.handleInputChange,onSearch:this.handleInputChange,onRemove:G=>this.foundation.removeTag(G),onClear:this.onClear,disabled:t,triggerRender:p,placeholder:v,componentName:"Select",componentProps:Object.assign({},this.props)}):[d.createElement($.Fragment,{key:"prefix"},m||y?this.renderPrefix():null),d.createElement($.Fragment,{key:"selection"},d.createElement("div",{className:E(`${I}-selection`)},n?this.renderMultipleSelection(T,K):this.renderSingleSelection(T,K))),d.createElement($.Fragment,{key:"suffix"},f?this.renderSuffix():null),d.createElement($.Fragment,{key:"clearicon"},q?d.createElement("div",{className:E(`${I}-clear`),onClick:this.onClear},J):se)],U=t||K&&M||K&&n?-1:0;return d.createElement("div",Object.assign({role:"combobox","aria-disabled":t,"aria-expanded":C,"aria-controls":`${I}-${this.selectOptionListID}`,"aria-haspopup":"listbox","aria-label":T.size?"selected":"","aria-invalid":this.props["aria-invalid"],"aria-errormessage":this.props["aria-errormessage"],"aria-labelledby":this.props["aria-labelledby"],"aria-describedby":this.props["aria-describedby"],"aria-required":this.props["aria-required"],className:Z,ref:G=>this.triggerRef.current=G,onClick:G=>this.foundation.handleClick(G),style:o,id:this.selectID,tabIndex:U,"aria-activedescendant":j!==-1?`${this.selectID}-option-${j}`:"",onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onFocus:G=>this.foundation.handleTriggerFocus(G),onBlur:G=>this.foundation.handleTriggerBlur(G),onKeyPress:this.onKeyPress},S,this.getDataAttr(O)),V)}render(){const{direction:e}=this.context,t=e==="rtl"?"bottomRight":"bottomLeft",{children:n,position:s=t,zIndex:o,getPopupContainer:i,motion:l,autoAdjustOverflow:c,mouseLeaveDelay:u,mouseEnterDelay:h,spacing:f,stopPropagation:m,dropdownMargin:y}=this.props,{isOpen:v,optionKey:p}=this.state,g=this.renderSelection();return d.createElement(xe,{getPopupContainer:i,motion:l,margin:y,autoAdjustOverflow:c,mouseLeaveDelay:u,mouseEnterDelay:h,zIndex:o,ref:this.optionsRef,content:()=>this.renderOptions(n),visible:v,trigger:"custom",rePosKey:p,position:s,spacing:f,stopPropagation:m,disableArrowKeyDown:!0,onVisibleChange:b=>this.handlePopoverVisibleChange(b),afterClose:()=>this.foundation.handlePopoverClose()},g)}}Ye.contextType=kt;Ye.Option=Ge;Ye.OptGroup=An;Ye.propTypes={"aria-describedby":a.string,"aria-errormessage":a.string,"aria-invalid":a.bool,"aria-labelledby":a.string,"aria-required":a.bool,autoFocus:a.bool,autoClearSearchValue:a.bool,borderless:a.bool,children:a.node,clearIcon:a.node,defaultValue:a.oneOfType([a.string,a.number,a.array,a.object]),ellipsisTrigger:a.bool,value:a.oneOfType([a.string,a.number,a.array,a.object]),placeholder:a.node,onChange:a.func,multiple:a.bool,filter:a.oneOfType([a.func,a.bool]),max:a.number,maxTagCount:a.number,maxHeight:a.oneOfType([a.string,a.number]),style:a.object,className:a.string,size:a.oneOf($e.SIZE_SET),disabled:a.bool,emptyContent:a.node,expandRestTagsOnClick:a.bool,onDropdownVisibleChange:a.func,zIndex:a.number,position:a.oneOf($e.POSITION_SET),onSearch:a.func,getPopupContainer:a.func,dropdownClassName:a.string,dropdownStyle:a.object,dropdownMargin:a.oneOfType([a.number,a.object]),outerTopSlot:a.node,innerTopSlot:a.node,inputProps:a.object,outerBottomSlot:a.node,innerBottomSlot:a.node,optionList:a.array,dropdownMatchSelectWidth:a.bool,loading:a.bool,defaultOpen:a.bool,validateStatus:a.oneOf($e.STATUS),defaultActiveFirstOption:a.bool,triggerRender:a.func,stopPropagation:a.bool,searchPosition:a.string,motion:a.bool,onChangeWithObject:a.bool,suffix:a.node,prefix:a.node,insetLabel:a.node,insetLabelId:a.string,showClear:a.bool,showArrow:a.bool,renderSelectedItem:a.func,allowCreate:a.bool,renderCreateItem:a.func,onMouseEnter:a.func,onMouseLeave:a.func,clickToHide:a.bool,onExceed:a.func,onCreate:a.func,remote:a.bool,onDeselect:a.func,onSelect:a.func,autoAdjustOverflow:a.bool,mouseEnterDelay:a.number,mouseLeaveDelay:a.number,spacing:a.oneOfType([a.number,a.object]),onBlur:a.func,onFocus:a.func,onClear:a.func,virtualize:a.object,renderOptionItem:a.func,onListScroll:a.func,arrowIcon:a.node,preventScroll:a.bool};Ye.__SemiComponentName__="Select";Ye.defaultProps=Le(Ye.__SemiComponentName__,{stopPropagation:!0,motion:!0,borderless:!1,zIndex:Xe.DEFAULT_Z_INDEX,filter:!1,multiple:!1,disabled:!1,defaultOpen:!1,allowCreate:!1,placeholder:"",onDropdownVisibleChange:A,onChangeWithObject:!1,onChange:A,onSearch:A,onMouseEnter:A,onMouseLeave:A,onDeselect:A,onSelect:A,onCreate:A,onExceed:A,onFocus:A,onBlur:A,onClear:A,onListScroll:A,maxHeight:Qi.LIST_HEIGHT,dropdownMatchSelectWidth:!0,defaultActiveFirstOption:!0,showArrow:!0,showClear:!1,searchPosition:$e.SEARCH_POSITION_TRIGGER,remote:!1,autoAdjustOverflow:!0,autoClearSearchValue:!0,arrowIcon:d.createElement(Ii,{"aria-label":""}),showRestTagsPopover:!1,restTagsPopoverProps:{},expandRestTagsOnClick:!1,ellipsisTrigger:!1});const N={TABS:`${D}-tabs`,TABS_BAR:`${D}-tabs-bar`,TABS_BAR_LINE:`${D}-tabs-bar-line`,TABS_BAR_CARD:`${D}-tabs-bar-card`,TABS_BAR_BUTTON:`${D}-tabs-bar-button`,TABS_BAR_SLASH:`${D}-tabs-bar-slash`,TABS_BAR_EXTRA:`${D}-tabs-bar-extra`,TABS_TAB:`${D}-tabs-tab`,TABS_TAB_ACTIVE:`${D}-tabs-tab-active`,TABS_TAB_DISABLED:`${D}-tabs-tab-disabled`,TABS_CONTENT:`${D}-tabs-content`,TABS_PANE:`${D}-tabs-pane`,TABS_PANE_INACTIVE:`${D}-tabs-pane-inactive`,TABS_PANE_ACTIVE:`${D}-tabs-pane-active`,TABS_PANE_MOTION_OVERLAY:`${D}-tabs-pane-motion-overlay`,TABS_PANE_ANIMATE_LEFT_SHOW:`${D}-tabs-pane-animate-leftShow`,TABS_PANE_ANIMATE_RIGHT_SHOW:`${D}-tabs-pane-animate-rightShow`,TABS_PANE_ANIMATE_TOP_SHOW:`${D}-tabs-pane-animate-topShow`,TABS_PANE_ANIMATE_BOTTOM_SHOW:`${D}-tabs-pane-animate-bottomShow`},st={TYPE_MAP:["line","card","button","slash"],SIZE:["small","medium","large"],POSITION_MAP:["top","left"]};class B1 extends ye{constructor(e){super(Object.assign({},e)),this.destroy=A,this.handlePrevent=t=>{t.stopPropagation(),t.preventDefault()},this.handleKeyDown=(t,n,s)=>{const{preventScroll:o}=this.getProps(),i=[...t.target.parentNode.childNodes].filter(l=>R(l,"attributes.data-tabkey.value","").includes("semiTab")&&R(l,"attributes.aria-disabled.value","")!=="true");switch(t.key){case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":this.determineOrientation(t,i);break;case"Backspace":case"Delete":this.handleDeleteKeyDown(t,i,n,s);break;case"Enter":case" ":this.handleTabClick(n,t),this.handlePrevent(t);break;case"Home":i[0].focus({preventScroll:o}),this.handlePrevent(t);break;case"End":i[i.length-1].focus({preventScroll:o}),this.handlePrevent(t);break}}}init(){this._adapter.collectPane()}_notifyChange(e){const{activeKey:t}=this.getStates();t!==e&&this._adapter.notifyChange(e)}handleTabClick(e,t){this._isInProps("activeKey")?this._notifyChange(e):(this._notifyChange(e),this.handleNewActiveKey(e)),this._adapter.notifyTabClick(e,t)}handleNewActiveKey(e){const{activeKey:t}=this.getStates();t!==e&&this._adapter.setNewActiveKey(e)}getDefaultActiveKey(){let e;const t=this.getProps();return"activeKey"in t?e=t.activeKey:"defaultActiveKey"in t?e=t.defaultActiveKey:e=this._adapter.getDefaultActiveKeyFromChildren(),e}handleTabListChange(){this._adapter.collectPane()}handleTabPanesChange(){this._adapter.collectPane(),this._adapter.collectActiveKey()}handleTabDelete(e){this._adapter.notifyTabDelete(e)}determineOrientation(e,t){const{tabPosition:n}=this.getProps();n==="left"?(e.key==="ArrowUp"||e.key==="ArrowDown")&&(this.switchTabOnArrowPress(e,t),this.handlePrevent(e)):(e.key==="ArrowLeft"||e.key==="ArrowRight")&&(this.switchTabOnArrowPress(e,t),this.handlePrevent(e))}handleDeleteKeyDown(e,t,n,s){const{preventScroll:o}=this.getProps();if(s){this.handleTabDelete(n);const i=t.indexOf(e.target);t.length!==1&&t[i+1>=t.length?i-1:i+1].focus({preventScroll:o})}}switchTabOnArrowPress(e,t){const{preventScroll:n}=this.getProps(),s=t.indexOf(e.target),o={ArrowLeft:-1,ArrowUp:-1,ArrowRight:1,ArrowDown:1};o[e.key]&&s!==void 0&&(t[s+o[e.key]]?t[s+o[e.key]].focus({preventScroll:n}):e.key==="ArrowLeft"||e.key==="ArrowUp"?t[t.length-1].focus({preventScroll:n}):(e.key==="ArrowRight"||e.key=="ArrowDown")&&t[0].focus({preventScroll:n}))}}var K1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const W1=(r,e)=>{const{tab:t,size:n,type:s,icon:o,selected:i,closable:l,disabled:c,itemKey:u,deleteTabItem:h,tabPosition:f,handleKeyDown:m,onClick:y}=r,v=K1(r,["tab","size","type","icon","selected","closable","disabled","itemKey","deleteTabItem","tabPosition","handleKeyDown","onClick"]),p=$.useMemo(()=>l?d.createElement(Ai,{"aria-label":"Close",role:"button",className:`${N.TABS_TAB}-icon-close`,onClick:C=>h(u,C)}):null,[s,l,h,u]),g=$.useCallback(C=>d.createElement("span",{className:`${N.TABS_BAR}-icon`},C),[]),b=$.useCallback(C=>{m&&m(C,u,l)},[m,u,l]),_=$.useCallback(C=>{!c&&y&&y(u,C)},[u,c,y]),O=o?g(o):null,T=E(N.TABS_TAB,`${N.TABS_TAB}-${s}`,`${N.TABS_TAB}-${f}`,`${N.TABS_TAB}-single`,{[N.TABS_TAB_ACTIVE]:i,[N.TABS_TAB_DISABLED]:c,[`${N.TABS_TAB}-small`]:n==="small",[`${N.TABS_TAB}-medium`]:n==="medium"});return d.createElement("div",Object.assign({role:"tab",id:`semiTab${u}`,"data-tabkey":`semiTab${u}`,"aria-controls":`semiTabPanel${u}`,"aria-disabled":c?"true":"false","aria-selected":i?"true":"false",tabIndex:i?0:-1,onKeyDown:b,onClick:_,className:T},v,{ref:e}),O,t,p)},rs=$.forwardRef(W1);rs.elementType="Tabs.TabItem";var z1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class mr extends d.Component{constructor(e){var t;super(e),t=this,this.handleItemClick=(n,s)=>{this.props.onTabClick(n,s)},this.handleKeyDown=(n,s,o)=>{this.props.handleKeyDown(n,s,o)},this.renderTabItem=n=>{const{size:s,type:o,deleteTabItem:i,handleKeyDown:l,tabPosition:c}=this.props,u=this._isActive(n.itemKey);return d.createElement(rs,Object.assign({},fr(n,["disabled","icon","itemKey","tab","closable"]),{key:this._getBarItemKeyByItemKey(n.itemKey),selected:u,size:s,type:o,tabPosition:c,handleKeyDown:l,deleteTabItem:i,onClick:this.handleItemClick}))},this.scrollTabItemIntoViewByKey=function(n){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"nearest";const o=document.querySelector(`[data-uuid="${t.state.uuid}"] .${N.TABS_TAB}[data-scrollkey="${n}"]`);o==null||o.scrollIntoView({behavior:"smooth",block:s,inline:s})},this.scrollActiveTabItemIntoView=n=>{const s=this._getBarItemKeyByItemKey(this.props.activeKey);this.scrollTabItemIntoViewByKey(s,n)},this.renderTabComponents=n=>n.map(s=>this.renderTabItem(s)),this.handleArrowClick=(n,s)=>{const o=s==="start"?n.pop():n.shift();if(!o)return;const i=this._getBarItemKeyByItemKey(o.itemKey);this.scrollTabItemIntoViewByKey(i)},this.renderCollapse=(n,s,o)=>{var i;const l=E({[`${N.TABS_BAR}-arrow-${o}`]:o,[`${N.TABS_BAR}-arrow`]:!0});if(Se(n))return d.createElement("div",{role:"presentation",className:l},d.createElement(qe,{disabled:!0,icon:s,theme:"borderless"}));const{dropdownClassName:c,dropdownStyle:u,showRestInDropdown:h,dropdownProps:f}=this.props,{rePosKey:m}=this.state,y=!n.length,v=d.createElement(te.Menu,null,n.map(_=>{const{icon:O,tab:T,itemKey:C}=_,S=O?this.renderIcon(_.icon):null;return d.createElement(te.Item,{key:C,onClick:w=>this.handleItemClick(C,w),active:this._isActive(C)},S,T)})),p=d.createElement("div",{role:"presentation",className:l,onClick:_=>this.handleArrowClick(n,o)},d.createElement(qe,{disabled:y,icon:s,theme:"borderless"})),g=E(c,{[`${N.TABS_BAR}-dropdown`]:!0}),b=(i=f==null?void 0:f[o])!==null&&i!==void 0?i:{};return d.createElement(d.Fragment,null,h?d.createElement(te,Object.assign({className:g,clickToHide:!0,clickTriggerToHide:!0,key:`${m}-${o}`,position:o==="start"?"bottomLeft":"bottomRight",render:y?null:v,showTick:!0,style:u,trigger:"hover",disableFocusListener:!0},b),p):p)},this.renderOverflow=n=>n.map((s,o)=>{const i=o===0?"start":"end",l=o===0?d.createElement(Yw,null):d.createElement(Jw,null),c=this.renderCollapse(s,l,i);return this.props.renderArrow?this.props.renderArrow(s,i,()=>this.handleArrowClick(s,i),c):c}),this.renderCollapsedTab=()=>{const{list:n}=this.props,s=n.map(o=>{const{itemKey:i}=o;return Object.assign({key:this._getBarItemKeyByItemKey(i),active:this._isActive(i)},o)});return d.createElement(lt,{items:s,overflowRenderDirection:this.props.arrowPosition,wrapperStyle:this.props.visibleTabsStyle,overflowRenderer:this.renderOverflow,renderMode:"scroll",className:`${N.TABS_BAR}-overflow-list`,visibleItemRenderer:this.renderTabItem,onVisibleStateChange:o=>{var i,l;const c=new Map;o.forEach((u,h)=>{c.set(this._getItemKeyByBarItemKey(h),u)}),(l=(i=this.props).onVisibleTabsChange)===null||l===void 0||l.call(i,c)}})},this.renderWithMoreTrigger=()=>{const{list:n,more:s}=this.props;let o=[],i=d.createElement("div",{className:E({[`${N.TABS_BAR}-more-trigger`]:!0,[`${N.TABS_BAR}-more-trigger-${this.props.type}`]:!0})},d.createElement(De,{componentName:"Tabs"},(c,u)=>d.createElement("div",{className:`${N.TABS_BAR}-more-trigger-content`},d.createElement("div",null,c.more),d.createElement(Ii,{className:`${N.TABS_BAR}-more-trigger-content-icon`})))),l;if(typeof s=="number")l=n.length-Math.min(s,n.length),o=n.slice(0,l).map(c=>this.renderTabItem(c));else if(typeof s=="object")l=n.length-Math.min(s.count,n.length),o=n.slice(0,l).map(c=>this.renderTabItem(c)),s.render&&(i=s.render());else if(s!==void 0)throw new Error("[Semi Tabs]: invalid tab props format: more");return d.createElement(d.Fragment,null,o,this.renderMoreDropdown(n.slice(l),s==null?void 0:s.dropdownProps,i))},this.renderMoreDropdown=(n,s,o)=>d.createElement(te,Object.assign({trigger:"hover",showTick:!0,position:"bottomLeft",className:`${N.TABS_BAR}-more-dropdown-${this.props.type}`,clickToHide:!0,menu:n.map(i=>({node:"item",name:i.tab,icon:i.icon,onClick:l=>this.props.onTabClick(i.itemKey,l),active:this.props.activeKey===i.itemKey}))},s),o),this._isActive=n=>n===this.props.activeKey,this._getBarItemKeyByItemKey=n=>`${n}-bar`,this._getItemKeyByBarItemKey=n=>n.replace(/-bar$/,""),this.state={endInd:e.list.length,rePosKey:0,startInd:0,uuid:"",currentVisibleItems:[]}}componentDidMount(){this.setState({uuid:RS()})}componentDidUpdate(e){e.activeKey!==this.props.activeKey&&this.props.collapsible&&this.scrollActiveTabItemIntoView()}renderIcon(e){return d.createElement("span",null,e)}renderExtra(){const{tabBarExtraContent:e,type:t,size:n}=this.props,s={float:"right"},o=e&&e.props?e.props.style:{},i=E(N.TABS_BAR_EXTRA,{[`${N.TABS_BAR}-${t}-extra`]:t,[`${N.TABS_BAR}-${t}-extra-${n}`]:n});if(e){const l=Object.assign(Object.assign({},s),o);return d.createElement("div",{className:i,style:l,"x-semi-prop":"tabBarExtraContent"},e)}return null}render(){const e=this.props,{type:t,style:n,className:s,list:o,tabPosition:i,more:l,collapsible:c}=e,u=z1(e,["type","style","className","list","tabPosition","more","collapsible"]),h=E(s,{[N.TABS_BAR]:!0,[N.TABS_BAR_LINE]:t==="line",[N.TABS_BAR_CARD]:t==="card",[N.TABS_BAR_BUTTON]:t==="button",[N.TABS_BAR_SLASH]:t==="slash",[`${N.TABS_BAR}-${i}`]:i,[`${N.TABS_BAR}-collapse`]:c}),f=this.renderExtra(),m=c?this.renderCollapsedTab():l?this.renderWithMoreTrigger():this.renderTabComponents(o);return d.createElement("div",Object.assign({role:"tablist","aria-orientation":i==="left"?"vertical":"horizontal",className:h,style:n},xt(u),{"data-uuid":this.state.uuid}),m,f)}}mr.propTypes={activeKey:a.string,className:a.string,collapsible:a.bool,list:a.array,onTabClick:a.func,size:a.oneOf(st.SIZE),style:a.object,tabBarExtraContent:a.node,tabPosition:a.oneOf(st.POSITION_MAP),type:a.oneOf(st.TYPE_MAP),closable:a.bool,deleteTabItem:a.func,more:a.oneOfType([a.number,a.object])};const ta=d.createContext({});var H1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};class Nn extends $.PureComponent{constructor(){super(...arguments),this.ref=$.createRef(),this.getDirection=(e,t,n,s)=>{if(t!==null&&e!==null&&Array.isArray(n)&&n.length){const o=n.findIndex(c=>c.itemKey===e),i=n.findIndex(c=>c.itemKey===t),l=n.findIndex(c=>c.itemKey===s);return o===i?l>o:i<o}return!1},this.shouldRender=()=>{const{itemKey:e}=this.props,{activeKey:t,lazyRender:n}=this.context,s=t===e;return this._active=this._active||s,n?this._active:!0}}render(){const{tabPaneMotion:e,tabPosition:t,prevActiveKey:n}=this.context,s=this.props,{className:o,style:i,children:l,itemKey:c,tabIndex:u}=s,h=H1(s,["className","style","children","itemKey","tabIndex"]),f=this.context.activeKey===c,m=E(o,{[N.TABS_PANE_INACTIVE]:!f,[N.TABS_PANE_ACTIVE]:f,[N.TABS_PANE]:!0}),y=this.shouldRender(),v=(()=>{const b=this.getDirection(this.context.activeKey,c,this.context.panes,n);return t==="top"?b?N.TABS_PANE_ANIMATE_RIGHT_SHOW:N.TABS_PANE_ANIMATE_LEFT_SHOW:b?N.TABS_PANE_ANIMATE_BOTTOM_SHOW:N.TABS_PANE_ANIMATE_TOP_SHOW})(),p=!this.context.panes.find(b=>b.itemKey===n),g=e&&f&&!p&&!this.context.forceDisableMotion;return d.createElement("div",Object.assign({ref:this.ref,role:"tabpanel",id:`semiTabPanel${c}`,"aria-labelledby":`semiTab${c}`,className:m,style:i,"aria-hidden":f?"false":"true",tabIndex:u||0},xt(h),{"x-semi-prop":"children"}),d.createElement(Rr,{motion:g,animationState:f?"enter":"leave",startClassName:v},b=>{let{animationClassName:_,animationEventsNeedBind:O}=b;return d.createElement("div",Object.assign({className:E(N.TABS_PANE_MOTION_OVERLAY,_),"x-semi-prop":"children"},O),y?l:null)}))}}Nn.isTabPane=!0;Nn.contextType=ta;Nn.propTypes={className:a.string,style:a.object,children:a.node,disabled:a.bool,itemKey:a.string,tab:a.node,icon:a.node,closable:a.bool};var V1=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(r);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(r,n[s])&&(t[n[s]]=r[n[s]]);return t};const xo=["className","style","disabled","itemKey","tab","icon"];class ct extends ce{constructor(e){super(e),this.setContentRef=t=>{this.contentRef={current:t}},this.getPanes=()=>{const{tabList:t,children:n}=this.props;return Array.isArray(t)&&t.length?t:d.Children.map(n,s=>{if(s){const{tab:o,icon:i,disabled:l,itemKey:c,closable:u}=s.props;return{tab:o,icon:i,disabled:l,itemKey:c,closable:u}}})},this.onTabClick=(t,n)=>{this.foundation.handleTabClick(t,n)},this.rePosChildren=(t,n)=>{const s=[],o=d.Children.toArray(t);return t.length&&(s.push(...o.filter(i=>i.props&&i.props.itemKey===n)),s.push(...o.filter(i=>i.props&&i.props.itemKey!==n))),s},this.getActiveItem=()=>{const{activeKey:t}=this.state,{children:n,tabList:s}=this.props;return s||!Array.isArray(n)?n:d.Children.toArray(n).filter(o=>$.isValidElement(o)&&o.type&&o.type.isTabPane?o.props.itemKey===t:!0)},this.deleteTabItem=(t,n)=>{n.stopPropagation(),this.foundation.handleTabDelete(t)},this.foundation=new B1(this.adapter),this.state={activeKey:this.foundation.getDefaultActiveKey(),panes:this.getPanes(),prevActiveKey:null,forceDisableMotion:!1},this.contentRef=$.createRef(),this.contentHeight="auto"}get adapter(){return Object.assign(Object.assign({},super.adapter),{collectPane:()=>{const e=this.getPanes();this.setState({panes:e})},collectActiveKey:()=>{const{tabList:e,children:t,activeKey:n}=this.props;if(typeof n<"u")return;const{activeKey:s}=this.state,o=this.getPanes();o.findIndex(i=>i.itemKey===s)===-1&&(o.length>0?this.setState({activeKey:o[0].itemKey}):this.setState({activeKey:""}))},notifyTabClick:(e,t)=>{this.props.onTabClick(e,t)},notifyChange:e=>{this.props.onChange(e)},setNewActiveKey:e=>{this.setState({activeKey:e})},getDefaultActiveKeyFromChildren:()=>{const{tabList:e,children:t}=this.props;let n="";return(e||d.Children.toArray(t).map(o=>$.isValidElement(o)?o.props:null)).forEach(o=>{o&&!n&&!o.disabled&&(n=o.itemKey)}),n},notifyTabDelete:e=>{this.props.onTabClose&&this.props.onTabClose(e)}})}static getDerivedStateFromProps(e,t){const n={};return!Rt(e.activeKey)&&e.activeKey!==t.activeKey&&(t.prevActiveKey=t.activeKey,n.activeKey=e.activeKey),n}componentDidUpdate(e,t){const n=d.Children.toArray(e.children).map(i=>fr($.isValidElement(i)?i.props:null,xo)),s=d.Children.toArray(this.props.children).map(i=>fr($.isValidElement(i)?i.props:null,xo)),o=this.props.tabList||e.tabList;if(de(this.props.tabList,e.tabList)||this.foundation.handleTabListChange(),t.activeKey!==this.state.activeKey&&t.activeKey!==this.state.prevActiveKey&&this.setState({prevActiveKey:t.activeKey}),e.activeKey!==this.props.activeKey){const i=(()=>{const l=new Set(n.map(c=>c.itemKey));return s.map(c=>c.itemKey).filter(c=>!l.has(c))})();this.setState({forceDisableMotion:i.includes(this.props.activeKey)})}!de(n,s)&&!o&&this.foundation.handleTabPanesChange()}render(){const e=this.props,{children:t,className:n,collapsible:s,contentStyle:o,keepDOM:i,lazyRender:l,renderTabBar:c,showRestInDropdown:u,size:h,style:f,tabBarClassName:m,tabBarExtraContent:y,tabBarStyle:v,tabPaneMotion:p,tabPosition:g,type:b,more:_,onVisibleTabsChange:O,visibleTabsStyle:T,arrowPosition:C,renderArrow:S,dropdownProps:w}=e,P=V1(e,["children","className","collapsible","contentStyle","keepDOM","lazyRender","renderTabBar","showRestInDropdown","size","style","tabBarClassName","tabBarExtraContent","tabBarStyle","tabPaneMotion","tabPosition","type","more","onVisibleTabsChange","visibleTabsStyle","arrowPosition","renderArrow","dropdownProps"]),{panes:x,activeKey:M}=this.state,j=E(n,{[N.TABS]:!0,[`${N.TABS}-${g}`]:g}),B=E({[N.TABS_CONTENT]:!0,[`${N.TABS_CONTENT}-${g}`]:g}),K={activeKey:M,className:m,collapsible:s,list:x,onTabClick:this.onTabClick,showRestInDropdown:u,size:h,style:v,tabBarExtraContent:y,tabPosition:g,type:b,deleteTabItem:this.deleteTabItem,handleKeyDown:this.foundation.handleKeyDown,more:_,onVisibleTabsChange:O,visibleTabsStyle:T,arrowPosition:C,renderArrow:S,dropdownProps:w},Z=c?c(K,mr):d.createElement(mr,Object.assign({},K)),q=i?t:this.getActiveItem();return d.createElement("div",Object.assign({className:j,style:f},this.getDataAttr(P)),Z,d.createElement(ta.Provider,{value:{activeKey:M,lazyRender:l,panes:x,tabPaneMotion:p,tabPosition:g,prevActiveKey:this.state.prevActiveKey,forceDisableMotion:this.state.forceDisableMotion}},d.createElement("div",{ref:this.setContentRef,className:B,style:Object.assign({},o)},q)))}}ct.TabPane=Nn;ct.TabItem=rs;ct.propTypes={activeKey:a.string,className:a.string,collapsible:a.bool,contentStyle:a.oneOfType([a.object]),defaultActiveKey:a.string,keepDOM:a.bool,lazyRender:a.bool,onChange:a.func,onTabClick:a.func,renderTabBar:a.func,showRestInDropdown:a.bool,size:a.oneOf(st.SIZE),style:a.object,tabBarClassName:a.string,tabBarExtraContent:a.node,tabBarStyle:a.object,tabList:a.array,tabPaneMotion:a.bool,tabPosition:a.oneOf(st.POSITION_MAP),type:a.oneOf(st.TYPE_MAP),onTabClose:a.func,preventScroll:a.bool,more:a.oneOfType([a.number,a.object]),arrowPosition:a.string,renderArrow:a.func,dropdownProps:a.object};ct.__SemiComponentName__="Tabs";ct.defaultProps=Le(ct.__SemiComponentName__,{children:[],collapsible:!1,keepDOM:!0,lazyRender:!1,onChange:()=>{},onTabClick:()=>{},size:"large",tabPaneMotion:!0,tabPosition:"top",type:"line",onTabClose:()=>{},showRestInDropdown:!0,arrowPosition:"both"});class na extends $.Component{constructor(e){super(e),this.state={}}render(){const{children:e,locale:t}=this.props;return d.createElement(Pi.Provider,{value:t},e)}}na.propTypes={locale:a.object,children:a.node};na.defaultProps={locale:hr};export{qe as B,go as F,na as L,Ye as S,ct as T,Nn as a,Y1 as b,hr as c,G1 as j,q1 as l};
